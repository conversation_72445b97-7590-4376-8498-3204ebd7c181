# Network IDS Signature Rules
# Các rule phát hiện tấn công mạng

# SSH Brute Force Detection
alert tcp any any -> any 22 (msg:"SSH Brute Force Attack Detected"; flow:to_server,established; content:"SSH"; threshold:type both,track by_src,count 10,seconds 60; classtype:attempted-recon; sid:100001; rev:1;)

# HTTP SQL Injection Attempts
alert tcp any any -> any 80 (msg:"HTTP SQL Injection Attempt"; flow:to_server,established; content:"GET"; http_method; content:"union"; nocase; http_uri; content:"select"; nocase; http_uri; classtype:web-application-attack; sid:100002; rev:1;)

# FTP Anonymous Login
alert tcp any any -> any 21 (msg:"FTP Anonymous Login Attempt"; flow:to_server,established; content:"USER anonymous"; nocase; classtype:attempted-recon; sid:100003; rev:1;)

# Port Scanning Detection
alert tcp any any -> any any (msg:"Port Scan Detected"; flags:S; threshold:type both,track by_src,count 20,seconds 10; classtype:attempted-recon; sid:100004; rev:1;)

# DNS Tunneling Detection
alert udp any any -> any 53 (msg:"Possible DNS Tunneling"; content:"|01 00 00 01 00 00 00 00 00 00|"; depth:10; content:"|00 00 10 00 01|"; distance:0; classtype:policy-violation; sid:100005; rev:1;)

# ICMP Flood Detection
alert icmp any any -> any any (msg:"ICMP Flood Attack"; threshold:type both,track by_src,count 50,seconds 10; classtype:attempted-dos; sid:100006; rev:1;)

# Telnet Login Attempts
alert tcp any any -> any 23 (msg:"Telnet Login Attempt"; flow:to_server,established; content:"login:"; nocase; classtype:attempted-recon; sid:100007; rev:1;)

# HTTP Directory Traversal
alert tcp any any -> any 80 (msg:"HTTP Directory Traversal Attempt"; flow:to_server,established; content:"GET"; http_method; content:"../"; http_uri; classtype:web-application-attack; sid:100008; rev:1;)

# SMTP Spam Detection
alert tcp any any -> any 25 (msg:"SMTP Spam Attempt"; flow:to_server,established; content:"MAIL FROM"; nocase; content:"viagra"; nocase; distance:0; classtype:policy-violation; sid:100009; rev:1;)

# RDP Brute Force
alert tcp any any -> any 3389 (msg:"RDP Brute Force Attack"; flow:to_server,established; threshold:type both,track by_src,count 5,seconds 60; classtype:attempted-recon; sid:100010; rev:1;)

# HTTP Admin Panel Access
alert tcp any any -> any 80 (msg:"HTTP Admin Panel Access Attempt"; flow:to_server,established; content:"GET"; http_method; content:"/admin"; http_uri; classtype:web-application-attack; sid:100011; rev:1;)

# DNS Query for Malicious Domain
alert udp any any -> any 53 (msg:"DNS Query for Malicious Domain"; content:"|01 00 00 01|"; depth:4; content:"malware"; nocase; classtype:trojan-activity; sid:100012; rev:1;)

# HTTP User-Agent Anomaly
alert tcp any any -> any 80 (msg:"Suspicious HTTP User-Agent"; flow:to_server,established; content:"User-Agent: "; http_header; content:"sqlmap"; nocase; classtype:web-application-attack; sid:100013; rev:1;)

# Large HTTP POST Request
alert tcp any any -> any 80 (msg:"Large HTTP POST Request"; flow:to_server,established; content:"POST"; http_method; content:"Content-Length: "; http_header; content:"|32 30 30 30 30|"; http_header; classtype:web-application-attack; sid:100014; rev:1;)

# Suspicious PowerShell Command
alert tcp any any -> any any (msg:"Suspicious PowerShell Command"; flow:to_server,established; content:"powershell"; nocase; content:"-encodedcommand"; nocase; classtype:trojan-activity; sid:100015; rev:1;)

# HTTP PHP Code Injection
alert tcp any any -> any 80 (msg:"HTTP PHP Code Injection"; flow:to_server,established; content:"GET"; http_method; content:"<?php"; nocase; http_uri; classtype:web-application-attack; sid:100016; rev:1;)

# Multiple Failed Login Attempts
alert tcp any any -> any any (msg:"Multiple Failed Login Attempts"; flow:to_server,established; content:"401 Unauthorized"; http_stat_code; threshold:type both,track by_src,count 5,seconds 60; classtype:attempted-recon; sid:100017; rev:1;)

# Suspicious File Download
alert tcp any any -> any 80 (msg:"Suspicious File Download"; flow:to_server,established; content:"GET"; http_method; content:".exe"; http_uri; classtype:policy-violation; sid:100018; rev:1;)

# Network Reconnaissance
alert tcp any any -> any any (msg:"Network Reconnaissance Activity"; flags:F; threshold:type both,track by_src,count 10,seconds 30; classtype:attempted-recon; sid:100019; rev:1;)

# HTTP Buffer Overflow Attempt
alert tcp any any -> any 80 (msg:"HTTP Buffer Overflow Attempt"; flow:to_server,established; content:"GET"; http_method; content:"|90 90 90 90|"; http_uri; classtype:attempted-admin; sid:100020; rev:1;)
