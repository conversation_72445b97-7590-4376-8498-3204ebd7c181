package com.securecloudstorage.gateway.client;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.net.URI;

/**
 * AI Security Client
 * Tích hợp với AI-Malware Detection Python service
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AISecurityClient {

    private final WebClient webClient;
    
    public AISecurityClient() {
        this.webClient = WebClient.builder()
            .baseUrl("http://localhost:8086")
            .build();
    }

    public Mono<AISecurityResult> analyzeThreat(String clientIp, HttpHeaders headers, URI uri) {
        return webClient.post()
            .uri("/api/analyze-threat")
            .bodyValue(AISecurityRequest.builder()
                .clientIp(clientIp)
                .headers(headers.toSingleValueMap())
                .uri(uri.toString())
                .timestamp(System.currentTimeMillis())
                .build())
            .retrieve()
            .bodyToMono(AISecurityResult.class)
            .doOnSuccess(result -> log.debug("AI Security result for IP {}: {}", clientIp, result))
            .doOnError(error -> log.error("AI Security check failed for IP {}: {}", clientIp, error.getMessage()))
            .onErrorReturn(AISecurityResult.builder()
                .malicious(false)
                .threatType("check_failed")
                .confidence(0.0)
                .build());
    }

    public Mono<String> scanFile(String fileId, byte[] fileContent) {
        return webClient.post()
            .uri("/api/scan-file")
            .bodyValue(AIFileScanRequest.builder()
                .fileId(fileId)
                .fileContent(fileContent)
                .timestamp(System.currentTimeMillis())
                .build())
            .retrieve()
            .bodyToMono(String.class)
            .doOnError(error -> log.error("AI file scan failed for file {}: {}", fileId, error.getMessage()));
    }

    public Mono<AISecurityStats> getStats() {
        return webClient.get()
            .uri("/api/stats")
            .retrieve()
            .bodyToMono(AISecurityStats.class)
            .doOnError(error -> log.error("Failed to get AI Security stats: {}", error.getMessage()));
    }
}
