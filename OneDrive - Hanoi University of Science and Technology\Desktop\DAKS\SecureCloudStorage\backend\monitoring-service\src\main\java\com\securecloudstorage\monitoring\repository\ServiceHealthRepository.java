package com.securecloudstorage.monitoring.repository;

import com.securecloudstorage.monitoring.entity.ServiceHealth;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * ServiceHealth Repository
 * Repository cho ServiceHealth entity
 */
@Repository
public interface ServiceHealthRepository extends JpaRepository<ServiceHealth, UUID>, JpaSpecificationExecutor<ServiceHealth> {

    // Basic queries
    Optional<ServiceHealth> findByHealthCheckId(String healthCheckId);
    List<ServiceHealth> findByServiceName(String serviceName);
    List<ServiceHealth> findByStatus(String status);
    List<ServiceHealth> findByEnvironment(String environment);
    List<ServiceHealth> findByRegion(String region);
    List<ServiceHealth> findByHost(String host);
    List<ServiceHealth> findByInstanceId(String instanceId);
    List<ServiceHealth> findByVersion(String version);
    List<ServiceHealth> findByDeploymentId(String deploymentId);
    List<ServiceHealth> findByActive(Boolean active);
    List<ServiceHealth> findByArchived(Boolean archived);

    // Combined queries
    List<ServiceHealth> findByServiceNameAndStatus(String serviceName, String status);
    List<ServiceHealth> findByServiceNameAndEnvironment(String serviceName, String environment);
    List<ServiceHealth> findByServiceNameAndRegion(String serviceName, String region);
    List<ServiceHealth> findByServiceNameAndHost(String serviceName, String host);
    List<ServiceHealth> findByStatusAndEnvironment(String status, String environment);
    List<ServiceHealth> findByServiceNameAndStatusAndEnvironment(String serviceName, String status, String environment);

    // Health status queries
    List<ServiceHealth> findByStatusAndActiveTrue(String status);
    List<ServiceHealth> findByServiceNameAndStatusAndActiveTrue(String serviceName, String status);
    List<ServiceHealth> findByStatusInAndActiveTrue(List<String> statuses);
    List<ServiceHealth> findByServiceNameAndStatusInAndActiveTrue(String serviceName, List<String> statuses);

    // Health score queries
    List<ServiceHealth> findByHealthScoreGreaterThan(Double healthScore);
    List<ServiceHealth> findByHealthScoreLessThan(Double healthScore);
    List<ServiceHealth> findByHealthScoreBetween(Double minScore, Double maxScore);
    List<ServiceHealth> findByHealthScoreGreaterThanAndServiceName(Double healthScore, String serviceName);
    List<ServiceHealth> findByHealthScoreLessThanAndServiceName(Double healthScore, String serviceName);

    // Response time queries
    List<ServiceHealth> findByResponseTimeGreaterThan(Long responseTime);
    List<ServiceHealth> findByResponseTimeLessThan(Long responseTime);
    List<ServiceHealth> findByResponseTimeBetween(Long minTime, Long maxTime);
    List<ServiceHealth> findByResponseTimeGreaterThanAndServiceName(Long responseTime, String serviceName);
    List<ServiceHealth> findByResponseTimeLessThanAndServiceName(Long responseTime, String serviceName);

    // Error rate queries
    List<ServiceHealth> findByErrorRateGreaterThan(Double errorRate);
    List<ServiceHealth> findByErrorRateLessThan(Double errorRate);
    List<ServiceHealth> findByErrorRateBetween(Double minRate, Double maxRate);
    List<ServiceHealth> findByErrorRateGreaterThanAndServiceName(Double errorRate, String serviceName);
    List<ServiceHealth> findByErrorRateLessThanAndServiceName(Double errorRate, String serviceName);

    // Date range queries
    List<ServiceHealth> findByLastCheckedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<ServiceHealth> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<ServiceHealth> findByUpdatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<ServiceHealth> findByLastSuccessAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<ServiceHealth> findByLastFailureAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    // Date range with filters
    List<ServiceHealth> findByServiceNameAndLastCheckedAtBetween(String serviceName, LocalDateTime startDate, LocalDateTime endDate);
    List<ServiceHealth> findByStatusAndLastCheckedAtBetween(String status, LocalDateTime startDate, LocalDateTime endDate);
    List<ServiceHealth> findByServiceNameAndStatusAndLastCheckedAtBetween(String serviceName, String status, LocalDateTime startDate, LocalDateTime endDate);

    // Failure queries
    List<ServiceHealth> findByConsecutiveFailuresGreaterThan(Integer failures);
    List<ServiceHealth> findByConsecutiveFailuresGreaterThanAndServiceName(Integer failures, String serviceName);
    List<ServiceHealth> findByFailureCountGreaterThan(Long failureCount);
    List<ServiceHealth> findByFailureCountGreaterThanAndServiceName(Long failureCount, String serviceName);

    // Uptime queries
    List<ServiceHealth> findByUptimePercentageGreaterThan(Double uptimePercentage);
    List<ServiceHealth> findByUptimePercentageLessThan(Double uptimePercentage);
    List<ServiceHealth> findByUptimePercentageBetween(Double minUptime, Double maxUptime);
    List<ServiceHealth> findByUptimePercentageGreaterThanAndServiceName(Double uptimePercentage, String serviceName);
    List<ServiceHealth> findByUptimePercentageLessThanAndServiceName(Double uptimePercentage, String serviceName);

    // SLA queries
    List<ServiceHealth> findBySlaComplianceTrue();
    List<ServiceHealth> findBySlaComplianceFalse();
    List<ServiceHealth> findBySlaComplianceTrueAndServiceName(String serviceName);
    List<ServiceHealth> findBySlaComplianceFalseAndServiceName(String serviceName);
    List<ServiceHealth> findByAvailabilitySlaGreaterThan(Double availabilitySla);
    List<ServiceHealth> findByAvailabilitySlaLessThan(Double availabilitySla);

    // Maintenance queries
    List<ServiceHealth> findByMaintenanceModeTrue();
    List<ServiceHealth> findByMaintenanceModeFalse();
    List<ServiceHealth> findByMaintenanceModeAndServiceName(Boolean maintenanceMode, String serviceName);
    List<ServiceHealth> findByMaintenanceWindowStartBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<ServiceHealth> findByMaintenanceWindowEndBetween(LocalDateTime startDate, LocalDateTime endDate);

    // Alert queries
    List<ServiceHealth> findByAlertEnabledTrue();
    List<ServiceHealth> findByAlertEnabledFalse();
    List<ServiceHealth> findByAlertEnabledTrueAndServiceName(String serviceName);
    List<ServiceHealth> findByAlertCountGreaterThan(Integer alertCount);
    List<ServiceHealth> findByAlertCountGreaterThanAndServiceName(Integer alertCount, String serviceName);
    List<ServiceHealth> findByLastAlertAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    // Auto healing queries
    List<ServiceHealth> findByAutoHealingEnabledTrue();
    List<ServiceHealth> findByAutoHealingEnabledFalse();
    List<ServiceHealth> findByAutoHealingEnabledTrueAndServiceName(String serviceName);
    List<ServiceHealth> findByAutoHealingAttemptsGreaterThan(Integer attempts);
    List<ServiceHealth> findByAutoHealingAttemptsGreaterThanAndServiceName(Integer attempts, String serviceName);
    List<ServiceHealth> findByLastAutoHealingAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    // Circuit breaker queries
    List<ServiceHealth> findByCircuitBreakerState(String circuitBreakerState);
    List<ServiceHealth> findByCircuitBreakerStateAndServiceName(String circuitBreakerState, String serviceName);
    List<ServiceHealth> findByCircuitBreakerFailureRateGreaterThan(Double failureRate);
    List<ServiceHealth> findByCircuitBreakerSlowCallRateGreaterThan(Double slowCallRate);

    // Business impact queries
    List<ServiceHealth> findByBusinessImpact(String businessImpact);
    List<ServiceHealth> findByBusinessImpactAndServiceName(String businessImpact, String serviceName);
    List<ServiceHealth> findByBusinessImpactIn(List<String> businessImpacts);
    List<ServiceHealth> findByBusinessImpactInAndActiveTrue(List<String> businessImpacts);

    // Priority queries
    List<ServiceHealth> findByPriority(String priority);
    List<ServiceHealth> findByPriorityAndServiceName(String priority, String serviceName);
    List<ServiceHealth> findByPriorityIn(List<String> priorities);
    List<ServiceHealth> findByPriorityInAndActiveTrue(List<String> priorities);

    // Check type queries
    List<ServiceHealth> findByCheckType(String checkType);
    List<ServiceHealth> findByCheckTypeAndServiceName(String checkType, String serviceName);
    List<ServiceHealth> findByCheckMethod(String checkMethod);
    List<ServiceHealth> findByCheckMethodAndServiceName(String checkMethod, String serviceName);

    // Active service health checks
    List<ServiceHealth> findByActiveTrue();
    List<ServiceHealth> findByActiveTrueAndServiceName(String serviceName);
    List<ServiceHealth> findByActiveTrueAndStatus(String status);
    List<ServiceHealth> findByActiveTrueAndEnvironment(String environment);
    List<ServiceHealth> findByActiveTrueAndServiceNameAndEnvironment(String serviceName, String environment);

    // Custom queries
    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.serviceName = :serviceName ORDER BY sh.lastCheckedAt DESC")
    List<ServiceHealth> findLatestByServiceName(@Param("serviceName") String serviceName);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.status = :status ORDER BY sh.healthScore ASC")
    List<ServiceHealth> findByStatusOrderedByHealthScore(@Param("status") String status);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.healthScore < :threshold ORDER BY sh.healthScore ASC")
    List<ServiceHealth> findUnhealthyServices(@Param("threshold") Double threshold);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.responseTime > :threshold ORDER BY sh.responseTime DESC")
    List<ServiceHealth> findSlowServices(@Param("threshold") Long threshold);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.errorRate > :threshold ORDER BY sh.errorRate DESC")
    List<ServiceHealth> findHighErrorRateServices(@Param("threshold") Double threshold);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.uptimePercentage < :threshold ORDER BY sh.uptimePercentage ASC")
    List<ServiceHealth> findLowUptimeServices(@Param("threshold") Double threshold);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.slaCompliance = false ORDER BY sh.uptimePercentage ASC")
    List<ServiceHealth> findSlaViolatingServices();

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.lastCheckedAt < :threshold")
    List<ServiceHealth> findStaleHealthChecks(@Param("threshold") LocalDateTime threshold);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.consecutiveFailures >= :threshold")
    List<ServiceHealth> findConsecutiveFailureServices(@Param("threshold") Integer threshold);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.autoHealingEnabled = true AND sh.status = 'DOWN' AND sh.autoHealingAttempts < sh.maxAutoHealingAttempts")
    List<ServiceHealth> findAutoHealingCandidates();

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.alertEnabled = true AND sh.consecutiveFailures >= sh.alertThreshold AND sh.lastAlertAt < :threshold")
    List<ServiceHealth> findAlertCandidates(@Param("threshold") LocalDateTime threshold);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.maintenanceMode = true AND sh.maintenanceWindowEnd < :now")
    List<ServiceHealth> findExpiredMaintenanceWindows(@Param("now") LocalDateTime now);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.archived = false AND sh.active = false AND sh.updatedAt < :threshold")
    List<ServiceHealth> findArchivalCandidates(@Param("threshold") LocalDateTime threshold);

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.businessImpact = 'CRITICAL' AND sh.status != 'UP' ORDER BY sh.lastCheckedAt DESC")
    List<ServiceHealth> findCriticalUnhealthyServices();

    @Query("SELECT sh FROM ServiceHealth sh WHERE sh.active = true AND sh.priority IN ('P1', 'P2') AND sh.status != 'UP' ORDER BY sh.priority ASC, sh.lastCheckedAt DESC")
    List<ServiceHealth> findHighPriorityUnhealthyServices();

    // Statistics queries
    @Query("SELECT COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true AND sh.status = :status")
    long countActiveByStatus(@Param("status") String status);

    @Query("SELECT COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true AND sh.serviceName = :serviceName")
    long countActiveByServiceName(@Param("serviceName") String serviceName);

    @Query("SELECT COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true AND sh.environment = :environment")
    long countActiveByEnvironment(@Param("environment") String environment);

    @Query("SELECT COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true AND sh.businessImpact = :businessImpact")
    long countActiveByBusinessImpact(@Param("businessImpact") String businessImpact);

    @Query("SELECT COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true AND sh.priority = :priority")
    long countActiveByPriority(@Param("priority") String priority);

    @Query("SELECT COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true AND sh.slaCompliance = true")
    long countSlaCompliantServices();

    @Query("SELECT COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true AND sh.slaCompliance = false")
    long countSlaViolatingServices();

    @Query("SELECT COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true AND sh.maintenanceMode = true")
    long countMaintenanceModeServices();

    @Query("SELECT sh.serviceName, COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.serviceName")
    List<Object[]> countActiveByServiceName();

    @Query("SELECT sh.status, COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.status")
    List<Object[]> countActiveByStatus();

    @Query("SELECT sh.environment, COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.environment")
    List<Object[]> countActiveByEnvironment();

    @Query("SELECT sh.businessImpact, COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.businessImpact")
    List<Object[]> countActiveByBusinessImpact();

    @Query("SELECT sh.priority, COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.priority")
    List<Object[]> countActiveByPriority();

    @Query("SELECT sh.checkType, COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.checkType")
    List<Object[]> countActiveByCheckType();

    @Query("SELECT sh.circuitBreakerState, COUNT(sh) FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.circuitBreakerState")
    List<Object[]> countActiveByCircuitBreakerState();

    // Aggregation queries
    @Query("SELECT AVG(sh.healthScore) FROM ServiceHealth sh WHERE sh.active = true AND sh.serviceName = :serviceName")
    Double averageHealthScoreByServiceName(@Param("serviceName") String serviceName);

    @Query("SELECT AVG(sh.responseTime) FROM ServiceHealth sh WHERE sh.active = true AND sh.serviceName = :serviceName")
    Double averageResponseTimeByServiceName(@Param("serviceName") String serviceName);

    @Query("SELECT AVG(sh.errorRate) FROM ServiceHealth sh WHERE sh.active = true AND sh.serviceName = :serviceName")
    Double averageErrorRateByServiceName(@Param("serviceName") String serviceName);

    @Query("SELECT AVG(sh.uptimePercentage) FROM ServiceHealth sh WHERE sh.active = true AND sh.serviceName = :serviceName")
    Double averageUptimePercentageByServiceName(@Param("serviceName") String serviceName);

    @Query("SELECT AVG(sh.healthScore) FROM ServiceHealth sh WHERE sh.active = true AND sh.environment = :environment")
    Double averageHealthScoreByEnvironment(@Param("environment") String environment);

    @Query("SELECT AVG(sh.responseTime) FROM ServiceHealth sh WHERE sh.active = true AND sh.environment = :environment")
    Double averageResponseTimeByEnvironment(@Param("environment") String environment);

    @Query("SELECT AVG(sh.errorRate) FROM ServiceHealth sh WHERE sh.active = true AND sh.environment = :environment")
    Double averageErrorRateByEnvironment(@Param("environment") String environment);

    @Query("SELECT AVG(sh.uptimePercentage) FROM ServiceHealth sh WHERE sh.active = true AND sh.environment = :environment")
    Double averageUptimePercentageByEnvironment(@Param("environment") String environment);

    @Query("SELECT MIN(sh.healthScore) FROM ServiceHealth sh WHERE sh.active = true AND sh.serviceName = :serviceName")
    Double minHealthScoreByServiceName(@Param("serviceName") String serviceName);

    @Query("SELECT MAX(sh.healthScore) FROM ServiceHealth sh WHERE sh.active = true AND sh.serviceName = :serviceName")
    Double maxHealthScoreByServiceName(@Param("serviceName") String serviceName);

    @Query("SELECT MIN(sh.responseTime) FROM ServiceHealth sh WHERE sh.active = true AND sh.serviceName = :serviceName")
    Long minResponseTimeByServiceName(@Param("serviceName") String serviceName);

    @Query("SELECT MAX(sh.responseTime) FROM ServiceHealth sh WHERE sh.active = true AND sh.serviceName = :serviceName")
    Long maxResponseTimeByServiceName(@Param("serviceName") String serviceName);

    // Time series queries
    @Query("SELECT sh.lastCheckedAt, sh.healthScore FROM ServiceHealth sh WHERE sh.serviceName = :serviceName AND sh.lastCheckedAt >= :since ORDER BY sh.lastCheckedAt ASC")
    List<Object[]> getHealthScoreTimeSeries(@Param("serviceName") String serviceName, @Param("since") LocalDateTime since);

    @Query("SELECT sh.lastCheckedAt, sh.responseTime FROM ServiceHealth sh WHERE sh.serviceName = :serviceName AND sh.lastCheckedAt >= :since ORDER BY sh.lastCheckedAt ASC")
    List<Object[]> getResponseTimeTimeSeries(@Param("serviceName") String serviceName, @Param("since") LocalDateTime since);

    @Query("SELECT sh.lastCheckedAt, sh.errorRate FROM ServiceHealth sh WHERE sh.serviceName = :serviceName AND sh.lastCheckedAt >= :since ORDER BY sh.lastCheckedAt ASC")
    List<Object[]> getErrorRateTimeSeries(@Param("serviceName") String serviceName, @Param("since") LocalDateTime since);

    @Query("SELECT sh.lastCheckedAt, sh.uptimePercentage FROM ServiceHealth sh WHERE sh.serviceName = :serviceName AND sh.lastCheckedAt >= :since ORDER BY sh.lastCheckedAt ASC")
    List<Object[]> getUptimeTimeSeries(@Param("serviceName") String serviceName, @Param("since") LocalDateTime since);

    @Query("SELECT DATE(sh.lastCheckedAt), AVG(sh.healthScore) FROM ServiceHealth sh WHERE sh.serviceName = :serviceName AND sh.lastCheckedAt >= :since GROUP BY DATE(sh.lastCheckedAt) ORDER BY DATE(sh.lastCheckedAt)")
    List<Object[]> getDailyAverageHealthScore(@Param("serviceName") String serviceName, @Param("since") LocalDateTime since);

    @Query("SELECT DATE(sh.lastCheckedAt), AVG(sh.responseTime) FROM ServiceHealth sh WHERE sh.serviceName = :serviceName AND sh.lastCheckedAt >= :since GROUP BY DATE(sh.lastCheckedAt) ORDER BY DATE(sh.lastCheckedAt)")
    List<Object[]> getDailyAverageResponseTime(@Param("serviceName") String serviceName, @Param("since") LocalDateTime since);

    @Query("SELECT DATE(sh.lastCheckedAt), AVG(sh.errorRate) FROM ServiceHealth sh WHERE sh.serviceName = :serviceName AND sh.lastCheckedAt >= :since GROUP BY DATE(sh.lastCheckedAt) ORDER BY DATE(sh.lastCheckedAt)")
    List<Object[]> getDailyAverageErrorRate(@Param("serviceName") String serviceName, @Param("since") LocalDateTime since);

    @Query("SELECT DATE(sh.lastCheckedAt), AVG(sh.uptimePercentage) FROM ServiceHealth sh WHERE sh.serviceName = :serviceName AND sh.lastCheckedAt >= :since GROUP BY DATE(sh.lastCheckedAt) ORDER BY DATE(sh.lastCheckedAt)")
    List<Object[]> getDailyAverageUptimePercentage(@Param("serviceName") String serviceName, @Param("since") LocalDateTime since);

    // Top queries
    @Query("SELECT sh.serviceName, AVG(sh.healthScore) as avgHealthScore FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.serviceName ORDER BY avgHealthScore DESC")
    List<Object[]> findTopServicesByHealthScore();

    @Query("SELECT sh.serviceName, AVG(sh.responseTime) as avgResponseTime FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.serviceName ORDER BY avgResponseTime ASC")
    List<Object[]> findTopServicesByResponseTime();

    @Query("SELECT sh.serviceName, AVG(sh.errorRate) as avgErrorRate FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.serviceName ORDER BY avgErrorRate ASC")
    List<Object[]> findTopServicesByErrorRate();

    @Query("SELECT sh.serviceName, AVG(sh.uptimePercentage) as avgUptimePercentage FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.serviceName ORDER BY avgUptimePercentage DESC")
    List<Object[]> findTopServicesByUptimePercentage();

    @Query("SELECT sh.serviceName, COUNT(sh) as checkCount FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.serviceName ORDER BY checkCount DESC")
    List<Object[]> findTopServicesByCheckCount();

    @Query("SELECT sh.serviceName, SUM(sh.failureCount) as totalFailures FROM ServiceHealth sh WHERE sh.active = true GROUP BY sh.serviceName ORDER BY totalFailures DESC")
    List<Object[]> findTopServicesByFailureCount();

    // Cleanup queries
    @Query("DELETE FROM ServiceHealth sh WHERE sh.archived = true AND sh.archivedAt < :threshold")
    void deleteArchivedBefore(@Param("threshold") LocalDateTime threshold);

    @Query("DELETE FROM ServiceHealth sh WHERE sh.active = false AND sh.updatedAt < :threshold")
    void deleteInactiveBefore(@Param("threshold") LocalDateTime threshold);

    @Query("UPDATE ServiceHealth sh SET sh.archived = true, sh.archivedAt = :now WHERE sh.active = false AND sh.updatedAt < :threshold")
    void archiveInactiveBefore(@Param("threshold") LocalDateTime threshold, @Param("now") LocalDateTime now);

    @Query("UPDATE ServiceHealth sh SET sh.active = false WHERE sh.lastCheckedAt < :threshold")
    void deactivateStaleHealthChecks(@Param("threshold") LocalDateTime threshold);

    @Query("DELETE FROM ServiceHealth sh WHERE sh.lastCheckedAt < :threshold")
    void deleteOldHealthChecks(@Param("threshold") LocalDateTime threshold);

    // Unique value queries
    @Query("SELECT DISTINCT sh.serviceName FROM ServiceHealth sh WHERE sh.active = true ORDER BY sh.serviceName")
    List<String> findUniqueServiceNames();

    @Query("SELECT DISTINCT sh.environment FROM ServiceHealth sh WHERE sh.active = true ORDER BY sh.environment")
    List<String> findUniqueEnvironments();

    @Query("SELECT DISTINCT sh.region FROM ServiceHealth sh WHERE sh.active = true ORDER BY sh.region")
    List<String> findUniqueRegions();

    @Query("SELECT DISTINCT sh.host FROM ServiceHealth sh WHERE sh.active = true ORDER BY sh.host")
    List<String> findUniqueHosts();

    @Query("SELECT DISTINCT sh.version FROM ServiceHealth sh WHERE sh.active = true ORDER BY sh.version")
    List<String> findUniqueVersions();

    @Query("SELECT DISTINCT sh.businessImpact FROM ServiceHealth sh WHERE sh.active = true ORDER BY sh.businessImpact")
    List<String> findUniqueBusinessImpacts();

    @Query("SELECT DISTINCT sh.priority FROM ServiceHealth sh WHERE sh.active = true ORDER BY sh.priority")
    List<String> findUniquePriorities();

    @Query("SELECT DISTINCT sh.checkType FROM ServiceHealth sh WHERE sh.active = true ORDER BY sh.checkType")
    List<String> findUniqueCheckTypes();
}
