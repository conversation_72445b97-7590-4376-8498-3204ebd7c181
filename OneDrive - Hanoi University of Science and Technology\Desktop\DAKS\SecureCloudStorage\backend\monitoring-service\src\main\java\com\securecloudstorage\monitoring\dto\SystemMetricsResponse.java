package com.securecloudstorage.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * System Metrics Response DTO
 * Response chứa thông tin metrics của hệ thống
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SystemMetricsResponse {

    // System Overview
    private String systemStatus;                    // HEALTHY, WARNING, CRITICAL
    private Double systemHealth;                    // 0-100
    private LocalDateTime timestamp;                // Thời gian collect metrics
    private Long uptime;                           // Uptime in seconds
    private String version;                        // System version

    // CPU Metrics
    private Double cpuUsage;                       // % CPU usage
    private Double cpuLoad1m;                      // 1-minute load average
    private Double cpuLoad5m;                      // 5-minute load average
    private Double cpuLoad15m;                     // 15-minute load average
    private Integer cpuCores;                      // Number of CPU cores
    private Double cpuTemperature;                 // CPU temperature (if available)

    // Memory Metrics
    private Long memoryTotal;                      // Total memory in bytes
    private Long memoryUsed;                       // Used memory in bytes
    private Long memoryFree;                       // Free memory in bytes
    private Double memoryUsagePercent;             // Memory usage percentage
    private Long memoryBuffers;                    // Buffer memory
    private Long memoryCached;                     // Cached memory
    private Long swapTotal;                        // Total swap space
    private Long swapUsed;                         // Used swap space
    private Double swapUsagePercent;               // Swap usage percentage

    // Disk Metrics
    private List<DiskMetric> diskMetrics;          // Disk usage for each mount point
    private Long diskTotalSpace;                   // Total disk space
    private Long diskUsedSpace;                    // Used disk space
    private Long diskFreeSpace;                    // Free disk space
    private Double diskUsagePercent;               // Disk usage percentage
    private Double diskIORead;                     // Disk read operations/sec
    private Double diskIOWrite;                    // Disk write operations/sec

    // Network Metrics
    private List<NetworkMetric> networkMetrics;    // Network interface metrics
    private Long networkBytesIn;                   // Total bytes received
    private Long networkBytesOut;                  // Total bytes sent
    private Long networkPacketsIn;                 // Total packets received
    private Long networkPacketsOut;                // Total packets sent
    private Double networkLatency;                 // Network latency ms
    private Integer networkConnections;            // Active connections

    // Application Metrics
    private Integer activeServices;                // Number of active services
    private Integer totalServices;                 // Total number of services
    private List<ServiceMetric> serviceMetrics;    // Metrics for each service
    private Long totalRequests;                    // Total requests served
    private Long requestsPerSecond;                // Requests per second
    private Double averageResponseTime;            // Average response time
    private Double errorRate;                      // Error rate percentage

    // Database Metrics
    private List<DatabaseMetric> databaseMetrics;  // Database metrics
    private Integer activeConnections;             // Active DB connections
    private Integer maxConnections;                // Max DB connections
    private Double connectionPoolUsage;            // Connection pool usage %
    private Long totalQueries;                     // Total queries executed
    private Double queryAverageTime;               // Average query time

    // JVM Metrics (for Java services)
    private JVMMetrics jvmMetrics;                 // JVM specific metrics
    private Integer threadCount;                   // Number of threads
    private Integer daemonThreadCount;             // Number of daemon threads
    private Long heapMemoryUsed;                   // Heap memory used
    private Long heapMemoryMax;                    // Max heap memory
    private Long nonHeapMemoryUsed;                // Non-heap memory used
    private Integer gcCount;                       // GC count
    private Long gcTime;                           // GC time

    // Security Metrics
    private Long securityEvents;                   // Security events count
    private Long threatsDetected;                  // Threats detected
    private Long blockedIPs;                       // Blocked IP addresses
    private Long securityAlerts;                   // Security alerts
    private Double securityScore;                  // Security score 0-100

    // Custom Metrics
    private Map<String, Object> customMetrics;     // Custom application metrics
    private List<AlertSummary> activeAlerts;       // Active alerts summary
    private Map<String, String> configuration;     // System configuration

    // Nested classes for complex metrics
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiskMetric {
        private String mountPoint;
        private Long totalSpace;
        private Long usedSpace;
        private Long freeSpace;
        private Double usagePercent;
        private String fileSystem;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NetworkMetric {
        private String interfaceName;
        private Long bytesReceived;
        private Long bytesSent;
        private Long packetsReceived;
        private Long packetsSent;
        private Double speed;
        private String status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceMetric {
        private String serviceName;
        private String status;
        private Double cpuUsage;
        private Long memoryUsage;
        private Integer port;
        private String version;
        private LocalDateTime lastCheck;
        private Double responseTime;
        private Long requestCount;
        private Double errorRate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DatabaseMetric {
        private String databaseName;
        private String type;
        private String status;
        private Integer connections;
        private Long storageUsed;
        private Long totalStorage;
        private Double queryTime;
        private Long queryCount;
        private Double cacheHitRate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JVMMetrics {
        private String jvmVersion;
        private Long heapUsed;
        private Long heapMax;
        private Long nonHeapUsed;
        private Long nonHeapMax;
        private Integer threadCount;
        private Integer peakThreadCount;
        private Integer daemonThreadCount;
        private Long gcCollectionCount;
        private Long gcCollectionTime;
        private Double cpuUsage;
        private List<String> memoryPools;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlertSummary {
        private String alertId;
        private String severity;
        private String message;
        private String service;
        private LocalDateTime createdAt;
        private String status;
    }
}
