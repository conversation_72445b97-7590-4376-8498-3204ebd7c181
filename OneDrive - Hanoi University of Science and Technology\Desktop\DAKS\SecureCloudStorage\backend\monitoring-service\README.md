# Monitoring Service

## Tổng quan

Monitoring Service là một thành phần quan trọng trong hệ thống SecureCloudStorage, chịu trách nhiệm giám sát toàn bộ hệ thống, thu thập metrics, kiểm tra health của các services, và quản lý alerts.

## Tính năng chính

### 1. Metrics Collection
- Thu thập metrics từ tất cả các services
- Hỗ trợ các loại metrics: GAUGE, COUNTER, HISTOGRAM, SUMMARY
- Lưu trữ và quản lý dữ liệu metrics dài hạn
- Tự động phát hiện anomaly và trend analysis

### 2. Health Monitoring
- Kiểm tra health của các services định kỳ
- Hỗ trợ nhiều loại health check: HTTP, TCP, PING, CUSTOM
- Tự động phát hiện và báo cáo các vấn đề về hiệu suất
- Auto-healing cho các services có vấn đề

### 3. Alert Management
- Tạo và quản lý alerts dựa trên metrics và health checks
- Hỗ trợ nhiều mức độ cảnh báo: LOW, MEDIUM, HIGH, CRITICAL
- Escalation tự động khi alerts không được xử lý
- Notification qua email, Slack, SMS

### 4. Dashboard & Reporting
- Dashboard real-time hiển thị trạng thái hệ thống
- Báo cáo performance và availability
- Metrics visualization với charts và graphs
- SLA monitoring và compliance reporting

## Cấu trúc dự án

```
monitoring-service/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/securecloudstorage/monitoring/
│   │   │       ├── MonitoringServiceApplication.java
│   │   │       ├── controller/
│   │   │       │   └── MonitoringController.java
│   │   │       ├── service/
│   │   │       │   ├── MetricsService.java
│   │   │       │   ├── HealthCheckService.java
│   │   │       │   ├── AlertService.java
│   │   │       │   └── NotificationService.java
│   │   │       ├── entity/
│   │   │       │   ├── Alert.java
│   │   │       │   ├── Metric.java
│   │   │       │   └── ServiceHealth.java
│   │   │       ├── repository/
│   │   │       │   ├── AlertRepository.java
│   │   │       │   ├── MetricRepository.java
│   │   │       │   └── ServiceHealthRepository.java
│   │   │       ├── dto/
│   │   │       │   ├── SystemMetricsResponse.java
│   │   │       │   ├── ServiceHealthResponse.java
│   │   │       │   ├── AlertResponse.java
│   │   │       │   └── DashboardResponse.java
│   │   │       └── config/
│   │   │           ├── MetricsConfig.java
│   │   │           ├── CacheConfig.java
│   │   │           ├── AsyncConfig.java
│   │   │           └── FeignConfig.java
│   │   └── resources/
│   │       ├── application.yml
│   │       └── schema.sql
│   └── test/
├── target/
├── Dockerfile
├── pom.xml
└── README.md
```

## Cơ sở dữ liệu

### Database Schema

Monitoring Service sử dụng PostgreSQL với 3 bảng chính:

1. **alerts** - Lưu trữ thông tin alerts
2. **metrics** - Lưu trữ dữ liệu metrics
3. **service_health** - Lưu trữ thông tin health checks

### Indexes và Optimization

- Indexes được tối ưu cho các truy vấn thường xuyên
- Materialized views cho các báo cáo phức tạp
- Partitioning cho dữ liệu lớn
- Tự động cleanup dữ liệu cũ

## API Endpoints

### Metrics Endpoints
- `GET /api/metrics/system` - System metrics
- `GET /api/metrics/services` - Service metrics
- `GET /api/metrics/performance` - Performance metrics
- `GET /api/metrics/capacity` - Capacity metrics
- `GET /api/metrics/sla` - SLA metrics

### Health Check Endpoints
- `GET /api/health/services` - Service health status
- `GET /api/health/dependencies` - Dependency health
- `GET /api/health/summary` - Health summary
- `POST /api/health/check` - Manual health check

### Alert Endpoints
- `GET /api/alerts` - List alerts
- `POST /api/alerts` - Create alert
- `PUT /api/alerts/{id}` - Update alert
- `DELETE /api/alerts/{id}` - Delete alert
- `POST /api/alerts/{id}/acknowledge` - Acknowledge alert
- `POST /api/alerts/{id}/resolve` - Resolve alert

### Dashboard Endpoints
- `GET /api/dashboard/overview` - Dashboard overview
- `GET /api/dashboard/metrics` - Metrics dashboard
- `GET /api/dashboard/health` - Health dashboard
- `GET /api/dashboard/alerts` - Alerts dashboard

## Cấu hình

### Database Configuration
```yaml
spring:
  datasource:
    url: **********************************************
    username: postgres
    password: postgres
```

### Monitoring Configuration
```yaml
monitoring:
  metrics:
    collection:
      enabled: true
      interval: 30s
      batch-size: 100
  health:
    check:
      enabled: true
      interval: 60s
      timeout: 30s
  alerts:
    enabled: true
    processing:
      interval: 10s
      batch-size: 50
```

### Notification Configuration
```yaml
monitoring:
  alerts:
    notifications:
      enabled: true
      channels:
        email:
          enabled: true
        slack:
          enabled: false
        sms:
          enabled: false
```

## Deployment

### Docker Deployment
```bash
# Build image
docker build -t monitoring-service:1.0.0 .

# Run container
docker run -d \
  --name monitoring-service \
  -p 8085:8085 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e DB_HOST=postgres \
  -e DB_PORT=5432 \
  -e DB_NAME=monitoring_db \
  -e DB_USER=postgres \
  -e DB_PASSWORD=postgres \
  monitoring-service:1.0.0
```

### Docker Compose
```yaml
version: '3.8'
services:
  monitoring-service:
    build: .
    ports:
      - "8085:8085"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=postgres
    depends_on:
      - postgres
      - redis
```

## Monitoring và Observability

### Actuator Endpoints
- `/actuator/health` - Health check
- `/actuator/info` - Application info
- `/actuator/metrics` - Metrics
- `/actuator/prometheus` - Prometheus metrics

### Prometheus Integration
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
```

### Grafana Dashboard
- System metrics dashboard
- Service health dashboard
- Alert management dashboard
- Performance monitoring dashboard

## Tích hợp với các Services

### API Gateway Integration
```java
@FeignClient(name = "api-gateway", url = "http://localhost:8080")
public interface GatewayClient {
    @GetMapping("/actuator/health")
    ResponseEntity<Object> getHealth();
    
    @GetMapping("/actuator/metrics")
    ResponseEntity<Object> getMetrics();
}
```

### Storage Service Integration
```java
@FeignClient(name = "storage-service", url = "http://localhost:8082")
public interface StorageClient {
    @GetMapping("/actuator/health")
    ResponseEntity<Object> getHealth();
    
    @GetMapping("/actuator/metrics")
    ResponseEntity<Object> getMetrics();
}
```

## Performance Tuning

### JVM Settings
```bash
JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication"
```

### Database Connection Pool
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 20000
      validation-timeout: 5000
```

### Caching Configuration
```yaml
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=10m
```

## Bảo mật

### Authentication & Authorization
- JWT token validation
- Role-based access control
- API key authentication for external integrations

### Data Protection
- Sensitive data encryption
- Secure communication (HTTPS/TLS)
- Data masking in logs

### Audit Logging
- All API calls logged
- User activity tracking
- Security event monitoring

## Troubleshooting

### Common Issues

1. **High Memory Usage**
   - Check metrics retention policy
   - Optimize database queries
   - Increase JVM heap size

2. **Slow Response Times**
   - Check database performance
   - Optimize indexes
   - Enable caching

3. **Missing Metrics**
   - Verify service endpoints
   - Check network connectivity
   - Review collection configuration

### Logs
```bash
# View application logs
docker logs monitoring-service

# View specific log level
docker logs monitoring-service | grep ERROR

# Follow logs in real-time
docker logs -f monitoring-service
```

## Development

### Setup Development Environment
```bash
# Clone repository
git clone <repository-url>
cd monitoring-service

# Install dependencies
mvn clean install

# Run tests
mvn test

# Run application
mvn spring-boot:run
```

### Testing
```bash
# Unit tests
mvn test

# Integration tests
mvn integration-test

# Test coverage
mvn jacoco:report
```

### Code Quality
```bash
# Code formatting
mvn spring-javaformat:apply

# Static analysis
mvn sonar:sonar

# Dependency check
mvn dependency-check:check
```

## Monitoring Metrics

### Key Metrics to Monitor
- **System Metrics**: CPU, Memory, Disk, Network
- **Application Metrics**: Request rate, Response time, Error rate
- **Business Metrics**: User activity, Feature usage, SLA compliance
- **Infrastructure Metrics**: Database performance, Cache hit rate

### Alerting Rules
- CPU usage > 80% for 5 minutes
- Memory usage > 85% for 3 minutes
- Error rate > 5% for 2 minutes
- Response time > 2 seconds for 1 minute
- Service down for more than 30 seconds

## Maintenance

### Regular Tasks
- Database cleanup (old metrics, resolved alerts)
- Log rotation and archival
- Performance tuning
- Security updates

### Backup Strategy
- Database backups (daily)
- Configuration backups
- Monitoring data retention policy
- Disaster recovery procedures

## Support

### Documentation
- API documentation: `/swagger-ui.html`
- Actuator endpoints: `/actuator`
- Health check: `/actuator/health`

### Contact
- Development Team: <EMAIL>
- Operations Team: <EMAIL>
- Security Team: <EMAIL>
