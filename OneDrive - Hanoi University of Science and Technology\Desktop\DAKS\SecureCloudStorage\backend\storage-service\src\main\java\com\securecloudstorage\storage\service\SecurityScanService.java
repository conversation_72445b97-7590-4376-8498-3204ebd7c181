package com.securecloudstorage.storage.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.concurrent.CompletableFuture;

/**
 * Security Scan Service
 * Tích hợp AI-Malware Detection và Network IDS
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SecurityScanService {

    private final WebClient webClient;

    public SecurityScanService() {
        this.webClient = WebClient.builder()
            .baseUrl("http://localhost:8086") // AI-Malware Detection service
            .build();
    }

    /**
     * Scan file asynchronously với AI-Malware Detection
     */
    public CompletableFuture<Boolean> scanFileAsync(MultipartFile file) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Call AI-Malware Detection service
                ScanResult result = webClient.post()
                    .uri("/api/scan-file")
                    .bodyValue(ScanRequest.builder()
                        .fileName(file.getOriginalFilename())
                        .fileContent(file.getBytes())
                        .fileSize(file.getSize())
                        .contentType(file.getContentType())
                        .build())
                    .retrieve()
                    .bodyToMono(ScanResult.class)
                    .block();

                if (result != null) {
                    log.info("AI scan result for {}: {} (confidence: {})", 
                        file.getOriginalFilename(), 
                        result.isSafe() ? "SAFE" : "THREAT", 
                        result.getConfidence());
                    
                    return result.isSafe();
                }

                return true; // Default to safe if scan fails

            } catch (Exception e) {
                log.error("Error scanning file {}: ", file.getOriginalFilename(), e);
                return true; // Default to safe if scan fails
            }
        });
    }

    /**
     * Get scan result for a file
     */
    public Object getScanResult(String fileId) {
        try {
            return webClient.get()
                .uri("/api/scan-result/{fileId}", fileId)
                .retrieve()
                .bodyToMono(Object.class)
                .block();
        } catch (Exception e) {
            log.error("Error getting scan result for {}: ", fileId, e);
            return null;
        }
    }

    /**
     * Check network threat với Network IDS
     */
    public Mono<Boolean> checkNetworkThreat(String clientIp) {
        return webClient.post()
            .uri("http://localhost:5000/api/check-threat")
            .bodyValue(NetworkThreatRequest.builder()
                .clientIp(clientIp)
                .timestamp(System.currentTimeMillis())
                .build())
            .retrieve()
            .bodyToMono(NetworkThreatResult.class)
            .map(result -> !result.isThreat())
            .onErrorReturn(true); // Default to safe if check fails
    }

    // DTOs for scan requests/responses
    public static class ScanRequest {
        private String fileName;
        private byte[] fileContent;
        private long fileSize;
        private String contentType;

        public static ScanRequestBuilder builder() {
            return new ScanRequestBuilder();
        }

        public static class ScanRequestBuilder {
            private String fileName;
            private byte[] fileContent;
            private long fileSize;
            private String contentType;

            public ScanRequestBuilder fileName(String fileName) {
                this.fileName = fileName;
                return this;
            }

            public ScanRequestBuilder fileContent(byte[] fileContent) {
                this.fileContent = fileContent;
                return this;
            }

            public ScanRequestBuilder fileSize(long fileSize) {
                this.fileSize = fileSize;
                return this;
            }

            public ScanRequestBuilder contentType(String contentType) {
                this.contentType = contentType;
                return this;
            }

            public ScanRequest build() {
                ScanRequest request = new ScanRequest();
                request.fileName = this.fileName;
                request.fileContent = this.fileContent;
                request.fileSize = this.fileSize;
                request.contentType = this.contentType;
                return request;
            }
        }

        // Getters and setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public byte[] getFileContent() { return fileContent; }
        public void setFileContent(byte[] fileContent) { this.fileContent = fileContent; }
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }
    }

    public static class ScanResult {
        private boolean safe;
        private double confidence;
        private String threatType;
        private String description;

        // Getters and setters
        public boolean isSafe() { return safe; }
        public void setSafe(boolean safe) { this.safe = safe; }
        public double getConfidence() { return confidence; }
        public void setConfidence(double confidence) { this.confidence = confidence; }
        public String getThreatType() { return threatType; }
        public void setThreatType(String threatType) { this.threatType = threatType; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }

    public static class NetworkThreatRequest {
        private String clientIp;
        private long timestamp;

        public static NetworkThreatRequestBuilder builder() {
            return new NetworkThreatRequestBuilder();
        }

        public static class NetworkThreatRequestBuilder {
            private String clientIp;
            private long timestamp;

            public NetworkThreatRequestBuilder clientIp(String clientIp) {
                this.clientIp = clientIp;
                return this;
            }

            public NetworkThreatRequestBuilder timestamp(long timestamp) {
                this.timestamp = timestamp;
                return this;
            }

            public NetworkThreatRequest build() {
                NetworkThreatRequest request = new NetworkThreatRequest();
                request.clientIp = this.clientIp;
                request.timestamp = this.timestamp;
                return request;
            }
        }

        // Getters and setters
        public String getClientIp() { return clientIp; }
        public void setClientIp(String clientIp) { this.clientIp = clientIp; }
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }

    public static class NetworkThreatResult {
        private boolean threat;
        private String threatType;
        private double score;

        // Getters and setters
        public boolean isThreat() { return threat; }
        public void setThreat(boolean threat) { this.threat = threat; }
        public String getThreatType() { return threatType; }
        public void setThreatType(String threatType) { this.threatType = threatType; }
        public double getScore() { return score; }
        public void setScore(double score) { this.score = score; }
    }
}
