"""
Configuration utility for malware detection system
"""

import yaml
import os
from pathlib import Path

def load_config(config_path='config/config.yaml'):
    """Load configuration from YAML file"""
    
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # Set default values
    config = set_defaults(config)
    
    # Create necessary directories
    create_directories(config)
    
    return config

def set_defaults(config):
    """Set default configuration values"""
    
    defaults = {
        'logging': {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file': 'logs/malware_detection.log'
        },
        'models': {
            'random_forest': {'enabled': True},
            'svm': {'enabled': True},
            'neural_network': {'enabled': True}
        },
        'features': {
            'pe_features': {'enabled': True},
            'static_features': {'enabled': True},
            'dynamic_features': {'enabled': False}
        },
        'analysis': {
            'static': {'enabled': True, 'timeout': 30},
            'dynamic': {'enabled': False, 'timeout': 300}
        },
        'monitoring': {
            'enabled': True,
            'scan_interval': 5,
            'quarantine_directory': 'quarantine/'
        },
        'database': {
            'type': 'sqlite',
            'path': 'data/malware_detection.db'
        },
        'performance': {
            'max_file_size': 104857600,
            'max_concurrent_scans': 4,
            'cache_enabled': True,
            'cache_size': 1000
        }
    }
    
    # Merge defaults with loaded config
    def merge_dicts(default, loaded):
        for key, value in default.items():
            if key not in loaded:
                loaded[key] = value
            elif isinstance(value, dict) and isinstance(loaded[key], dict):
                merge_dicts(value, loaded[key])
    
    merge_dicts(defaults, config)
    return config

def create_directories(config):
    """Create necessary directories based on configuration"""
    
    dirs_to_create = [
        'logs',
        'data',
        'data/samples',
        'data/datasets',
        'models',
        'quarantine',
        'temp'
    ]
    
    # Add configured directories
    if 'monitoring' in config and 'quarantine_directory' in config['monitoring']:
        dirs_to_create.append(config['monitoring']['quarantine_directory'])
    
    if 'analysis' in config and 'dynamic' in config['analysis']:
        if 'sandbox_path' in config['analysis']['dynamic']:
            dirs_to_create.append(config['analysis']['dynamic']['sandbox_path'])
    
    for dir_path in dirs_to_create:
        Path(dir_path).mkdir(parents=True, exist_ok=True)

def get_model_config(config, model_name):
    """Get configuration for a specific model"""
    return config.get('models', {}).get(model_name, {})

def get_feature_config(config, feature_type):
    """Get configuration for a specific feature type"""
    return config.get('features', {}).get(feature_type, {})

def is_feature_enabled(config, feature_type):
    """Check if a feature type is enabled"""
    return get_feature_config(config, feature_type).get('enabled', False)

def is_model_enabled(config, model_name):
    """Check if a model is enabled"""
    return get_model_config(config, model_name).get('enabled', False)
