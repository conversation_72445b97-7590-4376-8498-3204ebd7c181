@echo off
REM Build script for Secure Cloud Storage Backend (Windows)
REM Builds all microservices and starts the system

echo ======================================
echo Building Secure Cloud Storage Backend
echo ======================================

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Docker is not running
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Docker Compose is not installed
    exit /b 1
)

REM Create necessary directories
echo Creating directories...
mkdir uploads 2>nul
mkdir quarantine 2>nul
mkdir logs 2>nul
mkdir logs\gateway 2>nul
mkdir logs\storage 2>nul
mkdir logs\user 2>nul
mkdir logs\security 2>nul
mkdir logs\monitoring 2>nul
mkdir logs\ai-malware 2>nul
mkdir logs\network-ids 2>nul

REM Build API Gateway
echo Building API Gateway...
cd api-gateway
call mvnw.cmd clean package -DskipTests
cd ..

REM Build Storage Service
echo Building Storage Service...
cd storage-service
call mvnw.cmd clean package -DskipTests
cd ..

REM Build User Service
echo Building User Service...
cd user-service
call mvnw.cmd clean package -DskipTests
cd ..

REM Build Security Service (if exists)
if exist "security-service" (
    echo Building Security Service...
    cd security-service
    call mvnw.cmd clean package -DskipTests
    cd ..
)

REM Build Monitoring Service (if exists)
if exist "monitoring-service" (
    echo Building Monitoring Service...
    cd monitoring-service
    call mvnw.cmd clean package -DskipTests
    cd ..
)

REM Create Docker network
echo Creating Docker network...
docker network create secure-cloud-network 2>nul

REM Build and start services
echo Starting services with Docker Compose...
echo Starting MariaDB database...
docker-compose up -d mariadb

echo Waiting for MariaDB to be ready...
timeout /t 30 /nobreak >nul

echo Starting other services...
docker-compose up --build -d

echo ======================================
echo Services starting up...
echo ======================================

REM Wait for services to be ready
echo Waiting for services to be ready...
timeout /t 30 /nobreak >nul

REM Check service health
echo Checking service health...

echo Checking API Gateway on port 8080...
call :check_service "api-gateway" "8080"

echo Checking Storage Service on port 8082...
call :check_service "storage-service" "8082"

echo Checking User Service on port 8083...
call :check_service "user-service" "8083"

REM Check Python services
echo Checking Python services...
curl -s -f http://localhost:5000/api/stats >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Network IDS is ready
) else (
    echo ⚠ Network IDS is not ready
)

curl -s -f http://localhost:8086/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ AI-Malware Detection is ready
) else (
    echo ⚠ AI-Malware Detection is not ready
)

echo ======================================
echo Build and deployment completed!
echo ======================================
echo.
echo Services running:
echo - API Gateway: http://localhost:8080
echo - Storage Service: http://localhost:8082
echo - User Service: http://localhost:8083
echo - Security Service: http://localhost:8084
echo - Monitoring Service: http://localhost:8085
echo - AI-Malware Detection: http://localhost:8086
echo - Network IDS: http://localhost:5000
echo - Prometheus: http://localhost:9090
echo - Grafana: http://localhost:3000 (admin/admin)
echo.
echo To check logs: docker-compose logs -f [service-name]
echo To stop: docker-compose down
echo To rebuild: docker-compose down && docker-compose up --build -d
goto :eof

:check_service
set service_name=%~1
set port=%~2
set timeout=30

:check_loop
curl -s -f http://localhost:%port%/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ %service_name% is ready
    goto :eof
)
timeout /t 2 /nobreak >nul
set /a timeout-=2
if %timeout% gtr 0 goto :check_loop

echo ⚠ %service_name% is not ready (timeout)
goto :eof
