package com.securecloudstorage.security.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Security Event Request DTO
 * D<PERSON> liệu request để ghi lại security event
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecurityEventRequest {
    
    private String eventType;           // LOGIN, LOGOUT, FILE_ACCESS, FILE_UPLOAD, etc.
    private String severity;            // LOW, MEDIUM, HIGH, CRITICAL
    private String description;         // Mô tả chi tiết event
    private String userId;              // ID người dùng (nếu có)
    private String resourceId;          // ID tài nguyên liên quan (nếu có)
    private String ipAddress;           // IP address của client
    private String userAgent;           // User-Agent header
    private String sessionId;           // Session ID
    private Map<String, Object> metadata; // Metadata bổ sung
    private LocalDateTime timestamp;    // Thời gian x<PERSON>y ra event
    private String source;              // Nguồn gốc event (API_GATEWAY, STORAGE_SERVICE, etc.)
    private String action;              // Hành động cụ thể
    private String targetEntity;        // Entity được tác động
    private String targetEntityId;      // ID của entity được tác động
    private Boolean isSuccessful;       // Event có thành công không
    private String failureReason;       // Lý do thất bại (nếu có)
    private String geolocation;         // Vị trí địa lý (nếu có)
    private String deviceInfo;          // Thông tin thiết bị
    private String applicationVersion;  // Phiên bản ứng dụng
    private String riskScore;           // Điểm rủi ro (0-100)
    private String threatLevel;         // Mức độ nguy hiểm
    private String correlationId;       // ID để correlation với các event khác
    private String customData;          // Dữ liệu tùy chỉnh (JSON string)
}
