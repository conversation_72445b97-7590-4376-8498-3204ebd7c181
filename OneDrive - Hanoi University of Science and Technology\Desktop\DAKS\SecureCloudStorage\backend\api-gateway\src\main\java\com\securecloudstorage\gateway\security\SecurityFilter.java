package com.securecloudstorage.gateway.security;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Set;

/**
 * Security Filter tích hợp Network IDS và AI-Malware Detection
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SecurityFilter implements GlobalFilter, Ordered {

    private final JwtTokenProvider jwtTokenProvider;
    private final NetworkIDSClient networkIDSClient;
    private final AISecurityClient aiSecurityClient;

    private static final Set<String> EXCLUDED_PATHS = Set.of(
        "/api/users/login",
        "/api/users/register",
        "/api/users/refresh",
        "/actuator/health"
    );

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        
        String path = request.getURI().getPath();
        String clientIp = getClientIp(request);
        
        log.info("Processing request: {} from IP: {}", path, clientIp);

        // 1. Network IDS Security Check
        return performNetworkIDSCheck(request, clientIp)
            .flatMap(idsResult -> {
                if (!idsResult.isAllowed()) {
                    log.warn("Request blocked by Network IDS: {}", idsResult.getReason());
                    response.setStatusCode(HttpStatus.FORBIDDEN);
                    return response.setComplete();
                }

                // 2. AI-based Security Analysis
                return performAISecurityCheck(request, clientIp)
                    .flatMap(aiResult -> {
                        if (!aiResult.isAllowed()) {
                            log.warn("Request blocked by AI Security: {}", aiResult.getReason());
                            response.setStatusCode(HttpStatus.FORBIDDEN);
                            return response.setComplete();
                        }

                        // 3. JWT Token Validation (for protected endpoints)
                        if (!isExcludedPath(path)) {
                            return validateJwtToken(request)
                                .flatMap(isValid -> {
                                    if (!isValid) {
                                        response.setStatusCode(HttpStatus.UNAUTHORIZED);
                                        return response.setComplete();
                                    }
                                    return continueFilter(exchange, chain);
                                });
                        }

                        return continueFilter(exchange, chain);
                    });
            })
            .onErrorResume(error -> {
                log.error("Security filter error: ", error);
                response.setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
                return response.setComplete();
            });
    }

    private Mono<SecurityCheckResult> performNetworkIDSCheck(ServerHttpRequest request, String clientIp) {
        return networkIDSClient.checkThreat(clientIp, request.getHeaders())
            .map(result -> SecurityCheckResult.builder()
                .allowed(!result.isThreat())
                .reason(result.getThreatType())
                .score(result.getThreatScore())
                .build())
            .onErrorReturn(SecurityCheckResult.builder()
                .allowed(true)
                .reason("Network IDS check failed")
                .build());
    }

    private Mono<SecurityCheckResult> performAISecurityCheck(ServerHttpRequest request, String clientIp) {
        return aiSecurityClient.analyzeThreat(clientIp, request.getHeaders(), request.getURI())
            .map(result -> SecurityCheckResult.builder()
                .allowed(!result.isMalicious())
                .reason(result.getThreatType())
                .score(result.getConfidence())
                .build())
            .onErrorReturn(SecurityCheckResult.builder()
                .allowed(true)
                .reason("AI Security check failed")
                .build());
    }

    private Mono<Boolean> validateJwtToken(ServerHttpRequest request) {
        List<String> authHeaders = request.getHeaders().get("Authorization");
        
        if (authHeaders == null || authHeaders.isEmpty()) {
            return Mono.just(false);
        }

        String authHeader = authHeaders.get(0);
        if (!authHeader.startsWith("Bearer ")) {
            return Mono.just(false);
        }

        String token = authHeader.substring(7);
        return Mono.just(jwtTokenProvider.validateToken(token));
    }

    private Mono<Void> continueFilter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // Add security headers
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().add("X-Frame-Options", "DENY");
        response.getHeaders().add("X-Content-Type-Options", "nosniff");
        response.getHeaders().add("X-XSS-Protection", "1; mode=block");
        response.getHeaders().add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");

        return chain.filter(exchange);
    }

    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddress() != null 
            ? request.getRemoteAddress().getAddress().getHostAddress() 
            : "unknown";
    }

    private boolean isExcludedPath(String path) {
        return EXCLUDED_PATHS.contains(path) || path.startsWith("/actuator/");
    }

    @Override
    public int getOrder() {
        return -1; // High priority
    }
}
