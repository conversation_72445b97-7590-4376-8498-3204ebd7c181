package com.securecloudstorage.security.repository;

import com.securecloudstorage.security.entity.SecurityAlert;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Security Alert Repository
 * Repository cho SecurityAlert entity
 */
@Repository
public interface SecurityAlertRepository extends JpaRepository<SecurityAlert, Long> {

    // Find methods
    List<SecurityAlert> findByStatus(String status);
    
    List<SecurityAlert> findByStatusOrderByCreatedAtDesc(String status);
    
    List<SecurityAlert> findBySeverity(String severity);
    
    List<SecurityAlert> findByAlertType(String alertType);
    
    List<SecurityAlert> findByAssignedTo(String assignedTo);
    
    List<SecurityAlert> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end);
    
    List<SecurityAlert> findByStatusAndSeverity(String status, String severity);
    
    List<SecurityAlert> findByStatusAndAssignedTo(String status, String assignedTo);

    // Count methods
    long countByStatus(String status);
    
    long countBySeverity(String severity);
    
    long countByAlertType(String alertType);
    
    long countByAssignedTo(String assignedTo);
    
    long countByCreatedAtAfter(LocalDateTime timestamp);
    
    long countByStatusAndSeverity(String status, String severity);

    // Custom queries
    @Query("SELECT a FROM SecurityAlert a WHERE a.status = 'OPEN' AND a.priority <= :priority ORDER BY a.priority ASC, a.createdAt DESC")
    List<SecurityAlert> findOpenAlertsByPriority(@Param("priority") Integer priority);
    
    @Query("SELECT a FROM SecurityAlert a WHERE a.status = 'OPEN' AND a.slaDeadline < :deadline ORDER BY a.slaDeadline ASC")
    List<SecurityAlert> findOverdueAlerts(@Param("deadline") LocalDateTime deadline);
    
    @Query("SELECT a FROM SecurityAlert a WHERE a.status = 'OPEN' AND a.assignedTo IS NULL ORDER BY a.priority ASC, a.createdAt DESC")
    List<SecurityAlert> findUnassignedAlerts();
    
    @Query("SELECT a.severity, COUNT(a) FROM SecurityAlert a WHERE a.status = 'OPEN' GROUP BY a.severity")
    List<Object[]> getOpenAlertsBySeverity();
    
    @Query("SELECT a.alertType, COUNT(a) FROM SecurityAlert a WHERE a.createdAt > :timestamp GROUP BY a.alertType")
    List<Object[]> getAlertTypeStats(@Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT a.assignedTo, COUNT(a) FROM SecurityAlert a WHERE a.assignedTo IS NOT NULL AND a.status = 'OPEN' GROUP BY a.assignedTo")
    List<Object[]> getAssignedAlertsStats();
    
    @Query("SELECT DATE(a.createdAt) as date, COUNT(a) as count FROM SecurityAlert a WHERE a.createdAt > :timestamp GROUP BY DATE(a.createdAt) ORDER BY date DESC")
    List<Object[]> getDailyAlertCounts(@Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT a FROM SecurityAlert a WHERE a.status = 'OPEN' AND a.severity = 'CRITICAL' ORDER BY a.createdAt DESC")
    List<SecurityAlert> findCriticalOpenAlerts();
    
    @Query("SELECT a FROM SecurityAlert a WHERE a.eventId = :eventId")
    List<SecurityAlert> findByEventId(@Param("eventId") Long eventId);
    
    @Query("SELECT AVG(TIMESTAMPDIFF(HOUR, a.createdAt, a.resolvedAt)) FROM SecurityAlert a WHERE a.status = 'CLOSED' AND a.resolvedAt IS NOT NULL")
    Double getAverageResolutionTime();
    
    @Query("SELECT COUNT(a) FROM SecurityAlert a WHERE a.status = 'OPEN' AND a.createdAt > :timestamp")
    long countOpenAlertsAfter(@Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT a FROM SecurityAlert a WHERE a.status = 'OPEN' AND a.createdAt < :timestamp ORDER BY a.createdAt ASC")
    List<SecurityAlert> findStaleAlerts(@Param("timestamp") LocalDateTime timestamp);
}
