@echo off
echo Network Intrusion Detection System (IDS)
echo ========================================

:menu
echo.
echo Choose an option:
echo 1. Setup environment
echo 2. Train ML models
echo 3. Start monitoring
echo 4. View dashboard
echo 5. Analyze PCAP file
echo 6. Test system
echo 7. View statistics
echo 8. Configuration
echo 9. Exit
echo.

set /p choice=Enter your choice (1-9): 

if "%choice%"=="1" goto setup
if "%choice%"=="2" goto train
if "%choice%"=="3" goto monitor
if "%choice%"=="4" goto dashboard
if "%choice%"=="5" goto analyze
if "%choice%"=="6" goto test
if "%choice%"=="7" goto stats
if "%choice%"=="8" goto config
if "%choice%"=="9" goto exit

echo Invalid choice. Please try again.
goto menu

:setup
echo Setting up Network IDS environment...
echo.
echo Installing Python dependencies...
pip install -r requirements.txt
echo.
echo Creating directories...
mkdir logs 2>nul
mkdir data 2>nul
mkdir models 2>nul
mkdir reports 2>nul
mkdir rules 2>nul
echo.
echo Setup completed!
pause
goto menu

:train
echo Training ML models...
echo.
echo This may take several minutes...
python main.py --train
echo.
echo Training completed!
pause
goto menu

:monitor
echo Starting network monitoring...
echo.
echo WARNING: This requires administrator privileges!
echo Press Ctrl+C to stop monitoring
echo.
python main.py --start
pause
goto menu

:dashboard
echo Starting web dashboard...
echo.
echo Dashboard will be available at: http://localhost:8080
echo Press Ctrl+C to stop dashboard
echo.
python main.py --dashboard
pause
goto menu

:analyze
echo Analyzing PCAP file...
echo.
set /p pcap_file=Enter PCAP file path: 
if exist "%pcap_file%" (
    python main.py --analyze-pcap "%pcap_file%"
) else (
    echo File not found: %pcap_file%
)
pause
goto menu

:test
echo Testing Network IDS components...
echo.
echo Testing packet capture...
python -c "from src.capture.packet_capture import PacketCapture; pc = PacketCapture({}); print('Packet capture: OK' if pc else 'Packet capture: FAILED')"
echo.
echo Testing traffic analysis...
python -c "from src.analysis.traffic_analyzer import TrafficAnalyzer; ta = TrafficAnalyzer({}); print('Traffic analyzer: OK')"
echo.
echo Testing ML detection...
python -c "from src.detection.ml_detector import MLDetector; md = MLDetector({}); print('ML detector: OK')"
echo.
echo Testing signature detection...
python -c "from src.detection.signature_detector import SignatureDetector; sd = SignatureDetector({}); print('Signature detector: OK')"
echo.
echo Testing alert manager...
python -c "from src.alerts.alert_manager import AlertManager; am = AlertManager({}); print('Alert manager: OK')"
echo.
echo Component tests completed!
pause
goto menu

:stats
echo Network IDS Statistics
echo =====================
echo.
python -c "
import json
from pathlib import Path
from src.utils.config import Config
from src.utils.logger import LogAnalyzer

# Load configuration
config = Config()

# Analyze logs
log_analyzer = LogAnalyzer('logs/network_ids.log')
if Path('logs/network_ids.log').exists():
    events = log_analyzer.analyze_security_events()
    print('Security Events Analysis:')
    print(f'  Total events: {events.get(\"total_events\", 0)}')
    print(f'  Threat detections: {events.get(\"threat_detections\", 0)}')
    print(f'  Security events: {events.get(\"security_events\", 0)}')
    print(f'  System compromises: {events.get(\"compromises\", 0)}')
    print()
else:
    print('No log file found. Run the system first.')

# Check model status
if Path('models').exists():
    model_files = list(Path('models').glob('*'))
    print(f'ML Models: {len(model_files)} files found')
    for model in model_files:
        print(f'  - {model.name}')
    print()
else:
    print('No ML models found. Train models first.')
"
pause
goto menu

:config
echo Network IDS Configuration
echo =========================
echo.
echo Current configuration file: config/config.yaml
echo.
echo Options:
echo 1. Edit configuration
echo 2. View current config
echo 3. Reset to defaults
echo 4. Validate configuration
echo.
set /p config_choice=Enter choice (1-4): 

if "%config_choice%"=="1" (
    echo Opening configuration file...
    notepad config\config.yaml
) else if "%config_choice%"=="2" (
    echo Current configuration:
    type config\config.yaml
) else if "%config_choice%"=="3" (
    echo Resetting configuration to defaults...
    copy config\config.yaml config\config.yaml.backup
    echo Configuration backed up to config.yaml.backup
) else if "%config_choice%"=="4" (
    echo Validating configuration...
    python -c "from src.utils.config import Config; c = Config(); print('Configuration valid!' if c.validate_config() else 'Configuration invalid!')"
)

pause
goto menu

:exit
echo.
echo Thank you for using Network IDS!
echo.
echo Cleaning up...
echo Stopping any running processes...
taskkill /F /IM python.exe 2>nul
echo.
echo Goodbye!
pause
exit

:error
echo.
echo An error occurred. Please check the logs for more information.
echo Log file: logs/network_ids.log
echo.
pause
goto menu
