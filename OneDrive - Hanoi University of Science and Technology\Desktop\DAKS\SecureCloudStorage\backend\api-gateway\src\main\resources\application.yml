server:
  port: 8080
  
spring:
  application:
    name: secure-cloud-storage-gateway
  
  # Redis configuration for rate limiting
  redis:
    host: localhost
    port: 6379
    password: ""
    timeout: 2000ms
    
  # Cloud Gateway configuration
  cloud:
    gateway:
      routes:
        - id: storage-service
          uri: http://localhost:8082
          predicates:
            - Path=/api/storage/**
          filters:
            - StripPrefix=2
            - name: CircuitBreaker
              args:
                name: storage-service-cb
                fallbackUri: forward:/fallback/storage
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                key-resolver: "#{@userKeyResolver}"
        
        - id: user-service
          uri: http://localhost:8083
          predicates:
            - Path=/api/users/**
          filters:
            - StripPrefix=2
            - name: CircuitBreaker
              args:
                name: user-service-cb
                fallbackUri: forward:/fallback/user
        
        - id: security-service
          uri: http://localhost:8084
          predicates:
            - Path=/api/security/**
          filters:
            - StripPrefix=2
            - name: CircuitBreaker
              args:
                name: security-service-cb
                fallbackUri: forward:/fallback/security
        
        - id: monitoring-service
          uri: http://localhost:8085
          predicates:
            - Path=/api/monitoring/**
          filters:
            - StripPrefix=2
            - name: CircuitBreaker
              args:
                name: monitoring-service-cb
                fallbackUri: forward:/fallback/monitoring
        
        - id: network-ids
          uri: http://localhost:5000
          predicates:
            - Path=/api/ids/**
          filters:
            - StripPrefix=2
            - name: CircuitBreaker
              args:
                name: network-ids-cb
                fallbackUri: forward:/fallback/ids
        
        - id: ai-malware
          uri: http://localhost:8086
          predicates:
            - Path=/api/malware/**
          filters:
            - StripPrefix=2
            - name: CircuitBreaker
              args:
                name: ai-malware-cb
                fallbackUri: forward:/fallback/malware

      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true

# JWT configuration
jwt:
  secret: mySecretKey123456789012345678901234567890
  expiration: 86400000 # 24 hours

# Resilience4j Circuit Breaker configuration
resilience4j:
  circuitbreaker:
    instances:
      storage-service-cb:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 10s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
      user-service-cb:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 10s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
      security-service-cb:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 10s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
      monitoring-service-cb:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 10s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
      network-ids-cb:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 10s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
      ai-malware-cb:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 10s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10

# Actuator configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# Logging configuration
logging:
  level:
    com.securecloudstorage: DEBUG
    org.springframework.cloud.gateway: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/gateway.log
