[{"rule_id": "WEB_001", "name": "SQL Injection Detection", "description": "Detects SQL injection attempts in web traffic", "pattern": "(union\\s+select|select\\s+.*\\s+from|insert\\s+into|drop\\s+table)", "severity": "HIGH", "category": "web_attack"}, {"rule_id": "WEB_002", "name": "XSS Detection", "description": "Detects cross-site scripting attempts", "pattern": "(<script|javascript:|vbscript:|onload=|onerror=)", "severity": "MEDIUM", "category": "web_attack"}, {"rule_id": "NETWORK_001", "name": "Port Scan Detection", "description": "Detects network port scanning", "pattern": "(nmap|port.*scan|stealth.*scan)", "severity": "MEDIUM", "category": "reconnaissance"}]