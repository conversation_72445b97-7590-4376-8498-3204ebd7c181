import { apiService } from './apiService';
import {
    SecurityEvent,
    SecurityAlert,
    ThreatIntelligence,
    NetworkEvent,
    MalwareAnalysis,
    PaginatedResponse,
    SecurityEventType,
    SecuritySeverity,
    AlertStatus,
    ApiResponse
} from '../types';

// Security search and filter options
export interface SecuritySearchOptions {
    page?: number;
    size?: number;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
    eventType?: SecurityEventType;
    severity?: SecuritySeverity;
    dateFrom?: string;
    dateTo?: string;
    source?: string;
    userId?: string;
    ipAddress?: string;
    resolved?: boolean;
}

// Security dashboard data
export interface SecurityDashboard {
    overview: {
        totalEvents: number;
        criticalAlerts: number;
        activeThreats: number;
        blockedAttacks: number;
    };
    recentEvents: SecurityEvent[];
    activeAlerts: SecurityAlert[];
    threatTrends: Array<{
        date: string;
        count: number;
        severity: SecuritySeverity;
    }>;
    topThreats: Array<{
        type: string;
        count: number;
        severity: SecuritySeverity;
    }>;
    geographicThreats: Array<{
        country: string;
        count: number;
        coordinates: [number, number];
    }>;
}

// Security configuration
export interface SecurityConfig {
    autoBlockThreats: boolean;
    alertThresholds: {
        [key in SecuritySeverity]: number;
    };
    scanSettings: {
        realTimeScanning: boolean;
        scheduledScans: boolean;
        scanFrequency: string;
    };
    networkIds: {
        enabled: boolean;
        sensitivity: 'low' | 'medium' | 'high';
        blockSuspiciousIps: boolean;
    };
    malwareDetection: {
        enabled: boolean;
        quarantineInfected: boolean;
        notifyUsers: boolean;
    };
}

// Security Service Class
class SecurityService {
    // Security events management
    async getSecurityEvents(options: SecuritySearchOptions = {}): Promise<PaginatedResponse<SecurityEvent>> {
        try {
            const params = new URLSearchParams();

            if (options.page !== undefined) params.append('page', options.page.toString());
            if (options.size !== undefined) params.append('size', options.size.toString());
            if (options.sortBy) params.append('sortBy', options.sortBy);
            if (options.sortDirection) params.append('sortDirection', options.sortDirection);
            if (options.eventType) params.append('eventType', options.eventType);
            if (options.severity) params.append('severity', options.severity);
            if (options.dateFrom) params.append('dateFrom', options.dateFrom);
            if (options.dateTo) params.append('dateTo', options.dateTo);
            if (options.source) params.append('source', options.source);
            if (options.userId) params.append('userId', options.userId);
            if (options.ipAddress) params.append('ipAddress', options.ipAddress);
            if (options.resolved !== undefined) params.append('resolved', options.resolved.toString());

            const response = await apiService.get<PaginatedResponse<SecurityEvent>>(
                `/api/security/events?${params.toString()}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch security events');
            }
        } catch (error) {
            console.error('Get security events error:', error);
            throw error;
        }
    }

    async getSecurityEventById(eventId: string): Promise<SecurityEvent> {
        try {
            const response = await apiService.get<SecurityEvent>(`/api/security/events/${eventId}`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch security event');
            }
        } catch (error) {
            console.error('Get security event by ID error:', error);
            throw error;
        }
    }

    async resolveSecurityEvent(eventId: string, resolution: string): Promise<SecurityEvent> {
        try {
            const response = await apiService.post<SecurityEvent>(`/api/security/events/${eventId}/resolve`, {
                resolution
            });

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to resolve security event');
            }
        } catch (error) {
            console.error('Resolve security event error:', error);
            throw error;
        }
    }

    // Security alerts management
    async getSecurityAlerts(
        page: number = 0,
        size: number = 20,
        status?: AlertStatus,
        severity?: SecuritySeverity
    ): Promise<PaginatedResponse<SecurityAlert>> {
        try {
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('size', size.toString());
            if (status) params.append('status', status);
            if (severity) params.append('severity', severity);

            const response = await apiService.get<PaginatedResponse<SecurityAlert>>(
                `/api/security/alerts?${params.toString()}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch security alerts');
            }
        } catch (error) {
            console.error('Get security alerts error:', error);
            throw error;
        }
    }

    async acknowledgeAlert(alertId: string): Promise<SecurityAlert> {
        try {
            const response = await apiService.post<SecurityAlert>(`/api/security/alerts/${alertId}/acknowledge`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to acknowledge alert');
            }
        } catch (error) {
            console.error('Acknowledge alert error:', error);
            throw error;
        }
    }

    async resolveAlert(alertId: string, resolution: string): Promise<SecurityAlert> {
        try {
            const response = await apiService.post<SecurityAlert>(`/api/security/alerts/${alertId}/resolve`, {
                resolution
            });

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to resolve alert');
            }
        } catch (error) {
            console.error('Resolve alert error:', error);
            throw error;
        }
    }

    async closeAlert(alertId: string): Promise<SecurityAlert> {
        try {
            const response = await apiService.post<SecurityAlert>(`/api/security/alerts/${alertId}/close`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to close alert');
            }
        } catch (error) {
            console.error('Close alert error:', error);
            throw error;
        }
    }

    // Threat intelligence
    async getThreatIntelligence(
        page: number = 0,
        size: number = 20,
        isActive?: boolean
    ): Promise<PaginatedResponse<ThreatIntelligence>> {
        try {
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('size', size.toString());
            if (isActive !== undefined) params.append('isActive', isActive.toString());

            const response = await apiService.get<PaginatedResponse<ThreatIntelligence>>(
                `/api/security/threat-intelligence?${params.toString()}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch threat intelligence');
            }
        } catch (error) {
            console.error('Get threat intelligence error:', error);
            throw error;
        }
    }

    async addThreatIndicator(indicator: Omit<ThreatIntelligence, 'id' | 'firstSeen' | 'lastSeen'>): Promise<ThreatIntelligence> {
        try {
            const response = await apiService.post<ThreatIntelligence>('/api/security/threat-intelligence', indicator);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to add threat indicator');
            }
        } catch (error) {
            console.error('Add threat indicator error:', error);
            throw error;
        }
    }

    async updateThreatIndicator(indicatorId: string, updates: Partial<ThreatIntelligence>): Promise<ThreatIntelligence> {
        try {
            const response = await apiService.put<ThreatIntelligence>(`/api/security/threat-intelligence/${indicatorId}`, updates);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to update threat indicator');
            }
        } catch (error) {
            console.error('Update threat indicator error:', error);
            throw error;
        }
    }

    async deleteThreatIndicator(indicatorId: string): Promise<void> {
        try {
            const response = await apiService.delete(`/api/security/threat-intelligence/${indicatorId}`);

            if (!response.success) {
                throw new Error(response.message || 'Failed to delete threat indicator');
            }
        } catch (error) {
            console.error('Delete threat indicator error:', error);
            throw error;
        }
    }

    // Network IDS integration
    async getNetworkEvents(
        page: number = 0,
        size: number = 20,
        severity?: SecuritySeverity
    ): Promise<PaginatedResponse<NetworkEvent>> {
        try {
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('size', size.toString());
            if (severity) params.append('severity', severity);

            const response = await apiService.get<PaginatedResponse<NetworkEvent>>(
                `/api/ids/events?${params.toString()}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch network events');
            }
        } catch (error) {
            console.error('Get network events error:', error);
            throw error;
        }
    }

    async blockIpAddress(ipAddress: string, reason: string, duration?: number): Promise<void> {
        try {
            const response = await apiService.post('/api/ids/block-ip', {
                ipAddress,
                reason,
                duration
            });

            if (!response.success) {
                throw new Error(response.message || 'Failed to block IP address');
            }
        } catch (error) {
            console.error('Block IP address error:', error);
            throw error;
        }
    }

    async unblockIpAddress(ipAddress: string): Promise<void> {
        try {
            const response = await apiService.post('/api/ids/unblock-ip', {
                ipAddress
            });

            if (!response.success) {
                throw new Error(response.message || 'Failed to unblock IP address');
            }
        } catch (error) {
            console.error('Unblock IP address error:', error);
            throw error;
        }
    }

    async getBlockedIps(): Promise<Array<{
        ipAddress: string;
        reason: string;
        blockedAt: string;
        expiresAt?: string;
        isActive: boolean;
    }>> {
        try {
            const response = await apiService.get<Array<{
                ipAddress: string;
                reason: string;
                blockedAt: string;
                expiresAt?: string;
                isActive: boolean;
            }>>('/api/ids/blocked-ips');

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch blocked IPs');
            }
        } catch (error) {
            console.error('Get blocked IPs error:', error);
            throw error;
        }
    }

    // Malware detection
    async getMalwareAnalyses(
        page: number = 0,
        size: number = 20,
        status?: string
    ): Promise<PaginatedResponse<MalwareAnalysis>> {
        try {
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('size', size.toString());
            if (status) params.append('status', status);

            const response = await apiService.get<PaginatedResponse<MalwareAnalysis>>(
                `/api/malware/analyses?${params.toString()}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch malware analyses');
            }
        } catch (error) {
            console.error('Get malware analyses error:', error);
            throw error;
        }
    }

    async scanFile(fileId: string): Promise<MalwareAnalysis> {
        try {
            const response = await apiService.post<MalwareAnalysis>('/api/malware/scan', {
                fileId
            });

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to scan file');
            }
        } catch (error) {
            console.error('Scan file error:', error);
            throw error;
        }
    }

    async getAnalysisResult(analysisId: string): Promise<MalwareAnalysis> {
        try {
            const response = await apiService.get<MalwareAnalysis>(`/api/malware/analyses/${analysisId}`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to get analysis result');
            }
        } catch (error) {
            console.error('Get analysis result error:', error);
            throw error;
        }
    }

    // Security dashboard
    async getSecurityDashboard(): Promise<SecurityDashboard> {
        try {
            const response = await apiService.get<SecurityDashboard>('/api/security/dashboard');

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch security dashboard');
            }
        } catch (error) {
            console.error('Get security dashboard error:', error);
            throw error;
        }
    }

    // Security configuration
    async getSecurityConfig(): Promise<SecurityConfig> {
        try {
            const response = await apiService.get<SecurityConfig>('/api/security/config');

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch security configuration');
            }
        } catch (error) {
            console.error('Get security config error:', error);
            throw error;
        }
    }

    async updateSecurityConfig(config: Partial<SecurityConfig>): Promise<SecurityConfig> {
        try {
            const response = await apiService.put<SecurityConfig>('/api/security/config', config);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to update security configuration');
            }
        } catch (error) {
            console.error('Update security config error:', error);
            throw error;
        }
    }

    // Security reports
    async generateSecurityReport(
        type: 'events' | 'alerts' | 'threats' | 'comprehensive',
        dateFrom: string,
        dateTo: string,
        format: 'pdf' | 'csv' | 'xlsx' = 'pdf'
    ): Promise<void> {
        try {
            const params = new URLSearchParams();
            params.append('type', type);
            params.append('dateFrom', dateFrom);
            params.append('dateTo', dateTo);
            params.append('format', format);

            await apiService.downloadFile(
                `/api/security/reports?${params.toString()}`,
                `security_report_${type}_${dateFrom}_${dateTo}.${format}`
            );
        } catch (error) {
            console.error('Generate security report error:', error);
            throw error;
        }
    }

    // Security statistics
    async getSecurityStatistics(period: 'day' | 'week' | 'month' | 'year' = 'week'): Promise<{
        totalEvents: number;
        eventsByType: Record<SecurityEventType, number>;
        eventsBySeverity: Record<SecuritySeverity, number>;
        alertsGenerated: number;
        threatsBlocked: number;
        malwareDetected: number;
        networkAttacks: number;
        trends: Array<{
            date: string;
            events: number;
            alerts: number;
            threats: number;
        }>;
    }> {
        try {
            const response = await apiService.get<{
                totalEvents: number;
                eventsByType: Record<SecurityEventType, number>;
                eventsBySeverity: Record<SecuritySeverity, number>;
                alertsGenerated: number;
                threatsBlocked: number;
                malwareDetected: number;
                networkAttacks: number;
                trends: Array<{
                    date: string;
                    events: number;
                    alerts: number;
                    threats: number;
                }>;
            }>(`/api/security/statistics?period=${period}`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch security statistics');
            }
        } catch (error) {
            console.error('Get security statistics error:', error);
            throw error;
        }
    }

    // Utility methods
    getSeverityColor(severity: SecuritySeverity): string {
        const colorMap: Record<SecuritySeverity, string> = {
            [SecuritySeverity.LOW]: '#52c41a',
            [SecuritySeverity.MEDIUM]: '#faad14',
            [SecuritySeverity.HIGH]: '#fa8c16',
            [SecuritySeverity.CRITICAL]: '#f5222d'
        };
        return colorMap[severity] || '#d9d9d9';
    }

    getSeverityIcon(severity: SecuritySeverity): string {
        const iconMap: Record<SecuritySeverity, string> = {
            [SecuritySeverity.LOW]: '🟢',
            [SecuritySeverity.MEDIUM]: '🟡',
            [SecuritySeverity.HIGH]: '🟠',
            [SecuritySeverity.CRITICAL]: '🔴'
        };
        return iconMap[severity] || '⚪';
    }

    getEventTypeIcon(eventType: SecurityEventType): string {
        const iconMap: Record<SecurityEventType, string> = {
            [SecurityEventType.LOGIN_ATTEMPT]: '🔐',
            [SecurityEventType.LOGIN_SUCCESS]: '✅',
            [SecurityEventType.LOGIN_FAILURE]: '❌',
            [SecurityEventType.UNAUTHORIZED_ACCESS]: '🚫',
            [SecurityEventType.MALWARE_DETECTED]: '🦠',
            [SecurityEventType.SUSPICIOUS_ACTIVITY]: '⚠️',
            [SecurityEventType.BRUTE_FORCE]: '🔨',
            [SecurityEventType.DATA_BREACH]: '💥',
            [SecurityEventType.SYSTEM_INTRUSION]: '🏴‍☠️'
        };
        return iconMap[eventType] || '🔍';
    }

    formatEventDescription(event: SecurityEvent): string {
        const { type, source, target, description } = event;

        switch (type) {
            case SecurityEventType.LOGIN_SUCCESS:
                return `Successful login from ${source}`;
            case SecurityEventType.LOGIN_FAILURE:
                return `Failed login attempt from ${source}`;
            case SecurityEventType.MALWARE_DETECTED:
                return `Malware detected in ${target || 'file'}`;
            case SecurityEventType.UNAUTHORIZED_ACCESS:
                return `Unauthorized access attempt to ${target || 'resource'}`;
            default:
                return description;
        }
    }
}

// Export singleton instance
export const securityService = new SecurityService();
export default securityService;