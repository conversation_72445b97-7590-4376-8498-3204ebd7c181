package com.securecloudstorage.monitoring.service;

import com.securecloudstorage.monitoring.dto.AlertResponse;
import com.securecloudstorage.monitoring.entity.Alert;
import com.securecloudstorage.monitoring.repository.AlertRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Alert Service
 * Dịch vụ quản lý alerts và notifications
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlertService {

    private final AlertRepository alertRepository;
    private final NotificationService notificationService;

    /**
     * Lấy danh sách alerts
     */
    public List<AlertResponse> getAlerts(String status, String severity, String service) {
        try {
            List<Alert> alerts;
            
            if (status != null && severity != null && service != null) {
                alerts = alertRepository.findByStatusAndSeverityAndServiceOrderByCreatedAtDesc(
                    status, severity, service);
            } else if (status != null && severity != null) {
                alerts = alertRepository.findByStatusAndSeverityOrderByCreatedAtDesc(status, severity);
            } else if (status != null) {
                alerts = alertRepository.findByStatusOrderByCreatedAtDesc(status);
            } else if (severity != null) {
                alerts = alertRepository.findBySeverityOrderByCreatedAtDesc(severity);
            } else if (service != null) {
                alerts = alertRepository.findByServiceOrderByCreatedAtDesc(service);
            } else {
                alerts = alertRepository.findAllByOrderByCreatedAtDesc();
            }
            
            return alerts.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("Error getting alerts: ", e);
            return new ArrayList<>();
        }
    }

    /**
     * Tạo alert mới
     */
    @Transactional
    public AlertResponse createAlert(Map<String, Object> alertData) {
        try {
            Alert alert = new Alert();
            
            // Set basic fields
            alert.setAlertId(UUID.randomUUID().toString());
            alert.setTitle((String) alertData.get("title"));
            alert.setDescription((String) alertData.get("description"));
            alert.setSeverity((String) alertData.get("severity"));
            alert.setStatus("OPEN");
            alert.setType((String) alertData.get("type"));
            alert.setService((String) alertData.get("service"));
            alert.setSource((String) alertData.get("source"));
            alert.setEnvironment("production");
            alert.setCreatedAt(LocalDateTime.now());
            
            // Set priority based on severity
            alert.setPriority(calculatePriority(alert.getSeverity()));
            
            // Set metadata
            alert.setMetadata(convertMapToString(alertData));
            
            // Set SLA deadline
            alert.setSlaDeadline(calculateSlaDeadline(alert.getSeverity()));
            
            // Save alert
            alert = alertRepository.save(alert);
            
            // Send notification
            sendAlertNotification(alert);
            
            // Auto-assign if needed
            autoAssignAlert(alert);
            
            AlertResponse response = convertToResponse(alert);
            
            log.info("Alert created: {} - {} - {}", 
                alert.getAlertId(), alert.getSeverity(), alert.getTitle());
            
            return response;
            
        } catch (Exception e) {
            log.error("Error creating alert: ", e);
            throw new RuntimeException("Failed to create alert", e);
        }
    }

    /**
     * Acknowledge alert
     */
    @Transactional
    public void acknowledgeAlert(Long alertId) {
        try {
            Alert alert = alertRepository.findById(alertId)
                .orElseThrow(() -> new RuntimeException("Alert not found"));
            
            alert.setStatus("ACKNOWLEDGED");
            alert.setAcknowledgedAt(LocalDateTime.now());
            alert.setAcknowledgedBy("system"); // In real implementation, get from security context
            alert.setUpdatedAt(LocalDateTime.now());
            
            alertRepository.save(alert);
            
            log.info("Alert acknowledged: {}", alert.getAlertId());
            
        } catch (Exception e) {
            log.error("Error acknowledging alert {}: ", alertId, e);
            throw new RuntimeException("Failed to acknowledge alert", e);
        }
    }

    /**
     * Resolve alert
     */
    @Transactional
    public void resolveAlert(Long alertId) {
        try {
            Alert alert = alertRepository.findById(alertId)
                .orElseThrow(() -> new RuntimeException("Alert not found"));
            
            alert.setStatus("RESOLVED");
            alert.setResolvedAt(LocalDateTime.now());
            alert.setResolvedBy("system"); // In real implementation, get from security context
            alert.setUpdatedAt(LocalDateTime.now());
            
            // Calculate resolution time
            if (alert.getCreatedAt() != null) {
                long duration = java.time.Duration.between(alert.getCreatedAt(), alert.getResolvedAt()).toSeconds();
                alert.setDuration(duration);
            }
            
            alertRepository.save(alert);
            
            log.info("Alert resolved: {}", alert.getAlertId());
            
        } catch (Exception e) {
            log.error("Error resolving alert {}: ", alertId, e);
            throw new RuntimeException("Failed to resolve alert", e);
        }
    }

    /**
     * Auto-resolve alerts based on conditions
     */
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void autoResolveAlerts() {
        try {
            log.debug("Starting auto-resolve check...");
            
            List<Alert> openAlerts = alertRepository.findByStatusIn(Arrays.asList("OPEN", "ACKNOWLEDGED"));
            
            for (Alert alert : openAlerts) {
                if (shouldAutoResolve(alert)) {
                    alert.setStatus("RESOLVED");
                    alert.setResolvedAt(LocalDateTime.now());
                    alert.setResolvedBy("auto-resolve");
                    alert.setIsAutoResolved(true);
                    alert.setAutoResolutionReason("Condition no longer met");
                    
                    alertRepository.save(alert);
                    
                    log.info("Auto-resolved alert: {}", alert.getAlertId());
                }
            }
            
        } catch (Exception e) {
            log.error("Error during auto-resolve: ", e);
        }
    }

    /**
     * Check for escalation
     */
    @Scheduled(fixedRate = 600000) // Every 10 minutes
    public void checkEscalation() {
        try {
            log.debug("Starting escalation check...");
            
            List<Alert> alerts = alertRepository.findByStatusInAndSlaDeadlineBefore(
                Arrays.asList("OPEN", "ACKNOWLEDGED"), LocalDateTime.now());
            
            for (Alert alert : alerts) {
                escalateAlert(alert);
            }
            
        } catch (Exception e) {
            log.error("Error during escalation check: ", e);
        }
    }

    /**
     * Get alert statistics
     */
    public Map<String, Object> getAlertStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // Total alerts
            stats.put("totalAlerts", alertRepository.count());
            
            // Alerts by status
            stats.put("openAlerts", alertRepository.countByStatus("OPEN"));
            stats.put("acknowledgedAlerts", alertRepository.countByStatus("ACKNOWLEDGED"));
            stats.put("resolvedAlerts", alertRepository.countByStatus("RESOLVED"));
            stats.put("closedAlerts", alertRepository.countByStatus("CLOSED"));
            
            // Alerts by severity
            stats.put("criticalAlerts", alertRepository.countBySeverity("CRITICAL"));
            stats.put("highAlerts", alertRepository.countBySeverity("HIGH"));
            stats.put("mediumAlerts", alertRepository.countBySeverity("MEDIUM"));
            stats.put("lowAlerts", alertRepository.countBySeverity("LOW"));
            
            // Recent alerts
            LocalDateTime last24Hours = LocalDateTime.now().minusDays(1);
            stats.put("alertsLast24Hours", alertRepository.countByCreatedAtAfter(last24Hours));
            
            LocalDateTime lastWeek = LocalDateTime.now().minusDays(7);
            stats.put("alertsLastWeek", alertRepository.countByCreatedAtAfter(lastWeek));
            
            // Average resolution time
            stats.put("averageResolutionTime", alertRepository.getAverageResolutionTime());
            
            // Top services with alerts
            stats.put("topServices", getTopServicesWithAlerts());
            
            // Alert trends
            stats.put("alertTrends", getAlertTrends());
            
            return stats;
            
        } catch (Exception e) {
            log.error("Error getting alert statistics: ", e);
            stats.put("error", "Failed to get alert statistics");
            return stats;
        }
    }

    /**
     * Cleanup old alerts
     */
    @Scheduled(cron = "0 0 2 * * ?") // Daily at 2 AM
    public void cleanupOldAlerts() {
        try {
            log.info("Starting alert cleanup...");
            
            // Delete resolved alerts older than 30 days
            LocalDateTime cutoff = LocalDateTime.now().minusDays(30);
            List<Alert> oldResolvedAlerts = alertRepository.findByStatusAndResolvedAtBefore("RESOLVED", cutoff);
            
            for (Alert alert : oldResolvedAlerts) {
                alertRepository.delete(alert);
            }
            
            log.info("Cleaned up {} old alerts", oldResolvedAlerts.size());
            
        } catch (Exception e) {
            log.error("Error during alert cleanup: ", e);
        }
    }

    /**
     * Create system alert
     */
    public void createSystemAlert(String title, String description, String severity, String service) {
        try {
            Map<String, Object> alertData = new HashMap<>();
            alertData.put("title", title);
            alertData.put("description", description);
            alertData.put("severity", severity);
            alertData.put("service", service);
            alertData.put("type", "SYSTEM");
            alertData.put("source", "monitoring-service");
            
            createAlert(alertData);
            
        } catch (Exception e) {
            log.error("Error creating system alert: ", e);
        }
    }

    /**
     * Create performance alert
     */
    public void createPerformanceAlert(String service, String metric, double threshold, double currentValue) {
        try {
            Map<String, Object> alertData = new HashMap<>();
            alertData.put("title", "Performance Alert: " + metric);
            alertData.put("description", String.format("Service %s has %s of %.2f, exceeding threshold of %.2f", 
                service, metric, currentValue, threshold));
            alertData.put("severity", currentValue > threshold * 2 ? "CRITICAL" : "HIGH");
            alertData.put("service", service);
            alertData.put("type", "PERFORMANCE");
            alertData.put("source", "monitoring-service");
            alertData.put("metricName", metric);
            alertData.put("threshold", threshold);
            alertData.put("currentValue", currentValue);
            
            createAlert(alertData);
            
        } catch (Exception e) {
            log.error("Error creating performance alert: ", e);
        }
    }

    // Private helper methods

    private AlertResponse convertToResponse(Alert alert) {
        AlertResponse response = new AlertResponse();
        
        response.setId(alert.getId());
        response.setAlertId(alert.getAlertId());
        response.setTitle(alert.getTitle());
        response.setDescription(alert.getDescription());
        response.setSeverity(alert.getSeverity());
        response.setStatus(alert.getStatus());
        response.setType(alert.getType());
        response.setService(alert.getService());
        response.setSource(alert.getSource());
        response.setEnvironment(alert.getEnvironment());
        response.setCreatedAt(alert.getCreatedAt());
        response.setUpdatedAt(alert.getUpdatedAt());
        response.setAcknowledgedAt(alert.getAcknowledgedAt());
        response.setResolvedAt(alert.getResolvedAt());
        response.setClosedAt(alert.getClosedAt());
        response.setDuration(alert.getDuration());
        response.setAssignedTo(alert.getAssignedTo());
        response.setAcknowledgedBy(alert.getAcknowledgedBy());
        response.setResolvedBy(alert.getResolvedBy());
        response.setPriority(alert.getPriority());
        response.setResolution(alert.getResolution());
        response.setNotes(alert.getNotes());
        response.setIsNotified(alert.getIsNotified());
        response.setIsAutoResolved(alert.getIsAutoResolved());
        response.setAutoResolutionReason(alert.getAutoResolutionReason());
        response.setSlaDeadline(alert.getSlaDeadline());
        
        return response;
    }

    private Integer calculatePriority(String severity) {
        switch (severity.toUpperCase()) {
            case "CRITICAL": return 1;
            case "HIGH": return 2;
            case "MEDIUM": return 3;
            case "LOW": return 4;
            default: return 5;
        }
    }

    private String convertMapToString(Map<String, Object> map) {
        // Convert map to JSON string
        // In real implementation, use Jackson or Gson
        return map.toString();
    }

    private LocalDateTime calculateSlaDeadline(String severity) {
        LocalDateTime now = LocalDateTime.now();
        
        switch (severity.toUpperCase()) {
            case "CRITICAL": return now.plusHours(1);
            case "HIGH": return now.plusHours(4);
            case "MEDIUM": return now.plusHours(24);
            case "LOW": return now.plusDays(7);
            default: return now.plusDays(1);
        }
    }

    private void sendAlertNotification(Alert alert) {
        try {
            notificationService.sendAlertNotification(alert);
            
            alert.setIsNotified(true);
            alert.setLastNotificationAt(LocalDateTime.now());
            alert.setNotificationCount(1);
            
            alertRepository.save(alert);
            
        } catch (Exception e) {
            log.error("Error sending alert notification: ", e);
        }
    }

    private void autoAssignAlert(Alert alert) {
        try {
            // Auto-assign based on service and severity
            String assignee = getAssigneeForAlert(alert);
            
            if (assignee != null) {
                alert.setAssignedTo(assignee);
                alert.setUpdatedAt(LocalDateTime.now());
                alertRepository.save(alert);
                
                log.info("Auto-assigned alert {} to {}", alert.getAlertId(), assignee);
            }
            
        } catch (Exception e) {
            log.error("Error auto-assigning alert: ", e);
        }
    }

    private String getAssigneeForAlert(Alert alert) {
        // Auto-assignment logic based on service and severity
        if ("security-service".equals(alert.getService())) {
            return "security-team";
        } else if ("CRITICAL".equals(alert.getSeverity())) {
            return "on-call-engineer";
        } else if ("storage-service".equals(alert.getService())) {
            return "storage-team";
        }
        
        return null;
    }

    private boolean shouldAutoResolve(Alert alert) {
        try {
            // Check if the condition that triggered the alert is still present
            
            // For performance alerts, check if metric is back to normal
            if ("PERFORMANCE".equals(alert.getType())) {
                // Mock check - in real implementation, check actual metrics
                return Math.random() > 0.8; // 20% chance to auto-resolve
            }
            
            // For service down alerts, check if service is back up
            if ("SERVICE_DOWN".equals(alert.getType())) {
                // Mock check - in real implementation, check service health
                return Math.random() > 0.9; // 10% chance to auto-resolve
            }
            
            // For old alerts, auto-resolve after certain time
            if (alert.getCreatedAt().isBefore(LocalDateTime.now().minusDays(7))) {
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("Error checking auto-resolve condition: ", e);
            return false;
        }
    }

    private void escalateAlert(Alert alert) {
        try {
            String currentLevel = alert.getEscalationLevel();
            String nextLevel = getNextEscalationLevel(currentLevel);
            
            if (nextLevel != null) {
                alert.setEscalationLevel(nextLevel);
                alert.setEscalatedAt(LocalDateTime.now());
                alert.setEscalatedTo(getEscalationTarget(nextLevel));
                alert.setUpdatedAt(LocalDateTime.now());
                
                // Update priority
                alert.setPriority(Math.max(1, alert.getPriority() - 1));
                
                alertRepository.save(alert);
                
                // Send escalation notification
                notificationService.sendEscalationNotification(alert);
                
                log.warn("Escalated alert {} to level {}", alert.getAlertId(), nextLevel);
            }
            
        } catch (Exception e) {
            log.error("Error escalating alert: ", e);
        }
    }

    private String getNextEscalationLevel(String currentLevel) {
        switch (currentLevel) {
            case null:
            case "L1": return "L2";
            case "L2": return "L3";
            case "L3": return "MANAGEMENT";
            default: return null;
        }
    }

    private String getEscalationTarget(String level) {
        switch (level) {
            case "L2": return "senior-engineer";
            case "L3": return "team-lead";
            case "MANAGEMENT": return "engineering-manager";
            default: return null;
        }
    }

    private List<Map<String, Object>> getTopServicesWithAlerts() {
        List<Object[]> results = alertRepository.getTopServicesWithAlerts();
        
        return results.stream()
            .map(row -> {
                Map<String, Object> service = new HashMap<>();
                service.put("service", row[0]);
                service.put("alertCount", row[1]);
                return service;
            })
            .collect(Collectors.toList());
    }

    private List<Map<String, Object>> getAlertTrends() {
        List<Map<String, Object>> trends = new ArrayList<>();
        
        // Get daily alert counts for the last 7 days
        for (int i = 0; i < 7; i++) {
            LocalDateTime date = LocalDateTime.now().minusDays(i);
            LocalDateTime dayStart = date.toLocalDate().atStartOfDay();
            LocalDateTime dayEnd = dayStart.plusDays(1);
            
            long alertCount = alertRepository.countByCreatedAtBetween(dayStart, dayEnd);
            
            Map<String, Object> trend = new HashMap<>();
            trend.put("date", dayStart.toLocalDate());
            trend.put("alertCount", alertCount);
            trends.add(trend);
        }
        
        return trends;
    }
}
