rule Malware_Generic
{
    meta:
        description = "Generic malware detection rule"
        author = "AI Malware Detection System"
        date = "2025-01-01"
        
    strings:
        $a = "CreateRemoteThread"
        $b = "WriteProcessMemory"
        $c = "VirtualAlloc"
        $d = "GetProcAddress"
        $e = "LoadLibrary"
        $f = "RegOpenKeyEx"
        $g = "RegSetValueEx"
        $h = "CreateMutex"
        $i = "InternetOpen"
        $j = "HttpOpenRequest"
        
    condition:
        3 of them
}

rule Trojan_Downloader
{
    meta:
        description = "Trojan downloader detection"
        author = "AI Malware Detection System"
        
    strings:
        $url1 = "http://" nocase
        $url2 = "https://" nocase
        $download1 = "URLDownloadToFile"
        $download2 = "WinHttpRequest"
        $download3 = "InternetReadFile"
        $exec1 = "CreateProcess"
        $exec2 = "ShellExecute"
        
    condition:
        (1 of ($url*)) and (1 of ($download*)) and (1 of ($exec*))
}

rule Keylogger_Detection
{
    meta:
        description = "Keylogger detection rule"
        author = "AI Malware Detection System"
        
    strings:
        $hook1 = "SetWindowsHookEx"
        $hook2 = "GetAsyncKeyState"
        $hook3 = "GetKeyboardState"
        $log1 = "CreateFile"
        $log2 = "WriteFile"
        $hide1 = "ShowWindow"
        $hide2 = "SetWindowPos"
        
    condition:
        (1 of ($hook*)) and (1 of ($log*)) and (1 of ($hide*))
}

rule Ransomware_Detection
{
    meta:
        description = "Ransomware detection rule"
        author = "AI Malware Detection System"
        
    strings:
        $crypt1 = "CryptEncrypt"
        $crypt2 = "CryptDecrypt"
        $crypt3 = "CryptGenKey"
        $file1 = "FindFirstFile"
        $file2 = "FindNextFile"
        $ransom1 = "Your files have been encrypted"
        $ransom2 = "Pay the ransom"
        $ransom3 = "Bitcoin"
        $ext1 = ".encrypted"
        $ext2 = ".locked"
        
    condition:
        (1 of ($crypt*)) and (1 of ($file*)) and (1 of ($ransom*) or 1 of ($ext*))
}

rule Backdoor_Detection
{
    meta:
        description = "Backdoor detection rule"
        author = "AI Malware Detection System"
        
    strings:
        $net1 = "WSAStartup"
        $net2 = "socket"
        $net3 = "connect"
        $net4 = "bind"
        $net5 = "listen"
        $shell1 = "cmd.exe"
        $shell2 = "powershell"
        $shell3 = "CreatePipe"
        $shell4 = "CreateProcess"
        
    condition:
        (2 of ($net*)) and (1 of ($shell*))
}
