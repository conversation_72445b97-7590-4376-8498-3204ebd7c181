package com.securecloudstorage.security.controller;

import com.securecloudstorage.security.dto.SecurityEventRequest;
import com.securecloudstorage.security.dto.SecurityEventResponse;
import com.securecloudstorage.security.dto.SecurityStatsResponse;
import com.securecloudstorage.security.service.SecurityAuditService;
import com.securecloudstorage.security.service.ThreatDetectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * Security Controller
 * Xử lý security events, audit logs, và threat detection
 */
@RestController
@RequestMapping("/api/security")
@RequiredArgsConstructor
@Slf4j
public class SecurityController {

    private final SecurityAuditService auditService;
    private final ThreatDetectionService threatDetectionService;

    /**
     * Record security event
     */
    @PostMapping("/events")
    public ResponseEntity<SecurityEventResponse> recordEvent(
            @RequestBody SecurityEventRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            // Enrich request with client info
            request.setIpAddress(getClientIp(httpRequest));
            request.setUserAgent(httpRequest.getHeader("User-Agent"));
            
            SecurityEventResponse response = auditService.recordEvent(request);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error recording security event: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get security events
     */
    @GetMapping("/events")
    public ResponseEntity<Page<SecurityEventResponse>> getEvents(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "severity", required = false) String severity,
            @RequestParam(value = "eventType", required = false) String eventType,
            @RequestParam(value = "userId", required = false) String userId) {
        
        try {
            Page<SecurityEventResponse> events = auditService.getEvents(
                PageRequest.of(page, size), severity, eventType, userId);
            return ResponseEntity.ok(events);
            
        } catch (Exception e) {
            log.error("Error getting security events: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get security statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<SecurityStatsResponse> getSecurityStats() {
        try {
            SecurityStatsResponse stats = auditService.getSecurityStats();
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            log.error("Error getting security stats: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Analyze IP for threats
     */
    @PostMapping("/analyze-ip")
    public ResponseEntity<Map<String, Object>> analyzeIp(
            @RequestBody Map<String, String> request) {
        
        try {
            String ipAddress = request.get("ipAddress");
            Map<String, Object> analysis = threatDetectionService.analyzeIp(ipAddress);
            return ResponseEntity.ok(analysis);
            
        } catch (Exception e) {
            log.error("Error analyzing IP: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get blocked IPs
     */
    @GetMapping("/blocked-ips")
    public ResponseEntity<List<String>> getBlockedIps() {
        try {
            List<String> blockedIps = threatDetectionService.getBlockedIps();
            return ResponseEntity.ok(blockedIps);
            
        } catch (Exception e) {
            log.error("Error getting blocked IPs: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Block IP address
     */
    @PostMapping("/block-ip")
    public ResponseEntity<String> blockIp(
            @RequestBody Map<String, String> request) {
        
        try {
            String ipAddress = request.get("ipAddress");
            String reason = request.get("reason");
            
            threatDetectionService.blockIp(ipAddress, reason);
            return ResponseEntity.ok("IP blocked successfully");
            
        } catch (Exception e) {
            log.error("Error blocking IP: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Unblock IP address
     */
    @PostMapping("/unblock-ip")
    public ResponseEntity<String> unblockIp(
            @RequestBody Map<String, String> request) {
        
        try {
            String ipAddress = request.get("ipAddress");
            
            threatDetectionService.unblockIp(ipAddress);
            return ResponseEntity.ok("IP unblocked successfully");
            
        } catch (Exception e) {
            log.error("Error unblocking IP: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get security alerts
     */
    @GetMapping("/alerts")
    public ResponseEntity<List<Map<String, Object>>> getAlerts() {
        try {
            List<Map<String, Object>> alerts = auditService.getActiveAlerts();
            return ResponseEntity.ok(alerts);
            
        } catch (Exception e) {
            log.error("Error getting security alerts: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get user security profile
     */
    @GetMapping("/profile/{userId}")
    public ResponseEntity<Map<String, Object>> getUserSecurityProfile(
            @PathVariable String userId) {
        
        try {
            Map<String, Object> profile = auditService.getUserSecurityProfile(userId);
            return ResponseEntity.ok(profile);
            
        } catch (Exception e) {
            log.error("Error getting user security profile: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Validate security token
     */
    @PostMapping("/validate-token")
    public ResponseEntity<Map<String, Object>> validateToken(
            @RequestBody Map<String, String> request) {
        
        try {
            String token = request.get("token");
            Map<String, Object> result = auditService.validateSecurityToken(token);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Error validating security token: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get threat intelligence
     */
    @GetMapping("/threat-intelligence")
    public ResponseEntity<Map<String, Object>> getThreatIntelligence() {
        try {
            Map<String, Object> intelligence = threatDetectionService.getThreatIntelligence();
            return ResponseEntity.ok(intelligence);
            
        } catch (Exception e) {
            log.error("Error getting threat intelligence: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
