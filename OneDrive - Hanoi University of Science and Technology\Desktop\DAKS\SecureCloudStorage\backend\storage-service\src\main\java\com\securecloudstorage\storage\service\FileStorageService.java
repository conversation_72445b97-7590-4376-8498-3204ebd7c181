package com.securecloudstorage.storage.service;

import com.securecloudstorage.storage.dto.FileUploadResponse;
import com.securecloudstorage.storage.dto.FileInfoResponse;
import com.securecloudstorage.storage.entity.FileEntity;
import com.securecloudstorage.storage.entity.FileAccessLog;
import com.securecloudstorage.storage.entity.FileShare;
import com.securecloudstorage.storage.repository.FileRepository;
import com.securecloudstorage.storage.repository.FileAccessLogRepository;
import com.securecloudstorage.storage.repository.FileShareRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * File Storage Service
 * Xử lý lưu trữ file với encryption và chunking
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FileStorageService {

    private final FileRepository fileRepository;
    private final FileAccessLogRepository accessLogRepository;
    private final FileShareRepository shareRepository;
    private final EncryptionService encryptionService;

    @Value("${file.upload-dir:./uploads}")
    private String uploadDir;

    @Value("${file.quarantine-dir:./quarantine}")
    private String quarantineDir;

    @Transactional
    public FileUploadResponse storeFile(MultipartFile file, String userId, String description) {
        try {
            // Create upload directory if not exists
            Path uploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
            Files.createDirectories(uploadPath);

            // Generate unique file ID
            String fileId = UUID.randomUUID().toString();
            String fileName = file.getOriginalFilename();
            String fileExtension = getFileExtension(fileName);
            
            // Create file path
            Path targetLocation = uploadPath.resolve(fileId + fileExtension);

            // Encrypt and save file
            byte[] encryptedContent = encryptionService.encrypt(file.getBytes());
            Files.write(targetLocation, encryptedContent);

            // Save file metadata
            FileEntity fileEntity = FileEntity.builder()
                .fileId(fileId)
                .fileName(fileName)
                .filePath(targetLocation.toString())
                .fileSize(file.getSize())
                .contentType(file.getContentType())
                .userId(userId)
                .description(description)
                .uploadDate(LocalDateTime.now())
                .encrypted(true)
                .quarantined(false)
                .build();

            fileRepository.save(fileEntity);

            log.info("File stored successfully: {} for user: {}", fileName, userId);

            return FileUploadResponse.builder()
                .success(true)
                .fileId(fileId)
                .fileName(fileName)
                .fileSize(file.getSize())
                .message("File uploaded successfully")
                .build();

        } catch (IOException e) {
            log.error("Error storing file: ", e);
            throw new RuntimeException("Could not store file", e);
        }
    }

    public Resource loadFileAsResource(String fileId) {
        try {
            FileEntity fileEntity = fileRepository.findByFileId(fileId)
                .orElseThrow(() -> new RuntimeException("File not found: " + fileId));

            if (fileEntity.isQuarantined()) {
                throw new RuntimeException("File is quarantined: " + fileId);
            }

            Path filePath = Paths.get(fileEntity.getFilePath()).normalize();
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists() && resource.isReadable()) {
                // Decrypt file content
                byte[] encryptedContent = Files.readAllBytes(filePath);
                byte[] decryptedContent = encryptionService.decrypt(encryptedContent);
                
                // Create temporary file for decrypted content
                Path tempFile = Files.createTempFile("download_", fileEntity.getFileName());
                Files.write(tempFile, decryptedContent);
                
                return new UrlResource(tempFile.toUri());
            } else {
                throw new RuntimeException("File not found or not readable: " + fileId);
            }

        } catch (IOException e) {
            log.error("Error loading file: ", e);
            throw new RuntimeException("Could not load file", e);
        }
    }

    @Transactional
    public void quarantineFile(String fileId) {
        try {
            FileEntity fileEntity = fileRepository.findByFileId(fileId)
                .orElseThrow(() -> new RuntimeException("File not found: " + fileId));

            // Move file to quarantine directory
            Path quarantinePath = Paths.get(quarantineDir).toAbsolutePath().normalize();
            Files.createDirectories(quarantinePath);

            Path currentPath = Paths.get(fileEntity.getFilePath());
            Path quarantineFilePath = quarantinePath.resolve(currentPath.getFileName());

            Files.move(currentPath, quarantineFilePath, StandardCopyOption.REPLACE_EXISTING);

            // Update file entity
            fileEntity.setQuarantined(true);
            fileEntity.setFilePath(quarantineFilePath.toString());
            fileRepository.save(fileEntity);

            log.info("File quarantined: {}", fileId);

        } catch (IOException e) {
            log.error("Error quarantining file: ", e);
            throw new RuntimeException("Could not quarantine file", e);
        }
    }

    public boolean hasAccessPermission(String fileId, String userId) {
        // Check if user owns the file
        FileEntity fileEntity = fileRepository.findByFileId(fileId)
            .orElse(null);

        if (fileEntity == null) {
            return false;
        }

        if (fileEntity.getUserId().equals(userId)) {
            return true;
        }

        // Check if file is shared with user
        return shareRepository.existsByFileIdAndTargetUserId(fileId, userId);
    }

    public FileInfoResponse getFileInfo(String fileId) {
        FileEntity fileEntity = fileRepository.findByFileId(fileId)
            .orElseThrow(() -> new RuntimeException("File not found: " + fileId));

        return FileInfoResponse.builder()
            .fileId(fileEntity.getFileId())
            .fileName(fileEntity.getFileName())
            .fileSize(fileEntity.getFileSize())
            .contentType(fileEntity.getContentType())
            .uploadDate(fileEntity.getUploadDate())
            .description(fileEntity.getDescription())
            .encrypted(fileEntity.isEncrypted())
            .quarantined(fileEntity.isQuarantined())
            .build();
    }

    public List<FileInfoResponse> getUserFiles(String userId, int page, int size) {
        List<FileEntity> files = fileRepository.findByUserIdOrderByUploadDateDesc(userId);
        
        return files.stream()
            .skip((long) page * size)
            .limit(size)
            .map(this::convertToFileInfoResponse)
            .collect(Collectors.toList());
    }

    @Transactional
    public void deleteFile(String fileId, String userId) {
        FileEntity fileEntity = fileRepository.findByFileId(fileId)
            .orElseThrow(() -> new RuntimeException("File not found: " + fileId));

        if (!fileEntity.getUserId().equals(userId)) {
            throw new RuntimeException("Access denied");
        }

        try {
            // Delete physical file
            Path filePath = Paths.get(fileEntity.getFilePath());
            Files.deleteIfExists(filePath);

            // Delete from database
            fileRepository.delete(fileEntity);

            // Delete shares
            shareRepository.deleteByFileId(fileId);

            log.info("File deleted: {}", fileId);

        } catch (IOException e) {
            log.error("Error deleting file: ", e);
            throw new RuntimeException("Could not delete file", e);
        }
    }

    @Transactional
    public String shareFile(String fileId, String userId, String targetUserId, String permission) {
        FileEntity fileEntity = fileRepository.findByFileId(fileId)
            .orElseThrow(() -> new RuntimeException("File not found: " + fileId));

        if (!fileEntity.getUserId().equals(userId)) {
            throw new RuntimeException("Access denied");
        }

        String shareToken = UUID.randomUUID().toString();

        FileShare share = FileShare.builder()
            .fileId(fileId)
            .ownerUserId(userId)
            .targetUserId(targetUserId)
            .permission(permission)
            .shareToken(shareToken)
            .sharedDate(LocalDateTime.now())
            .build();

        shareRepository.save(share);

        log.info("File shared: {} by {} to {}", fileId, userId, targetUserId);

        return shareToken;
    }

    public void logDownload(String fileId, String userId) {
        FileAccessLog accessLog = FileAccessLog.builder()
            .fileId(fileId)
            .userId(userId)
            .accessType("DOWNLOAD")
            .accessDate(LocalDateTime.now())
            .build();

        accessLogRepository.save(accessLog);
    }

    public String getContentType(String fileId, HttpServletRequest request) {
        FileEntity fileEntity = fileRepository.findByFileId(fileId)
            .orElseThrow(() -> new RuntimeException("File not found: " + fileId));

        String contentType = fileEntity.getContentType();
        if (contentType == null) {
            contentType = "application/octet-stream";
        }

        return contentType;
    }

    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf('.') == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf('.'));
    }

    private FileInfoResponse convertToFileInfoResponse(FileEntity fileEntity) {
        return FileInfoResponse.builder()
            .fileId(fileEntity.getFileId())
            .fileName(fileEntity.getFileName())
            .fileSize(fileEntity.getFileSize())
            .contentType(fileEntity.getContentType())
            .uploadDate(fileEntity.getUploadDate())
            .description(fileEntity.getDescription())
            .encrypted(fileEntity.isEncrypted())
            .quarantined(fileEntity.isQuarantined())
            .build();
    }
}
