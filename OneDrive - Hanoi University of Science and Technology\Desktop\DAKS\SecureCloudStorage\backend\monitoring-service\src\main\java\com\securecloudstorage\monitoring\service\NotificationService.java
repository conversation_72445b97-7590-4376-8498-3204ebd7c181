package com.securecloudstorage.monitoring.service;

import com.securecloudstorage.monitoring.entity.Alert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * Notification Service
 * Dịch vụ gửi thông báo alerts
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationService {

    private final JavaMailSender mailSender;

    /**
     * Gửi alert notification
     */
    public void sendAlertNotification(Alert alert) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("Sending alert notification: {} - {}", alert.getAlertId(), alert.getTitle());
                
                // Send email notification
                sendEmailNotification(alert);
                
                // Send Slack notification (if configured)
                sendSlackNotification(alert);
                
                // Send SMS notification for critical alerts
                if ("CRITICAL".equals(alert.getSeverity())) {
                    sendSMSNotification(alert);
                }
                
            } catch (Exception e) {
                log.error("Error sending alert notification: ", e);
            }
        });
    }

    /**
     * Gửi escalation notification
     */
    public void sendEscalationNotification(Alert alert) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("Sending escalation notification: {} - Level {}", 
                    alert.getAlertId(), alert.getEscalationLevel());
                
                // Send escalation email
                sendEscalationEmail(alert);
                
                // Send urgent notifications
                sendUrgentNotification(alert);
                
            } catch (Exception e) {
                log.error("Error sending escalation notification: ", e);
            }
        });
    }

    /**
     * Gửi system notification
     */
    public void sendSystemNotification(String title, String message, String severity) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("Sending system notification: {} - {}", title, severity);
                
                // Send system email
                sendSystemEmail(title, message, severity);
                
            } catch (Exception e) {
                log.error("Error sending system notification: ", e);
            }
        });
    }

    // Private helper methods

    private void sendEmailNotification(Alert alert) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(getEmailRecipients(alert));
            message.setSubject(String.format("[%s] %s", alert.getSeverity(), alert.getTitle()));
            message.setText(buildEmailContent(alert));
            message.setFrom("<EMAIL>");
            
            mailSender.send(message);
            
            log.info("Email notification sent for alert: {}", alert.getAlertId());
            
        } catch (Exception e) {
            log.error("Error sending email notification: ", e);
        }
    }

    private void sendSlackNotification(Alert alert) {
        try {
            // Mock Slack notification - in real implementation, use Slack API
            log.info("Slack notification sent for alert: {}", alert.getAlertId());
            
        } catch (Exception e) {
            log.error("Error sending Slack notification: ", e);
        }
    }

    private void sendSMSNotification(Alert alert) {
        try {
            // Mock SMS notification - in real implementation, use SMS service
            log.info("SMS notification sent for critical alert: {}", alert.getAlertId());
            
        } catch (Exception e) {
            log.error("Error sending SMS notification: ", e);
        }
    }

    private void sendEscalationEmail(Alert alert) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(getEscalationRecipients(alert));
            message.setSubject(String.format("[ESCALATED] [%s] %s", alert.getSeverity(), alert.getTitle()));
            message.setText(buildEscalationContent(alert));
            message.setFrom("<EMAIL>");
            
            mailSender.send(message);
            
            log.info("Escalation email sent for alert: {}", alert.getAlertId());
            
        } catch (Exception e) {
            log.error("Error sending escalation email: ", e);
        }
    }

    private void sendUrgentNotification(Alert alert) {
        try {
            // Send urgent notifications through multiple channels
            log.info("Urgent notification sent for alert: {}", alert.getAlertId());
            
        } catch (Exception e) {
            log.error("Error sending urgent notification: ", e);
        }
    }

    private void sendSystemEmail(String title, String message, String severity) {
        try {
            SimpleMailMessage email = new SimpleMailMessage();
            email.setTo(getSystemRecipients());
            email.setSubject(String.format("[SYSTEM] [%s] %s", severity, title));
            email.setText(message);
            email.setFrom("<EMAIL>");
            
            mailSender.send(email);
            
            log.info("System email sent: {}", title);
            
        } catch (Exception e) {
            log.error("Error sending system email: ", e);
        }
    }

    private String[] getEmailRecipients(Alert alert) {
        // Get email recipients based on alert service and severity
        if ("security-service".equals(alert.getService())) {
            return new String[]{"<EMAIL>"};
        } else if ("CRITICAL".equals(alert.getSeverity())) {
            return new String[]{"<EMAIL>", "<EMAIL>"};
        } else {
            return new String[]{"<EMAIL>"};
        }
    }

    private String[] getEscalationRecipients(Alert alert) {
        // Get escalation recipients based on escalation level
        switch (alert.getEscalationLevel()) {
            case "L2":
                return new String[]{"<EMAIL>"};
            case "L3":
                return new String[]{"<EMAIL>"};
            case "MANAGEMENT":
                return new String[]{"<EMAIL>"};
            default:
                return new String[]{"<EMAIL>"};
        }
    }

    private String[] getSystemRecipients() {
        return new String[]{"<EMAIL>"};
    }

    private String buildEmailContent(Alert alert) {
        StringBuilder content = new StringBuilder();
        
        content.append("Alert Details:\n");
        content.append("=============\n\n");
        content.append("Alert ID: ").append(alert.getAlertId()).append("\n");
        content.append("Title: ").append(alert.getTitle()).append("\n");
        content.append("Description: ").append(alert.getDescription()).append("\n");
        content.append("Severity: ").append(alert.getSeverity()).append("\n");
        content.append("Service: ").append(alert.getService()).append("\n");
        content.append("Type: ").append(alert.getType()).append("\n");
        content.append("Status: ").append(alert.getStatus()).append("\n");
        content.append("Created: ").append(alert.getCreatedAt()).append("\n");
        
        if (alert.getAssignedTo() != null) {
            content.append("Assigned To: ").append(alert.getAssignedTo()).append("\n");
        }
        
        if (alert.getSlaDeadline() != null) {
            content.append("SLA Deadline: ").append(alert.getSlaDeadline()).append("\n");
        }
        
        content.append("\nPlease investigate and take appropriate action.\n");
        content.append("\nMonitoring System\n");
        content.append("SecureCloudStorage Platform");
        
        return content.toString();
    }

    private String buildEscalationContent(Alert alert) {
        StringBuilder content = new StringBuilder();
        
        content.append("ESCALATED ALERT:\n");
        content.append("================\n\n");
        content.append("This alert has been escalated to level: ").append(alert.getEscalationLevel()).append("\n\n");
        content.append("Alert ID: ").append(alert.getAlertId()).append("\n");
        content.append("Title: ").append(alert.getTitle()).append("\n");
        content.append("Description: ").append(alert.getDescription()).append("\n");
        content.append("Severity: ").append(alert.getSeverity()).append("\n");
        content.append("Service: ").append(alert.getService()).append("\n");
        content.append("Created: ").append(alert.getCreatedAt()).append("\n");
        content.append("Escalated: ").append(alert.getEscalatedAt()).append("\n");
        
        if (alert.getAssignedTo() != null) {
            content.append("Currently Assigned To: ").append(alert.getAssignedTo()).append("\n");
        }
        
        content.append("\nThis alert requires immediate attention due to SLA violation.\n");
        content.append("\nMonitoring System\n");
        content.append("SecureCloudStorage Platform");
        
        return content.toString();
    }
}
