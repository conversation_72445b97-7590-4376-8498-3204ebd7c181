package com.securecloudstorage.security.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Security Event Entity
 * Entity lưu trữ các security events
 */
@Entity
@Table(name = "security_events")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecurityEvent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "event_type", nullable = false)
    private String eventType;

    @Column(name = "severity", nullable = false)
    private String severity;

    @Column(name = "description", length = 2000)
    private String description;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "resource_id")
    private String resourceId;

    @Column(name = "ip_address")
    private String ipAddress;

    @Column(name = "user_agent", length = 1000)
    private String userAgent;

    @Column(name = "session_id")
    private String sessionId;

    @Column(name = "timestamp")
    private LocalDateTime timestamp;

    @Column(name = "source")
    private String source;

    @Column(name = "action")
    private String action;

    @Column(name = "target_entity")
    private String targetEntity;

    @Column(name = "target_entity_id")
    private String targetEntityId;

    @Column(name = "is_successful")
    private Boolean isSuccessful;

    @Column(name = "failure_reason")
    private String failureReason;

    @Column(name = "geolocation")
    private String geolocation;

    @Column(name = "device_info", length = 1000)
    private String deviceInfo;

    @Column(name = "application_version")
    private String applicationVersion;

    @Column(name = "risk_score")
    private String riskScore;

    @Column(name = "threat_level")
    private String threatLevel;

    @Column(name = "correlation_id")
    private String correlationId;

    @Column(name = "custom_data", length = 5000)
    private String customData;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "status")
    private String status;

    @Column(name = "assigned_to")
    private String assignedTo;

    @Column(name = "resolution", length = 2000)
    private String resolution;

    @Column(name = "notes", length = 2000)
    private String notes;

    @Column(name = "priority")
    private Integer priority;

    @Column(name = "category")
    private String category;

    @Column(name = "subcategory")
    private String subcategory;

    @Column(name = "is_processed")
    private Boolean isProcessed;

    @Column(name = "processed_by")
    private String processedBy;

    @Column(name = "processed_at")
    private LocalDateTime processedAt;

    @Column(name = "tags")
    private String tags;

    @Column(name = "alert_id")
    private String alertId;

    @Column(name = "incident_id")
    private String incidentId;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        if (status == null) {
            status = "NEW";
        }
        if (isProcessed == null) {
            isProcessed = false;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
