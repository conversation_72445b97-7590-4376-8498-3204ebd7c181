package com.securecloudstorage.storage.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * File Access Log Entity for MariaDB
 */
@Entity
@Table(name = "file_access_log", indexes = {
    @Index(name = "idx_access_log_file_id", columnList = "file_id"),
    @Index(name = "idx_access_log_user_id", columnList = "user_id"),
    @Index(name = "idx_access_log_date", columnList = "access_date")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileAccessLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "file_id", nullable = false, length = 255)
    private String fileId;
    
    @Column(name = "user_id", nullable = false, length = 255)
    private String userId;
    
    @Column(name = "access_type", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private AccessType accessType;
    
    @CreationTimestamp
    @Column(name = "access_date", nullable = false)
    private LocalDateTime accessDate;
    
    @Column(name = "ip_address", length = 45)
    private String ipAddress;
    
    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;
    
    @Column(name = "session_id", length = 255)
    private String sessionId;
    
    @Column(name = "success", nullable = false)
    private boolean success = true;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    @Column(name = "file_size")
    private Long fileSize;
    
    @Column(name = "download_duration")
    private Long downloadDuration; // in milliseconds
    
    public enum AccessType {
        DOWNLOAD,
        VIEW,
        SHARE,
        DELETE,
        UPDATE,
        SCAN
    }
}
