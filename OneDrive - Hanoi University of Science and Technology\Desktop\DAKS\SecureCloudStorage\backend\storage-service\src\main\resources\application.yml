server:
  port: 8082
  
spring:
  application:
    name: secure-cloud-storage-service
  
  # Database configuration
  datasource:
    url: ***************************************
    driver-class-name: org.mariadb.jdbc.Driver
    username: root
    password: root
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
    
  # JPA/Hibernate configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MariaDBDialect
        format_sql: true
    defer-datasource-initialization: false
        
  # File upload configuration
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 10MB

# File storage configuration
file:
  upload-dir: ./uploads
  quarantine-dir: ./quarantine
  max-file-size: 104857600 # 100MB
  allowed-extensions: 
    - jpg
    - jpeg
    - png
    - gif
    - pdf
    - docx
    - xlsx
    - pptx
    - txt
    - zip
    - rar

# Encryption configuration
encryption:
  algorithm: AES
  key-size: 256
  secret-key: mySecretKey123456789012345678901234567890

# AI-Malware Detection integration
ai-malware:
  service-url: http://localhost:8086
  enabled: true
  timeout: 30s

# Network IDS integration
network-ids:
  service-url: http://localhost:5000
  enabled: true
  timeout: 10s

# Security configuration
security:
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    allow-credentials: true

# Actuator configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# Logging configuration
logging:
  level:
    com.securecloudstorage: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/storage-service.log
