package com.securecloudstorage.gateway.client;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * AI Security Request/Response DTOs
 */
@Data
@Builder
public class AISecurityRequest {
    private String clientIp;
    private Map<String, String> headers;
    private String uri;
    private long timestamp;
}

@Data
@Builder
class AISecurityResult {
    private boolean malicious;
    private String threatType;
    private double confidence;
    private String description;
    private long timestamp;
    
    public boolean isMalicious() {
        return malicious;
    }
}

@Data
@Builder
class AIFileScanRequest {
    private String fileId;
    private byte[] fileContent;
    private long timestamp;
}

@Data
@Builder
class AISecurityStats {
    private long totalScans;
    private long malwareDetected;
    private long falsePositives;
    private double accuracy;
    private String lastScan;
}
