server:
  port: 8085
  servlet:
    context-path: /monitoring
  tomcat:
    max-threads: 200
    max-connections: 8192
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css

spring:
  application:
    name: monitoring-service
  profiles:
    active: dev
  
  datasource:
    url: **********************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1200000
      connection-timeout: 20000
      validation-timeout: 5000
      leak-detection-threshold: 60000
      pool-name: MonitoringServiceHikariCP
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        reWriteBatchedInserts: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        generate_statistics: true
        order_inserts: true
        order_updates: true
        jdbc:
          batch_size: 25
          fetch_size: 50
        cache:
          use_second_level_cache: true
          use_query_cache: true
          region:
            factory_class: org.hibernate.cache.caffeine.CaffeineRegionFactory
    open-in-view: false

  redis:
    host: localhost
    port: 6379
    database: 0
    password: 
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
      shutdown-timeout: 100ms

  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-app-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          connectiontimeout: 5000
          timeout: 3000
          writetimeout: 5000

  jackson:
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
      accept-empty-string-as-null-object: true
    default-property-inclusion: non_null
    time-zone: UTC

  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=10m,expireAfterAccess=5m

  async:
    core-pool-size: 10
    max-pool-size: 50
    queue-capacity: 100
    thread-name-prefix: MonitoringAsync-
    keep-alive-seconds: 60

  task:
    scheduling:
      pool:
        size: 10
      thread-name-prefix: MonitoringScheduler-

  cloud:
    loadbalancer:
      enabled: true
      ribbon:
        enabled: false

eureka:
  client:
    enabled: false
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
    healthcheck:
      enabled: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
    metadata-map:
      management.context-path: /actuator
      health.path: /actuator/health
      status.path: /actuator/info

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,loggers,env,configprops,mappings,scheduledtasks,httptrace,threaddump,heapdump,flyway,liquibase,caches,conditions,beans,startup
      path-mapping:
        prometheus: /metrics
      cors:
        allowed-origins: "*"
        allowed-methods: GET,POST,PUT,DELETE,OPTIONS
        allowed-headers: "*"
        allow-credentials: true
    enabled-by-default: true
  endpoint:
    health:
      show-details: always
      show-components: always
      cache:
        time-to-live: 10s
    info:
      cache:
        time-to-live: 10s
    metrics:
      enabled: true
      cache:
        time-to-live: 10s
    prometheus:
      enabled: true
      cache:
        time-to-live: 10s
  health:
    circuitbreakers:
      enabled: true
    ratelimiters:
      enabled: true
    db:
      enabled: true
    redis:
      enabled: true
    mail:
      enabled: true
    diskspace:
      enabled: true
      threshold: 10GB
    ping:
      enabled: true
  info:
    env:
      enabled: true
    build:
      enabled: true
    git:
      enabled: true
      mode: full
    java:
      enabled: true
    os:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
        descriptions: true
        pushgateway:
          enabled: false
    distribution:
      percentiles-histogram:
        http.server.requests: true
        spring.data.repository.invocations: true
      percentiles:
        http.server.requests: 0.5, 0.9, 0.95, 0.99
        spring.data.repository.invocations: 0.5, 0.9, 0.95, 0.99
      slo:
        http.server.requests: 10ms, 50ms, 100ms, 200ms, 500ms, 1s, 2s, 5s, 10s
    enable:
      jvm: true
      system: true
      web: true
      jdbc: true
      hikaricp: true
      redis: true
      caffeine: true
      process: true
      http: true
      logback: true
      spring.data.repository.invocations: true
    web:
      server:
        max-key-length: 256
        auto-time-requests: true
    tags:
      application: monitoring-service
      environment: ${spring.profiles.active}
      service: monitoring-service
      version: 1.0.0

logging:
  level:
    root: INFO
    com.securecloudstorage: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.data.redis: DEBUG
    org.springframework.mail: DEBUG
    org.springframework.cache: DEBUG
    org.springframework.async: DEBUG
    org.springframework.scheduling: DEBUG
    io.micrometer: DEBUG
    com.zaxxer.hikari: DEBUG
    org.postgresql: INFO
  pattern:
    console: '%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'
  file:
    name: logs/monitoring-service.log
    max-size: 10MB
    max-history: 30
    total-size-cap: 1GB

# Monitoring Service Configuration
monitoring:
  # Metrics collection settings
  metrics:
    collection:
      enabled: true
      interval: 30s
      batch-size: 100
      timeout: 10s
      retry-attempts: 3
      retry-delay: 1s
    
    # Metric retention settings
    retention:
      default-period: 30d
      key-metrics-period: 90d
      sla-metrics-period: 365d
      cleanup-interval: 1h
      archive-threshold: 7d
    
    # Performance settings
    performance:
      cpu-threshold: 80.0
      memory-threshold: 85.0
      disk-threshold: 90.0
      response-time-threshold: 1000
      error-rate-threshold: 5.0
      throughput-threshold: 100.0
  
  # Health check settings
  health:
    check:
      enabled: true
      interval: 60s
      timeout: 30s
      retry-attempts: 3
      retry-delay: 5s
      parallel-checks: 10
      max-concurrent-checks: 50
    
    # Service health thresholds
    thresholds:
      health-score-warning: 0.8
      health-score-critical: 0.6
      response-time-warning: 500
      response-time-critical: 2000
      error-rate-warning: 1.0
      error-rate-critical: 5.0
      uptime-warning: 99.0
      uptime-critical: 95.0
      consecutive-failures-warning: 3
      consecutive-failures-critical: 5
    
    # Auto-healing settings
    auto-healing:
      enabled: true
      max-attempts: 3
      wait-time: 60s
      escalation-delay: 300s
      success-threshold: 2
      failure-threshold: 5
  
  # Alert settings
  alerts:
    enabled: true
    processing:
      enabled: true
      interval: 10s
      batch-size: 50
      timeout: 30s
      retry-attempts: 3
      retry-delay: 2s
    
    # Alert thresholds
    thresholds:
      escalation-time: 15m
      auto-resolution-time: 5m
      sla-breach-time: 30m
      notification-throttle: 10m
      duplicate-suppression: 5m
    
    # Notification settings
    notifications:
      enabled: true
      channels:
        email:
          enabled: true
          timeout: 10s
          retry-attempts: 3
        slack:
          enabled: false
          webhook-url: https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
          timeout: 10s
          retry-attempts: 3
        sms:
          enabled: false
          provider: twilio
          timeout: 10s
          retry-attempts: 3
        webhook:
          enabled: false
          url: https://your-webhook-url.com
          timeout: 10s
          retry-attempts: 3
    
    # Escalation settings
    escalation:
      enabled: true
      levels:
        - name: L1
          duration: 15m
          recipients: ["<EMAIL>"]
        - name: L2
          duration: 30m
          recipients: ["<EMAIL>"]
        - name: L3
          duration: 60m
          recipients: ["<EMAIL>"]
    
    # SLA settings
    sla:
      enabled: true
      default-response-time: 15m
      default-resolution-time: 4h
      critical-response-time: 5m
      critical-resolution-time: 1h
      high-response-time: 10m
      high-resolution-time: 2h
      medium-response-time: 30m
      medium-resolution-time: 8h
      low-response-time: 2h
      low-resolution-time: 24h
  
  # Dashboard settings
  dashboard:
    enabled: true
    refresh-interval: 30s
    data-retention: 7d
    cache-timeout: 5m
    max-data-points: 1000
    
    # Widget settings
    widgets:
      system-metrics:
        enabled: true
        refresh-interval: 30s
      service-health:
        enabled: true
        refresh-interval: 60s
      alerts:
        enabled: true
        refresh-interval: 10s
      performance:
        enabled: true
        refresh-interval: 30s
  
  # Integration settings
  integration:
    services:
      gateway:
        enabled: true
        url: http://localhost:8080
        timeout: 10s
        retry-attempts: 3
      storage:
        enabled: true
        url: http://localhost:8082
        timeout: 10s
        retry-attempts: 3
      user:
        enabled: true
        url: http://localhost:8083
        timeout: 10s
        retry-attempts: 3
      security:
        enabled: true
        url: http://localhost:8084
        timeout: 10s
        retry-attempts: 3
      ai-malware:
        enabled: true
        url: http://localhost:8086
        timeout: 10s
        retry-attempts: 3
      network-ids:
        enabled: true
        url: http://localhost:5000
        timeout: 10s
        retry-attempts: 3
  
  # Cleanup settings
  cleanup:
    enabled: true
    schedule: "0 0 2 * * ?" # Daily at 2 AM
    archive-threshold: 30d
    delete-threshold: 90d
    batch-size: 1000
    
    # Specific cleanup settings
    alerts:
      resolved-threshold: 7d
      archived-threshold: 30d
      delete-threshold: 90d
    metrics:
      inactive-threshold: 7d
      archived-threshold: 30d
      delete-threshold: 90d
    health-checks:
      inactive-threshold: 7d
      archived-threshold: 30d
      delete-threshold: 90d

# Resilience4j Configuration
resilience4j:
  circuitbreaker:
    instances:
      default:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 10s
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        automatic-transition-from-open-to-half-open-enabled: true
        sliding-window-type: count_based
        record-exceptions:
          - java.io.IOException
          - java.util.concurrent.TimeoutException
          - org.springframework.web.client.ResourceAccessException
        ignore-exceptions:
          - java.lang.IllegalArgumentException
      
      external-service:
        failure-rate-threshold: 60
        wait-duration-in-open-state: 30s
        sliding-window-size: 20
        minimum-number-of-calls: 10
        permitted-number-of-calls-in-half-open-state: 5
        automatic-transition-from-open-to-half-open-enabled: true
        sliding-window-type: count_based
  
  retry:
    instances:
      default:
        max-attempts: 3
        wait-duration: 1s
        enable-exponential-backoff: true
        exponential-backoff-multiplier: 2
        retry-exceptions:
          - java.io.IOException
          - java.util.concurrent.TimeoutException
          - org.springframework.web.client.ResourceAccessException
        ignore-exceptions:
          - java.lang.IllegalArgumentException
      
      external-service:
        max-attempts: 5
        wait-duration: 2s
        enable-exponential-backoff: true
        exponential-backoff-multiplier: 1.5
        retry-exceptions:
          - java.io.IOException
          - java.util.concurrent.TimeoutException
          - org.springframework.web.client.ResourceAccessException
  
  ratelimiter:
    instances:
      default:
        limit-for-period: 100
        limit-refresh-period: 1s
        timeout-duration: 3s
      
      api:
        limit-for-period: 1000
        limit-refresh-period: 1s
        timeout-duration: 1s
  
  bulkhead:
    instances:
      default:
        max-concurrent-calls: 10
        max-wait-duration: 1s
      
      metrics-collection:
        max-concurrent-calls: 20
        max-wait-duration: 2s
      
      health-check:
        max-concurrent-calls: 50
        max-wait-duration: 3s
  
  timelimiter:
    instances:
      default:
        timeout-duration: 10s
        cancel-running-future: true
      
      external-service:
        timeout-duration: 30s
        cancel-running-future: true

# Feign Configuration
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
        errorDecoder: com.securecloudstorage.monitoring.config.FeignErrorDecoder
        requestInterceptors:
          - com.securecloudstorage.monitoring.config.FeignRequestInterceptor
        retryer: com.securecloudstorage.monitoring.config.FeignRetryer
      
      gateway-service:
        connectTimeout: 5000
        readTimeout: 15000
        loggerLevel: full
      
      storage-service:
        connectTimeout: 5000
        readTimeout: 15000
        loggerLevel: full
      
      user-service:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
      
      security-service:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
  
  hystrix:
    enabled: false
  
  compression:
    request:
      enabled: true
      mime-types: application/json,application/xml,text/xml,text/plain
      min-request-size: 2048
    response:
      enabled: true

# Application Info
info:
  app:
    name: Monitoring Service
    description: SecureCloudStorage Monitoring Service
    version: 1.0.0
    encoding: UTF-8
    java.version: 17
  build:
    artifact: monitoring-service
    name: monitoring-service
    time: '@timestamp@'
    version: 1.0.0
  git:
    branch: main
    commit:
      id: '@git.commit.id@'
      time: '@git.commit.time@'
  system:
    timezone: UTC
    locale: en_US
