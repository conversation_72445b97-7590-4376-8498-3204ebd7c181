package com.securecloudstorage.monitoring.repository;

import com.securecloudstorage.monitoring.entity.Alert;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Alert Repository
 * Repository cho Alert entity
 */
@Repository
public interface AlertRepository extends JpaRepository<Alert, UUID>, JpaSpecificationExecutor<Alert> {

    // Basic queries
    Optional<Alert> findByAlertId(String alertId);
    List<Alert> findByService(String service);
    List<Alert> findByStatus(String status);
    List<Alert> findBySeverity(String severity);
    List<Alert> findByType(String type);
    List<Alert> findByAssignedTo(String assignedTo);
    List<Alert> findByResolvedBy(String resolvedBy);
    List<Alert> findByEnvironment(String environment);
    List<Alert> findByHost(String host);
    List<Alert> findByActive(Boolean active);
    List<Alert> findByArchived(Boolean archived);

    // Combined queries
    List<Alert> findByServiceAndStatus(String service, String status);
    List<Alert> findByServiceAndSeverity(String service, String severity);
    List<Alert> findByStatusAndSeverity(String status, String severity);
    List<Alert> findByServiceAndStatusAndSeverity(String service, String status, String severity);
    List<Alert> findByServiceAndEnvironment(String service, String environment);
    List<Alert> findByStatusAndEnvironment(String status, String environment);
    List<Alert> findByAssignedToAndStatus(String assignedTo, String status);

    // Active alerts
    List<Alert> findByActiveTrue();
    List<Alert> findByActiveTrueAndStatus(String status);
    List<Alert> findByActiveTrueAndSeverity(String severity);
    List<Alert> findByActiveTrueAndService(String service);
    List<Alert> findByActiveTrueAndServiceAndSeverity(String service, String severity);

    // Date range queries
    List<Alert> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<Alert> findByUpdatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<Alert> findByResolvedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<Alert> findByEscalatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    // Date range with filters
    List<Alert> findByServiceAndCreatedAtBetween(String service, LocalDateTime startDate, LocalDateTime endDate);
    List<Alert> findByStatusAndCreatedAtBetween(String status, LocalDateTime startDate, LocalDateTime endDate);
    List<Alert> findBySeverityAndCreatedAtBetween(String severity, LocalDateTime startDate, LocalDateTime endDate);
    List<Alert> findByServiceAndStatusAndCreatedAtBetween(String service, String status, LocalDateTime startDate, LocalDateTime endDate);

    // Critical alerts
    List<Alert> findBySeverityAndActiveTrue(String severity);
    List<Alert> findBySeverityAndStatusAndActiveTrue(String severity, String status);
    List<Alert> findBySeverityAndServiceAndActiveTrue(String severity, String service);

    // Escalation queries
    List<Alert> findByEscalationLevel(String escalationLevel);
    List<Alert> findByEscalationLevelAndStatus(String escalationLevel, String status);
    List<Alert> findByEscalatedAtIsNotNull();
    List<Alert> findByEscalatedAtIsNotNullAndStatus(String status);

    // SLA queries
    List<Alert> findBySlaBreachedTrue();
    List<Alert> findBySlaBreachedTrueAndStatus(String status);
    List<Alert> findBySlaBreachedTrueAndService(String service);
    List<Alert> findBySlaDeadlineBeforeAndStatusNot(LocalDateTime deadline, String status);

    // Auto resolution queries
    List<Alert> findByAutoResolvedTrue();
    List<Alert> findByAutoResolvedTrueAndService(String service);
    List<Alert> findByAutoResolvedFalseAndStatus(String status);

    // Acknowledgment queries
    List<Alert> findByAcknowledgedBy(String acknowledgedBy);
    List<Alert> findByAcknowledgedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<Alert> findByAcknowledgmentRequiredTrueAndStatusNot(String status);

    // Suppression queries
    List<Alert> findBySuppressedBy(String suppressedBy);
    List<Alert> findBySuppressedUntilAfter(LocalDateTime suppressionEnd);
    List<Alert> findBySuppressedUntilBefore(LocalDateTime suppressionEnd);

    // Notification queries
    List<Alert> findByNotificationSentFalse();
    List<Alert> findByNotificationSentTrueAndService(String service);
    List<Alert> findByLastNotificationSentBefore(LocalDateTime threshold);

    // Correlation queries
    List<Alert> findByCorrelationId(String correlationId);
    List<Alert> findByParentAlertId(String parentAlertId);
    List<Alert> findByChildAlertCountGreaterThan(Integer count);

    // Metric-based queries
    List<Alert> findByMetricName(String metricName);
    List<Alert> findByMetricNameAndService(String metricName, String service);
    List<Alert> findByMetricValueGreaterThan(Double value);
    List<Alert> findByMetricValueLessThan(Double value);
    List<Alert> findByMetricValueBetween(Double minValue, Double maxValue);

    // Custom queries
    @Query("SELECT a FROM Alert a WHERE a.active = true AND a.status = 'ACTIVE' AND a.severity IN :severities")
    List<Alert> findActiveBySeverities(@Param("severities") List<String> severities);

    @Query("SELECT a FROM Alert a WHERE a.service = :service AND a.createdAt >= :since AND a.status != 'RESOLVED'")
    List<Alert> findUnresolvedByServiceSince(@Param("service") String service, @Param("since") LocalDateTime since);

    @Query("SELECT a FROM Alert a WHERE a.assignedTo = :assignedTo AND a.status IN ('ACTIVE', 'ACKNOWLEDGED') ORDER BY a.severity DESC, a.createdAt ASC")
    List<Alert> findActiveAssignedAlerts(@Param("assignedTo") String assignedTo);

    @Query("SELECT a FROM Alert a WHERE a.slaDeadline < :now AND a.status != 'RESOLVED' AND a.slaBreached = false")
    List<Alert> findSlaBreachCandidates(@Param("now") LocalDateTime now);

    @Query("SELECT a FROM Alert a WHERE a.escalationLevel IS NULL AND a.severity = 'CRITICAL' AND a.createdAt < :threshold AND a.status != 'RESOLVED'")
    List<Alert> findEscalationCandidates(@Param("threshold") LocalDateTime threshold);

    @Query("SELECT a FROM Alert a WHERE a.autoResolved = false AND a.status = 'ACTIVE' AND a.lastOccurrence < :threshold")
    List<Alert> findAutoResolutionCandidates(@Param("threshold") LocalDateTime threshold);

    @Query("SELECT a FROM Alert a WHERE a.notificationSent = false OR (a.severity = 'CRITICAL' AND a.lastNotificationSent < :threshold)")
    List<Alert> findNotificationCandidates(@Param("threshold") LocalDateTime threshold);

    @Query("SELECT a FROM Alert a WHERE a.status = 'SUPPRESSED' AND a.suppressedUntil < :now")
    List<Alert> findExpiredSuppressions(@Param("now") LocalDateTime now);

    @Query("SELECT a FROM Alert a WHERE a.archived = false AND a.status = 'RESOLVED' AND a.resolvedAt < :threshold")
    List<Alert> findArchivalCandidates(@Param("threshold") LocalDateTime threshold);

    // Statistics queries
    @Query("SELECT COUNT(a) FROM Alert a WHERE a.active = true AND a.status = :status")
    long countActiveByStatus(@Param("status") String status);

    @Query("SELECT COUNT(a) FROM Alert a WHERE a.active = true AND a.severity = :severity")
    long countActiveBySeverity(@Param("severity") String severity);

    @Query("SELECT COUNT(a) FROM Alert a WHERE a.service = :service AND a.createdAt >= :since")
    long countByServiceSince(@Param("service") String service, @Param("since") LocalDateTime since);

    @Query("SELECT COUNT(a) FROM Alert a WHERE a.resolvedBy = :resolvedBy AND a.resolvedAt >= :since")
    long countResolvedBySince(@Param("resolvedBy") String resolvedBy, @Param("since") LocalDateTime since);

    @Query("SELECT COUNT(a) FROM Alert a WHERE a.autoResolved = true AND a.resolvedAt >= :since")
    long countAutoResolvedSince(@Param("since") LocalDateTime since);

    @Query("SELECT COUNT(a) FROM Alert a WHERE a.slaBreached = true AND a.createdAt >= :since")
    long countSlaBreachedSince(@Param("since") LocalDateTime since);

    @Query("SELECT a.service, COUNT(a) FROM Alert a WHERE a.active = true GROUP BY a.service")
    List<Object[]> countActiveByService();

    @Query("SELECT a.severity, COUNT(a) FROM Alert a WHERE a.active = true GROUP BY a.severity")
    List<Object[]> countActiveBySeverity();

    @Query("SELECT a.status, COUNT(a) FROM Alert a WHERE a.active = true GROUP BY a.status")
    List<Object[]> countActiveByStatus();

    @Query("SELECT a.assignedTo, COUNT(a) FROM Alert a WHERE a.status IN ('ACTIVE', 'ACKNOWLEDGED') GROUP BY a.assignedTo")
    List<Object[]> countActiveByAssignee();

    // Performance queries
    @Query("SELECT AVG(a.durationSeconds) FROM Alert a WHERE a.status = 'RESOLVED' AND a.resolvedAt >= :since")
    Double averageResolutionTime(@Param("since") LocalDateTime since);

    @Query("SELECT AVG(EXTRACT(EPOCH FROM (a.acknowledgedAt - a.createdAt))) FROM Alert a WHERE a.acknowledgedAt IS NOT NULL AND a.createdAt >= :since")
    Double averageAcknowledgmentTime(@Param("since") LocalDateTime since);

    @Query("SELECT MIN(a.createdAt), MAX(a.createdAt) FROM Alert a WHERE a.service = :service AND a.active = true")
    List<Object[]> getAlertTimeRange(@Param("service") String service);

    // Top queries
    @Query("SELECT a.service, COUNT(a) as alertCount FROM Alert a WHERE a.createdAt >= :since GROUP BY a.service ORDER BY alertCount DESC")
    List<Object[]> findTopServicesByAlertCount(@Param("since") LocalDateTime since);

    @Query("SELECT a.metricName, COUNT(a) as alertCount FROM Alert a WHERE a.createdAt >= :since AND a.metricName IS NOT NULL GROUP BY a.metricName ORDER BY alertCount DESC")
    List<Object[]> findTopMetricsByAlertCount(@Param("since") LocalDateTime since);

    @Query("SELECT a.assignedTo, COUNT(a) as alertCount FROM Alert a WHERE a.resolvedAt >= :since AND a.assignedTo IS NOT NULL GROUP BY a.assignedTo ORDER BY alertCount DESC")
    List<Object[]> findTopResolversByCount(@Param("since") LocalDateTime since);

    @Query("SELECT a.assignedTo, AVG(a.durationSeconds) as avgResolutionTime FROM Alert a WHERE a.resolvedAt >= :since AND a.assignedTo IS NOT NULL AND a.durationSeconds IS NOT NULL GROUP BY a.assignedTo ORDER BY avgResolutionTime ASC")
    List<Object[]> findTopResolversBySpeed(@Param("since") LocalDateTime since);

    // Trend queries
    @Query("SELECT DATE(a.createdAt), COUNT(a) FROM Alert a WHERE a.createdAt >= :since GROUP BY DATE(a.createdAt) ORDER BY DATE(a.createdAt)")
    List<Object[]> getDailyAlertTrend(@Param("since") LocalDateTime since);

    @Query("SELECT DATE(a.createdAt), a.severity, COUNT(a) FROM Alert a WHERE a.createdAt >= :since GROUP BY DATE(a.createdAt), a.severity ORDER BY DATE(a.createdAt), a.severity")
    List<Object[]> getDailyAlertTrendBySeverity(@Param("since") LocalDateTime since);

    @Query("SELECT DATE(a.createdAt), a.service, COUNT(a) FROM Alert a WHERE a.createdAt >= :since GROUP BY DATE(a.createdAt), a.service ORDER BY DATE(a.createdAt), a.service")
    List<Object[]> getDailyAlertTrendByService(@Param("since") LocalDateTime since);

    // Cleanup queries
    @Query("DELETE FROM Alert a WHERE a.archived = true AND a.archivedAt < :threshold")
    void deleteArchivedBefore(@Param("threshold") LocalDateTime threshold);

    @Query("DELETE FROM Alert a WHERE a.status = 'RESOLVED' AND a.resolvedAt < :threshold")
    void deleteResolvedBefore(@Param("threshold") LocalDateTime threshold);

    @Query("UPDATE Alert a SET a.archived = true, a.archivedAt = :now WHERE a.status = 'RESOLVED' AND a.resolvedAt < :threshold")
    void archiveResolvedBefore(@Param("threshold") LocalDateTime threshold, @Param("now") LocalDateTime now);

    @Query("UPDATE Alert a SET a.active = false WHERE a.status = 'RESOLVED' AND a.resolvedAt < :threshold")
    void deactivateResolvedBefore(@Param("threshold") LocalDateTime threshold);
}
