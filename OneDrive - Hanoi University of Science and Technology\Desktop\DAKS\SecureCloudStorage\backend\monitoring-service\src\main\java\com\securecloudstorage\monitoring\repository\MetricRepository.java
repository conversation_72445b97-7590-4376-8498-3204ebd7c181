package com.securecloudstorage.monitoring.repository;

import com.securecloudstorage.monitoring.entity.Metric;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Metric Repository
 * Repository cho Metric entity
 */
@Repository
public interface MetricRepository extends JpaRepository<Metric, UUID>, JpaSpecificationExecutor<Metric> {

    // Basic queries
    Optional<Metric> findByMetricId(String metricId);
    List<Metric> findByMetricName(String metricName);
    List<Metric> findByMetricType(String metricType);
    List<Metric> findByService(String service);
    List<Metric> findByHost(String host);
    List<Metric> findByInstance(String instance);
    List<Metric> findByEnvironment(String environment);
    List<Metric> findByNamespace(String namespace);
    List<Metric> findByCategory(String category);
    List<Metric> findBySubcategory(String subcategory);
    List<Metric> findBySource(String source);
    List<Metric> findByCollector(String collector);
    List<Metric> findByActive(Boolean active);
    List<Metric> findByArchived(Boolean archived);

    // Combined queries
    List<Metric> findByServiceAndMetricName(String service, String metricName);
    List<Metric> findByServiceAndCategory(String service, String category);
    List<Metric> findByServiceAndEnvironment(String service, String environment);
    List<Metric> findByMetricNameAndEnvironment(String metricName, String environment);
    List<Metric> findByServiceAndMetricNameAndEnvironment(String service, String metricName, String environment);
    List<Metric> findByCategoryAndSubcategory(String category, String subcategory);
    List<Metric> findByMetricTypeAndCategory(String metricType, String category);

    // Active metrics
    List<Metric> findByActiveTrue();
    List<Metric> findByActiveTrueAndService(String service);
    List<Metric> findByActiveTrueAndMetricName(String metricName);
    List<Metric> findByActiveTrueAndCategory(String category);
    List<Metric> findByActiveTrueAndServiceAndCategory(String service, String category);

    // Key metrics
    List<Metric> findByIsKeyMetricTrue();
    List<Metric> findByIsKeyMetricTrueAndService(String service);
    List<Metric> findByIsKeyMetricTrueAndCategory(String category);
    List<Metric> findByIsKeyMetricTrueAndServiceAndCategory(String service, String category);

    // SLA metrics
    List<Metric> findByIsSlaMetricTrue();
    List<Metric> findByIsSlaMetricTrueAndService(String service);
    List<Metric> findByIsSlaMetricTrueAndEnvironment(String environment);
    List<Metric> findByIsSlaMetricTrueAndServiceAndEnvironment(String service, String environment);

    // Alert enabled metrics
    List<Metric> findByAlertEnabledTrue();
    List<Metric> findByAlertEnabledTrueAndService(String service);
    List<Metric> findByAlertEnabledTrueAndSeverity(String severity);
    List<Metric> findByAlertRuleId(String alertRuleId);

    // Anomaly detection metrics
    List<Metric> findByAnomalyDetectionEnabledTrue();
    List<Metric> findByAnomalyDetectionEnabledTrueAndService(String service);
    List<Metric> findByAnomalyDetectionEnabledTrueAndAnomalySensitivity(String anomalySensitivity);

    // Forecast metrics
    List<Metric> findByForecastEnabledTrue();
    List<Metric> findByForecastEnabledTrueAndService(String service);
    List<Metric> findByForecastEnabledTrueAndForecastHorizonGreaterThan(Integer horizon);

    // Date range queries
    List<Metric> findByMeasuredAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<Metric> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<Metric> findByUpdatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<Metric> findByProcessedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    // Date range with filters
    List<Metric> findByServiceAndMeasuredAtBetween(String service, LocalDateTime startDate, LocalDateTime endDate);
    List<Metric> findByMetricNameAndMeasuredAtBetween(String metricName, LocalDateTime startDate, LocalDateTime endDate);
    List<Metric> findByServiceAndMetricNameAndMeasuredAtBetween(String service, String metricName, LocalDateTime startDate, LocalDateTime endDate);

    // Value range queries
    List<Metric> findByValueGreaterThan(Double value);
    List<Metric> findByValueLessThan(Double value);
    List<Metric> findByValueBetween(Double minValue, Double maxValue);
    List<Metric> findByValueGreaterThanAndService(Double value, String service);
    List<Metric> findByValueLessThanAndService(Double value, String service);

    // Threshold queries
    List<Metric> findByThresholdWarningIsNotNull();
    List<Metric> findByThresholdCriticalIsNotNull();
    List<Metric> findByThresholdWarningIsNotNullAndService(String service);
    List<Metric> findByThresholdCriticalIsNotNullAndService(String service);

    // Business impact queries
    List<Metric> findByBusinessImpact(String businessImpact);
    List<Metric> findByBusinessImpactAndService(String businessImpact, String service);
    List<Metric> findByBusinessImpactInAndActiveTrue(List<String> businessImpacts);

    // Trend queries
    List<Metric> findByTrendDirection(String trendDirection);
    List<Metric> findByTrendDirectionAndService(String trendDirection, String service);
    List<Metric> findByTrendPercentageGreaterThan(Double percentage);
    List<Metric> findByTrendPercentageLessThan(Double percentage);

    // Data quality queries
    List<Metric> findByDataQualityScoreGreaterThan(Double score);
    List<Metric> findByDataQualityScoreLessThan(Double score);
    List<Metric> findByDataCompletenessGreaterThan(Double completeness);
    List<Metric> findByDataAccuracyGreaterThan(Double accuracy);
    List<Metric> findByDataFreshnessLessThan(Integer freshness);

    // Validation queries
    List<Metric> findByValidationStatus(String validationStatus);
    List<Metric> findByValidationStatusAndService(String validationStatus, String service);
    List<Metric> findByValidationStatusNot(String validationStatus);

    // Dashboard queries
    List<Metric> findByDashboardId(String dashboardId);
    List<Metric> findByDashboardIdAndService(String dashboardId, String service);
    List<Metric> findByChartType(String chartType);
    List<Metric> findByChartTypeAndService(String chartType, String service);

    // Custom queries
    @Query("SELECT m FROM Metric m WHERE m.active = true AND m.service = :service AND m.category = :category ORDER BY m.sortOrder ASC, m.displayName ASC")
    List<Metric> findActiveByServiceAndCategoryOrdered(@Param("service") String service, @Param("category") String category);

    @Query("SELECT m FROM Metric m WHERE m.active = true AND m.isKeyMetric = true ORDER BY m.businessImpact DESC, m.service ASC, m.displayName ASC")
    List<Metric> findActiveKeyMetricsOrdered();

    @Query("SELECT m FROM Metric m WHERE m.active = true AND m.isSlaMetric = true AND m.environment = :environment ORDER BY m.service ASC, m.displayName ASC")
    List<Metric> findActiveSlaMetricsByEnvironment(@Param("environment") String environment);

    @Query("SELECT m FROM Metric m WHERE m.alertEnabled = true AND m.active = true AND " +
           "((m.thresholdWarning IS NOT NULL AND m.value > m.thresholdWarning) OR " +
           "(m.thresholdCritical IS NOT NULL AND m.value > m.thresholdCritical))")
    List<Metric> findThresholdExceededMetrics();

    @Query("SELECT m FROM Metric m WHERE m.alertEnabled = true AND m.active = true AND " +
           "m.thresholdCritical IS NOT NULL AND m.value > m.thresholdCritical")
    List<Metric> findCriticalThresholdExceededMetrics();

    @Query("SELECT m FROM Metric m WHERE m.active = true AND m.measuredAt < :threshold")
    List<Metric> findStaleMetrics(@Param("threshold") LocalDateTime threshold);

    @Query("SELECT m FROM Metric m WHERE m.active = true AND m.dataQualityScore < :threshold")
    List<Metric> findPoorQualityMetrics(@Param("threshold") Double threshold);

    @Query("SELECT m FROM Metric m WHERE m.anomalyDetectionEnabled = true AND m.active = true AND " +
           "ABS(m.value - m.baselineValue) > (m.anomalyThreshold * m.baselineValue)")
    List<Metric> findAnomalyDetectedMetrics();

    @Query("SELECT m FROM Metric m WHERE m.isSlaMetric = true AND m.active = true AND " +
           "((m.slaOperator = 'GT' AND m.value <= m.slaTarget) OR " +
           "(m.slaOperator = 'LT' AND m.value >= m.slaTarget) OR " +
           "(m.slaOperator = 'EQ' AND m.value != m.slaTarget))")
    List<Metric> findSlaViolatedMetrics();

    @Query("SELECT m FROM Metric m WHERE m.archived = false AND m.active = false AND m.updatedAt < :threshold")
    List<Metric> findArchivalCandidates(@Param("threshold") LocalDateTime threshold);

    // Statistics queries
    @Query("SELECT COUNT(m) FROM Metric m WHERE m.active = true AND m.service = :service")
    long countActiveByService(@Param("service") String service);

    @Query("SELECT COUNT(m) FROM Metric m WHERE m.active = true AND m.category = :category")
    long countActiveByCategory(@Param("category") String category);

    @Query("SELECT COUNT(m) FROM Metric m WHERE m.active = true AND m.environment = :environment")
    long countActiveByEnvironment(@Param("environment") String environment);

    @Query("SELECT COUNT(m) FROM Metric m WHERE m.isKeyMetric = true AND m.active = true")
    long countActiveKeyMetrics();

    @Query("SELECT COUNT(m) FROM Metric m WHERE m.isSlaMetric = true AND m.active = true")
    long countActiveSlaMetrics();

    @Query("SELECT COUNT(m) FROM Metric m WHERE m.alertEnabled = true AND m.active = true")
    long countActiveAlertEnabledMetrics();

    @Query("SELECT COUNT(m) FROM Metric m WHERE m.anomalyDetectionEnabled = true AND m.active = true")
    long countActiveAnomalyDetectionMetrics();

    @Query("SELECT m.service, COUNT(m) FROM Metric m WHERE m.active = true GROUP BY m.service")
    List<Object[]> countActiveByService();

    @Query("SELECT m.category, COUNT(m) FROM Metric m WHERE m.active = true GROUP BY m.category")
    List<Object[]> countActiveByCategory();

    @Query("SELECT m.environment, COUNT(m) FROM Metric m WHERE m.active = true GROUP BY m.environment")
    List<Object[]> countActiveByEnvironment();

    @Query("SELECT m.businessImpact, COUNT(m) FROM Metric m WHERE m.active = true GROUP BY m.businessImpact")
    List<Object[]> countActiveByBusinessImpact();

    @Query("SELECT m.validationStatus, COUNT(m) FROM Metric m WHERE m.active = true GROUP BY m.validationStatus")
    List<Object[]> countActiveByValidationStatus();

    // Aggregation queries
    @Query("SELECT AVG(m.value) FROM Metric m WHERE m.service = :service AND m.metricName = :metricName AND m.measuredAt >= :since")
    Double averageValueByServiceAndMetricSince(@Param("service") String service, @Param("metricName") String metricName, @Param("since") LocalDateTime since);

    @Query("SELECT MIN(m.value) FROM Metric m WHERE m.service = :service AND m.metricName = :metricName AND m.measuredAt >= :since")
    Double minValueByServiceAndMetricSince(@Param("service") String service, @Param("metricName") String metricName, @Param("since") LocalDateTime since);

    @Query("SELECT MAX(m.value) FROM Metric m WHERE m.service = :service AND m.metricName = :metricName AND m.measuredAt >= :since")
    Double maxValueByServiceAndMetricSince(@Param("service") String service, @Param("metricName") String metricName, @Param("since") LocalDateTime since);

    @Query("SELECT SUM(m.value) FROM Metric m WHERE m.service = :service AND m.metricName = :metricName AND m.measuredAt >= :since")
    Double sumValueByServiceAndMetricSince(@Param("service") String service, @Param("metricName") String metricName, @Param("since") LocalDateTime since);

    @Query("SELECT COUNT(m) FROM Metric m WHERE m.service = :service AND m.metricName = :metricName AND m.measuredAt >= :since")
    long countByServiceAndMetricSince(@Param("service") String service, @Param("metricName") String metricName, @Param("since") LocalDateTime since);

    @Query("SELECT AVG(m.dataQualityScore) FROM Metric m WHERE m.service = :service AND m.active = true")
    Double averageDataQualityScoreByService(@Param("service") String service);

    @Query("SELECT AVG(m.dataFreshness) FROM Metric m WHERE m.service = :service AND m.active = true")
    Double averageDataFreshnessByService(@Param("service") String service);

    // Time series queries
    @Query("SELECT m.measuredAt, m.value FROM Metric m WHERE m.service = :service AND m.metricName = :metricName AND m.measuredAt >= :since ORDER BY m.measuredAt ASC")
    List<Object[]> getTimeSeriesData(@Param("service") String service, @Param("metricName") String metricName, @Param("since") LocalDateTime since);

    @Query("SELECT DATE(m.measuredAt), AVG(m.value) FROM Metric m WHERE m.service = :service AND m.metricName = :metricName AND m.measuredAt >= :since GROUP BY DATE(m.measuredAt) ORDER BY DATE(m.measuredAt)")
    List<Object[]> getDailyAverageTimeSeries(@Param("service") String service, @Param("metricName") String metricName, @Param("since") LocalDateTime since);

    @Query("SELECT DATE(m.measuredAt), MAX(m.value) FROM Metric m WHERE m.service = :service AND m.metricName = :metricName AND m.measuredAt >= :since GROUP BY DATE(m.measuredAt) ORDER BY DATE(m.measuredAt)")
    List<Object[]> getDailyMaxTimeSeries(@Param("service") String service, @Param("metricName") String metricName, @Param("since") LocalDateTime since);

    @Query("SELECT DATE(m.measuredAt), MIN(m.value) FROM Metric m WHERE m.service = :service AND m.metricName = :metricName AND m.measuredAt >= :since GROUP BY DATE(m.measuredAt) ORDER BY DATE(m.measuredAt)")
    List<Object[]> getDailyMinTimeSeries(@Param("service") String service, @Param("metricName") String metricName, @Param("since") LocalDateTime since);

    // Top metrics queries
    @Query("SELECT m.service, m.metricName, MAX(m.value) as maxValue FROM Metric m WHERE m.measuredAt >= :since GROUP BY m.service, m.metricName ORDER BY maxValue DESC")
    List<Object[]> findTopMetricsByMaxValue(@Param("since") LocalDateTime since);

    @Query("SELECT m.service, m.metricName, AVG(m.value) as avgValue FROM Metric m WHERE m.measuredAt >= :since GROUP BY m.service, m.metricName ORDER BY avgValue DESC")
    List<Object[]> findTopMetricsByAvgValue(@Param("since") LocalDateTime since);

    @Query("SELECT m.service, COUNT(m) as metricCount FROM Metric m WHERE m.active = true GROUP BY m.service ORDER BY metricCount DESC")
    List<Object[]> findTopServicesByMetricCount();

    @Query("SELECT m.metricName, COUNT(m) as serviceCount FROM Metric m WHERE m.active = true GROUP BY m.metricName ORDER BY serviceCount DESC")
    List<Object[]> findTopMetricsByServiceCount();

    // Cleanup queries
    @Query("DELETE FROM Metric m WHERE m.archived = true AND m.archivedAt < :threshold")
    void deleteArchivedBefore(@Param("threshold") LocalDateTime threshold);

    @Query("DELETE FROM Metric m WHERE m.active = false AND m.updatedAt < :threshold")
    void deleteInactiveBefore(@Param("threshold") LocalDateTime threshold);

    @Query("UPDATE Metric m SET m.archived = true, m.archivedAt = :now WHERE m.active = false AND m.updatedAt < :threshold")
    void archiveInactiveBefore(@Param("threshold") LocalDateTime threshold, @Param("now") LocalDateTime now);

    @Query("UPDATE Metric m SET m.active = false WHERE m.measuredAt < :threshold")
    void deactivateStaleMetrics(@Param("threshold") LocalDateTime threshold);

    @Query("DELETE FROM Metric m WHERE m.measuredAt < :threshold")
    void deleteOldMetrics(@Param("threshold") LocalDateTime threshold);

    // Latest metrics queries
    @Query("SELECT m FROM Metric m WHERE m.active = true AND m.service = :service AND m.metricName = :metricName ORDER BY m.measuredAt DESC")
    List<Metric> findLatestByServiceAndMetric(@Param("service") String service, @Param("metricName") String metricName);

    @Query("SELECT m FROM Metric m WHERE m.active = true AND m.service = :service ORDER BY m.measuredAt DESC")
    List<Metric> findLatestByService(@Param("service") String service);

    @Query("SELECT DISTINCT m.service, m.metricName FROM Metric m WHERE m.active = true ORDER BY m.service, m.metricName")
    List<Object[]> findUniqueServiceMetricPairs();

    @Query("SELECT DISTINCT m.service FROM Metric m WHERE m.active = true ORDER BY m.service")
    List<String> findUniqueServices();

    @Query("SELECT DISTINCT m.metricName FROM Metric m WHERE m.active = true ORDER BY m.metricName")
    List<String> findUniqueMetricNames();

    @Query("SELECT DISTINCT m.category FROM Metric m WHERE m.active = true ORDER BY m.category")
    List<String> findUniqueCategories();

    @Query("SELECT DISTINCT m.environment FROM Metric m WHERE m.active = true ORDER BY m.environment")
    List<String> findUniqueEnvironments();
}
