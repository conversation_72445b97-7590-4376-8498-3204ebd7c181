package com.securecloudstorage.monitoring.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Metric Entity
 * Thực thể lưu trữ metrics data
 */
@Entity
@Table(name = "metrics")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Metric {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "metric_id", unique = true, nullable = false)
    private String metricId;

    @Column(name = "metric_name", nullable = false)
    private String metricName;

    @Column(name = "metric_type", nullable = false)
    private String metricType; // GAUGE, COUNTER, HISTOGRAM, SUMMARY

    @Column(name = "value", nullable = false)
    private Double value;

    @Column(name = "unit")
    private String unit;

    @Column(name = "service", nullable = false)
    private String service;

    @Column(name = "instance")
    private String instance;

    @Column(name = "host")
    private String host;

    @Column(name = "environment")
    private String environment; // DEV, STAGING, PROD

    @Column(name = "namespace")
    private String namespace;

    @Column(name = "category")
    private String category; // SYSTEM, APPLICATION, BUSINESS

    @Column(name = "subcategory")
    private String subcategory;

    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags;

    @Column(name = "labels", columnDefinition = "TEXT")
    private String labels;

    @Column(name = "dimensions", columnDefinition = "TEXT")
    private String dimensions;

    @Column(name = "description")
    private String description;

    @Column(name = "source")
    private String source;

    @Column(name = "collector")
    private String collector;

    @Column(name = "collection_interval")
    private Integer collectionInterval; // seconds

    @Column(name = "retention_period")
    private Integer retentionPeriod; // days

    @Column(name = "aggregation_method")
    private String aggregationMethod; // AVG, SUM, MIN, MAX, COUNT

    @Column(name = "aggregation_window")
    private Integer aggregationWindow; // seconds

    @Column(name = "sampling_rate")
    private Double samplingRate;

    @Column(name = "threshold_warning")
    private Double thresholdWarning;

    @Column(name = "threshold_critical")
    private Double thresholdCritical;

    @Column(name = "threshold_operator")
    private String thresholdOperator; // GT, LT, EQ, NEQ, GTE, LTE

    @Column(name = "alert_enabled")
    private Boolean alertEnabled;

    @Column(name = "alert_rule_id")
    private String alertRuleId;

    @Column(name = "dashboard_id")
    private String dashboardId;

    @Column(name = "chart_type")
    private String chartType; // LINE, BAR, PIE, GAUGE, HEATMAP

    @Column(name = "display_name")
    private String displayName;

    @Column(name = "display_unit")
    private String displayUnit;

    @Column(name = "display_format")
    private String displayFormat;

    @Column(name = "display_color")
    private String displayColor;

    @Column(name = "sort_order")
    private Integer sortOrder;

    @Column(name = "is_key_metric")
    private Boolean isKeyMetric;

    @Column(name = "is_sla_metric")
    private Boolean isSlaMetric;

    @Column(name = "sla_target")
    private Double slaTarget;

    @Column(name = "sla_operator")
    private String slaOperator;

    @Column(name = "business_impact")
    private String businessImpact; // LOW, MEDIUM, HIGH, CRITICAL

    @Column(name = "trend_direction")
    private String trendDirection; // UP, DOWN, STABLE

    @Column(name = "trend_percentage")
    private Double trendPercentage;

    @Column(name = "baseline_value")
    private Double baselineValue;

    @Column(name = "baseline_period")
    private Integer baselinePeriod; // days

    @Column(name = "anomaly_detection_enabled")
    private Boolean anomalyDetectionEnabled;

    @Column(name = "anomaly_sensitivity")
    private String anomalySensitivity; // LOW, MEDIUM, HIGH

    @Column(name = "anomaly_threshold")
    private Double anomalyThreshold;

    @Column(name = "forecast_enabled")
    private Boolean forecastEnabled;

    @Column(name = "forecast_horizon")
    private Integer forecastHorizon; // days

    @Column(name = "forecast_confidence")
    private Double forecastConfidence;

    @Column(name = "data_quality_score")
    private Double dataQualityScore;

    @Column(name = "data_completeness")
    private Double dataCompleteness;

    @Column(name = "data_accuracy")
    private Double dataAccuracy;

    @Column(name = "data_freshness")
    private Integer dataFreshness; // seconds since last update

    @Column(name = "validation_rule")
    private String validationRule;

    @Column(name = "validation_status")
    private String validationStatus; // VALID, INVALID, PENDING

    @Column(name = "validation_error")
    private String validationError;

    @Column(name = "active")
    private Boolean active;

    @Column(name = "archived")
    private Boolean archived;

    @Column(name = "archived_at")
    private LocalDateTime archivedAt;

    @Column(name = "archived_by")
    private String archivedBy;

    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata;

    @Column(name = "external_id")
    private String externalId;

    @Column(name = "external_system")
    private String externalSystem;

    @Column(name = "integration_data", columnDefinition = "TEXT")
    private String integrationData;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "measured_at")
    private LocalDateTime measuredAt;

    @Column(name = "processed_at")
    private LocalDateTime processedAt;

    @Column(name = "last_alert_at")
    private LocalDateTime lastAlertAt;

    @Column(name = "alert_count")
    private Integer alertCount;

    @Column(name = "min_value")
    private Double minValue;

    @Column(name = "max_value")
    private Double maxValue;

    @Column(name = "avg_value")
    private Double avgValue;

    @Column(name = "sum_value")
    private Double sumValue;

    @Column(name = "count_value")
    private Long countValue;

    @Column(name = "percentile_50")
    private Double percentile50;

    @Column(name = "percentile_90")
    private Double percentile90;

    @Column(name = "percentile_95")
    private Double percentile95;

    @Column(name = "percentile_99")
    private Double percentile99;

    @Column(name = "standard_deviation")
    private Double standardDeviation;

    @Column(name = "variance")
    private Double variance;

    // Helper methods

    @JsonIgnore
    public boolean isActive() {
        return Boolean.TRUE.equals(active);
    }

    @JsonIgnore
    public boolean isArchived() {
        return Boolean.TRUE.equals(archived);
    }

    @JsonIgnore
    public boolean isKeyMetric() {
        return Boolean.TRUE.equals(isKeyMetric);
    }

    @JsonIgnore
    public boolean isSlaMetric() {
        return Boolean.TRUE.equals(isSlaMetric);
    }

    @JsonIgnore
    public boolean isAlertEnabled() {
        return Boolean.TRUE.equals(alertEnabled);
    }

    @JsonIgnore
    public boolean isAnomalyDetectionEnabled() {
        return Boolean.TRUE.equals(anomalyDetectionEnabled);
    }

    @JsonIgnore
    public boolean isForecastEnabled() {
        return Boolean.TRUE.equals(forecastEnabled);
    }

    @JsonIgnore
    public boolean isWarningThresholdExceeded() {
        if (thresholdWarning == null || value == null) return false;
        return evaluateThreshold(value, thresholdWarning, thresholdOperator);
    }

    @JsonIgnore
    public boolean isCriticalThresholdExceeded() {
        if (thresholdCritical == null || value == null) return false;
        return evaluateThreshold(value, thresholdCritical, thresholdOperator);
    }

    @JsonIgnore
    public boolean isSlaTarget() {
        if (slaTarget == null || value == null) return false;
        return evaluateThreshold(value, slaTarget, slaOperator);
    }

    @JsonIgnore
    public boolean isStale() {
        if (measuredAt == null) return true;
        int stalenessThreshold = collectionInterval != null ? collectionInterval * 3 : 300; // 5 minutes default
        return LocalDateTime.now().isAfter(measuredAt.plusSeconds(stalenessThreshold));
    }

    @JsonIgnore
    public boolean isDataQualityGood() {
        return dataQualityScore != null && dataQualityScore >= 0.8;
    }

    @JsonProperty("ageInMinutes")
    public long getAgeInMinutes() {
        if (measuredAt != null) {
            return java.time.Duration.between(measuredAt, LocalDateTime.now()).toMinutes();
        }
        return 0;
    }

    @JsonProperty("freshnessScore")
    public double getFreshnessScore() {
        if (dataFreshness == null) return 0.0;
        if (dataFreshness <= 60) return 1.0; // Fresh if updated within 1 minute
        if (dataFreshness <= 300) return 0.8; // Good if updated within 5 minutes
        if (dataFreshness <= 900) return 0.6; // Fair if updated within 15 minutes
        if (dataFreshness <= 1800) return 0.4; // Poor if updated within 30 minutes
        return 0.0; // Stale if older than 30 minutes
    }

    @JsonProperty("trendIndicator")
    public String getTrendIndicator() {
        if (trendDirection == null) return "UNKNOWN";
        if (trendPercentage == null) return trendDirection;
        
        String indicator = trendDirection;
        if (trendPercentage > 0) {
            indicator += " (+" + String.format("%.1f", trendPercentage) + "%)";
        } else if (trendPercentage < 0) {
            indicator += " (" + String.format("%.1f", trendPercentage) + "%)";
        }
        return indicator;
    }

    @PrePersist
    public void prePersist() {
        if (metricId == null) {
            metricId = "METRIC-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        }
        if (active == null) {
            active = true;
        }
        if (archived == null) {
            archived = false;
        }
        if (alertEnabled == null) {
            alertEnabled = false;
        }
        if (isKeyMetric == null) {
            isKeyMetric = false;
        }
        if (isSlaMetric == null) {
            isSlaMetric = false;
        }
        if (anomalyDetectionEnabled == null) {
            anomalyDetectionEnabled = false;
        }
        if (forecastEnabled == null) {
            forecastEnabled = false;
        }
        if (alertCount == null) {
            alertCount = 0;
        }
        if (measuredAt == null) {
            measuredAt = LocalDateTime.now();
        }
        if (environment == null) {
            environment = "PROD";
        }
        if (category == null) {
            category = "SYSTEM";
        }
        if (metricType == null) {
            metricType = "GAUGE";
        }
        if (collectionInterval == null) {
            collectionInterval = 60; // 1 minute default
        }
        if (retentionPeriod == null) {
            retentionPeriod = 30; // 30 days default
        }
        if (businessImpact == null) {
            businessImpact = "LOW";
        }
        if (validationStatus == null) {
            validationStatus = "PENDING";
        }
    }

    @PreUpdate
    public void preUpdate() {
        if (archived != null && archived && archivedAt == null) {
            archivedAt = LocalDateTime.now();
        }
        if (value != null && measuredAt != null) {
            processedAt = LocalDateTime.now();
            dataFreshness = (int) java.time.Duration.between(measuredAt, processedAt).getSeconds();
        }
    }

    private boolean evaluateThreshold(double value, double threshold, String operator) {
        if (operator == null) return false;
        switch (operator.toUpperCase()) {
            case "GT": return value > threshold;
            case "LT": return value < threshold;
            case "EQ": return value == threshold;
            case "NEQ": return value != threshold;
            case "GTE": return value >= threshold;
            case "LTE": return value <= threshold;
            default: return false;
        }
    }
}
