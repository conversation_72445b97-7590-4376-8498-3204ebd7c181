package com.securecloudstorage.security.repository;

import com.securecloudstorage.security.entity.BlockedIp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Blocked IP Repository
 * Repository cho BlockedIp entity
 */
@Repository
public interface BlockedIpRepository extends JpaRepository<BlockedIp, Long> {

    // Find methods
    BlockedIp findByIpAddress(String ipAddress);
    
    List<BlockedIp> findByIsActiveTrue();
    
    List<BlockedIp> findByIsActiveFalse();
    
    List<BlockedIp> findByBlockType(String blockType);
    
    List<BlockedIp> findBySeverity(String severity);
    
    List<BlockedIp> findByBlockedAtBetween(LocalDateTime start, LocalDateTime end);
    
    List<BlockedIp> findByExpiryDateBefore(LocalDateTime expiry);
    
    List<BlockedIp> findByIsActiveTrueAndExpiryDateBefore(LocalDateTime expiry);

    // Exists methods
    boolean existsByIpAddress(String ipAddress);
    
    boolean existsByIpAddressAndIsActiveTrue(String ipAddress);

    // Count methods
    long countByIsActiveTrue();
    
    long countByIsActiveFalse();
    
    long countByBlockType(String blockType);
    
    long countBySeverity(String severity);
    
    long countByBlockedAtAfter(LocalDateTime timestamp);

    // Custom queries
    @Query("SELECT b FROM BlockedIp b WHERE b.isActive = true AND (b.expiryDate IS NULL OR b.expiryDate > :now)")
    List<BlockedIp> findActiveNonExpiredBlocks(@Param("now") LocalDateTime now);
    
    @Query("SELECT b FROM BlockedIp b WHERE b.isActive = true AND b.expiryDate IS NOT NULL AND b.expiryDate <= :now")
    List<BlockedIp> findExpiredBlocks(@Param("now") LocalDateTime now);
    
    @Query("SELECT b.severity, COUNT(b) FROM BlockedIp b WHERE b.isActive = true GROUP BY b.severity")
    List<Object[]> getActiveBlocskBySeverity();
    
    @Query("SELECT b.blockType, COUNT(b) FROM BlockedIp b WHERE b.isActive = true GROUP BY b.blockType")
    List<Object[]> getActiveBlocksByType();
    
    @Query("SELECT DATE(b.blockedAt) as date, COUNT(b) as count FROM BlockedIp b WHERE b.blockedAt > :timestamp GROUP BY DATE(b.blockedAt) ORDER BY date DESC")
    List<Object[]> getDailyBlockCounts(@Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT b FROM BlockedIp b WHERE b.isActive = true AND b.severity = 'CRITICAL' ORDER BY b.blockedAt DESC")
    List<BlockedIp> findCriticalActiveBlocks();
    
    @Query("SELECT b FROM BlockedIp b WHERE b.blockType = 'TEMPORARY' AND b.isActive = true AND b.expiryDate <= :deadline")
    List<BlockedIp> findTemporaryBlocksNearExpiry(@Param("deadline") LocalDateTime deadline);
    
    @Query("SELECT b FROM BlockedIp b WHERE b.isActive = true AND b.blockedAt < :timestamp ORDER BY b.blockedAt ASC")
    List<BlockedIp> findOldActiveBlocks(@Param("timestamp") LocalDateTime timestamp);
}
