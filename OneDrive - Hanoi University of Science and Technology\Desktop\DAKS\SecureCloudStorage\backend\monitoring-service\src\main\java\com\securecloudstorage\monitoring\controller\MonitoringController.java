package com.securecloudstorage.monitoring.controller;

import com.securecloudstorage.monitoring.dto.SystemMetricsResponse;
import com.securecloudstorage.monitoring.dto.ServiceHealthResponse;
import com.securecloudstorage.monitoring.dto.AlertResponse;
import com.securecloudstorage.monitoring.dto.DashboardResponse;
import com.securecloudstorage.monitoring.service.MetricsService;
import com.securecloudstorage.monitoring.service.HealthCheckService;
import com.securecloudstorage.monitoring.service.AlertService;
import com.securecloudstorage.monitoring.service.DashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Monitoring Controller
 * API endpoints for system monitoring and metrics
 */
@RestController
@RequestMapping("/api/monitoring")
@RequiredArgsConstructor
@Slf4j
public class MonitoringController {

    private final MetricsService metricsService;
    private final HealthCheckService healthCheckService;
    private final AlertService alertService;
    private final DashboardService dashboardService;

    /**
     * Get system metrics
     */
    @GetMapping("/metrics")
    public ResponseEntity<SystemMetricsResponse> getSystemMetrics() {
        try {
            SystemMetricsResponse metrics = metricsService.getSystemMetrics();
            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            log.error("Error getting system metrics: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get metrics for specific service
     */
    @GetMapping("/metrics/{serviceName}")
    public ResponseEntity<Map<String, Object>> getServiceMetrics(@PathVariable String serviceName) {
        try {
            Map<String, Object> metrics = metricsService.getServiceMetrics(serviceName);
            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            log.error("Error getting metrics for service {}: ", serviceName, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get health status of all services
     */
    @GetMapping("/health")
    public ResponseEntity<List<ServiceHealthResponse>> getHealthStatus() {
        try {
            List<ServiceHealthResponse> healthStatus = healthCheckService.checkAllServices();
            return ResponseEntity.ok(healthStatus);
        } catch (Exception e) {
            log.error("Error getting health status: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get health status of specific service
     */
    @GetMapping("/health/{serviceName}")
    public ResponseEntity<ServiceHealthResponse> getServiceHealth(@PathVariable String serviceName) {
        try {
            ServiceHealthResponse health = healthCheckService.checkService(serviceName);
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            log.error("Error getting health for service {}: ", serviceName, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get system alerts
     */
    @GetMapping("/alerts")
    public ResponseEntity<List<AlertResponse>> getAlerts(
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "severity", required = false) String severity,
            @RequestParam(value = "service", required = false) String service) {
        try {
            List<AlertResponse> alerts = alertService.getAlerts(status, severity, service);
            return ResponseEntity.ok(alerts);
        } catch (Exception e) {
            log.error("Error getting alerts: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Create manual alert
     */
    @PostMapping("/alerts")
    public ResponseEntity<AlertResponse> createAlert(@RequestBody Map<String, Object> alertData) {
        try {
            AlertResponse alert = alertService.createAlert(alertData);
            return ResponseEntity.ok(alert);
        } catch (Exception e) {
            log.error("Error creating alert: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Acknowledge alert
     */
    @PostMapping("/alerts/{alertId}/acknowledge")
    public ResponseEntity<String> acknowledgeAlert(@PathVariable Long alertId) {
        try {
            alertService.acknowledgeAlert(alertId);
            return ResponseEntity.ok("Alert acknowledged successfully");
        } catch (Exception e) {
            log.error("Error acknowledging alert {}: ", alertId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Resolve alert
     */
    @PostMapping("/alerts/{alertId}/resolve")
    public ResponseEntity<String> resolveAlert(@PathVariable Long alertId) {
        try {
            alertService.resolveAlert(alertId);
            return ResponseEntity.ok("Alert resolved successfully");
        } catch (Exception e) {
            log.error("Error resolving alert {}: ", alertId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get dashboard data
     */
    @GetMapping("/dashboard")
    public ResponseEntity<DashboardResponse> getDashboard() {
        try {
            DashboardResponse dashboard = dashboardService.getDashboardData();
            return ResponseEntity.ok(dashboard);
        } catch (Exception e) {
            log.error("Error getting dashboard data: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get system performance data
     */
    @GetMapping("/performance")
    public ResponseEntity<Map<String, Object>> getPerformanceData(
            @RequestParam(value = "timeRange", defaultValue = "1h") String timeRange) {
        try {
            Map<String, Object> performance = metricsService.getPerformanceData(timeRange);
            return ResponseEntity.ok(performance);
        } catch (Exception e) {
            log.error("Error getting performance data: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get system logs
     */
    @GetMapping("/logs")
    public ResponseEntity<Map<String, Object>> getLogs(
            @RequestParam(value = "service", required = false) String service,
            @RequestParam(value = "level", required = false) String level,
            @RequestParam(value = "lines", defaultValue = "100") int lines) {
        try {
            Map<String, Object> logs = metricsService.getLogs(service, level, lines);
            return ResponseEntity.ok(logs);
        } catch (Exception e) {
            log.error("Error getting logs: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get capacity planning data
     */
    @GetMapping("/capacity")
    public ResponseEntity<Map<String, Object>> getCapacityData() {
        try {
            Map<String, Object> capacity = metricsService.getCapacityData();
            return ResponseEntity.ok(capacity);
        } catch (Exception e) {
            log.error("Error getting capacity data: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get SLA metrics
     */
    @GetMapping("/sla")
    public ResponseEntity<Map<String, Object>> getSLAMetrics() {
        try {
            Map<String, Object> sla = metricsService.getSLAMetrics();
            return ResponseEntity.ok(sla);
        } catch (Exception e) {
            log.error("Error getting SLA metrics: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get security metrics
     */
    @GetMapping("/security")
    public ResponseEntity<Map<String, Object>> getSecurityMetrics() {
        try {
            Map<String, Object> security = metricsService.getSecurityMetrics();
            return ResponseEntity.ok(security);
        } catch (Exception e) {
            log.error("Error getting security metrics: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get application metrics
     */
    @GetMapping("/application")
    public ResponseEntity<Map<String, Object>> getApplicationMetrics() {
        try {
            Map<String, Object> application = metricsService.getApplicationMetrics();
            return ResponseEntity.ok(application);
        } catch (Exception e) {
            log.error("Error getting application metrics: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get network metrics
     */
    @GetMapping("/network")
    public ResponseEntity<Map<String, Object>> getNetworkMetrics() {
        try {
            Map<String, Object> network = metricsService.getNetworkMetrics();
            return ResponseEntity.ok(network);
        } catch (Exception e) {
            log.error("Error getting network metrics: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get database metrics
     */
    @GetMapping("/database")
    public ResponseEntity<Map<String, Object>> getDatabaseMetrics() {
        try {
            Map<String, Object> database = metricsService.getDatabaseMetrics();
            return ResponseEntity.ok(database);
        } catch (Exception e) {
            log.error("Error getting database metrics: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get custom metrics
     */
    @GetMapping("/custom/{metricName}")
    public ResponseEntity<Map<String, Object>> getCustomMetric(@PathVariable String metricName) {
        try {
            Map<String, Object> metric = metricsService.getCustomMetric(metricName);
            return ResponseEntity.ok(metric);
        } catch (Exception e) {
            log.error("Error getting custom metric {}: ", metricName, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Export metrics
     */
    @GetMapping("/export")
    public ResponseEntity<String> exportMetrics(
            @RequestParam(value = "format", defaultValue = "json") String format) {
        try {
            String exportData = metricsService.exportMetrics(format);
            return ResponseEntity.ok(exportData);
        } catch (Exception e) {
            log.error("Error exporting metrics: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
