package com.securecloudstorage.storage.controller;

import com.securecloudstorage.storage.dto.FileUploadResponse;
import com.securecloudstorage.storage.dto.FileInfoResponse;
import com.securecloudstorage.storage.service.FileStorageService;
import com.securecloudstorage.storage.service.SecurityScanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * File Storage Controller
 * Xử lý upload, download, và quản lý file với AI-Malware scanning
 */
@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
@Slf4j
public class FileStorageController {

    private final FileStorageService fileStorageService;
    private final SecurityScanService securityScanService;

    /**
     * Upload file với AI-Malware scanning
     */
    @PostMapping("/upload")
    public ResponseEntity<FileUploadResponse> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "description", required = false) String description,
            Authentication authentication) {
        
        try {
            String userId = authentication.getName();
            log.info("User {} uploading file: {}", userId, file.getOriginalFilename());

            // Validate file
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(FileUploadResponse.builder()
                        .success(false)
                        .message("File is empty")
                        .build());
            }

            // Check file size
            if (file.getSize() > 100 * 1024 * 1024) { // 100MB limit
                return ResponseEntity.badRequest()
                    .body(FileUploadResponse.builder()
                        .success(false)
                        .message("File size exceeds maximum limit (100MB)")
                        .build());
            }

            // Perform AI-Malware scan asynchronously
            CompletableFuture<Boolean> scanResult = securityScanService.scanFileAsync(file);

            // Store file
            FileUploadResponse response = fileStorageService.storeFile(file, userId, description);

            // Wait for scan result
            boolean isSafe = scanResult.get();
            response.setScanned(true);
            response.setSafe(isSafe);

            if (!isSafe) {
                // Quarantine file
                fileStorageService.quarantineFile(response.getFileId());
                response.setMessage("File uploaded but quarantined due to security concerns");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error uploading file: ", e);
            return ResponseEntity.internalServerError()
                .body(FileUploadResponse.builder()
                    .success(false)
                    .message("Upload failed: " + e.getMessage())
                    .build());
        }
    }

    /**
     * Download file
     */
    @GetMapping("/download/{fileId}")
    public ResponseEntity<Resource> downloadFile(
            @PathVariable String fileId,
            HttpServletRequest request,
            Authentication authentication) {
        
        try {
            String userId = authentication.getName();
            log.info("User {} downloading file: {}", userId, fileId);

            // Check access permission
            if (!fileStorageService.hasAccessPermission(fileId, userId)) {
                return ResponseEntity.status(403).build();
            }

            // Get file resource
            Resource resource = fileStorageService.loadFileAsResource(fileId);
            String contentType = fileStorageService.getContentType(fileId, request);

            // Log download
            fileStorageService.logDownload(fileId, userId);

            return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, 
                    "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);

        } catch (Exception e) {
            log.error("Error downloading file {}: ", fileId, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get file info
     */
    @GetMapping("/{fileId}")
    public ResponseEntity<FileInfoResponse> getFileInfo(
            @PathVariable String fileId,
            Authentication authentication) {
        
        try {
            String userId = authentication.getName();
            
            if (!fileStorageService.hasAccessPermission(fileId, userId)) {
                return ResponseEntity.status(403).build();
            }

            FileInfoResponse fileInfo = fileStorageService.getFileInfo(fileId);
            return ResponseEntity.ok(fileInfo);

        } catch (Exception e) {
            log.error("Error getting file info {}: ", fileId, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * List user files
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<FileInfoResponse>> getUserFiles(
            @PathVariable String userId,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            Authentication authentication) {
        
        try {
            // Check permission
            if (!userId.equals(authentication.getName())) {
                return ResponseEntity.status(403).build();
            }

            List<FileInfoResponse> files = fileStorageService.getUserFiles(userId, page, size);
            return ResponseEntity.ok(files);

        } catch (Exception e) {
            log.error("Error getting user files for {}: ", userId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Delete file
     */
    @DeleteMapping("/{fileId}")
    public ResponseEntity<Void> deleteFile(
            @PathVariable String fileId,
            Authentication authentication) {
        
        try {
            String userId = authentication.getName();
            
            if (!fileStorageService.hasAccessPermission(fileId, userId)) {
                return ResponseEntity.status(403).build();
            }

            fileStorageService.deleteFile(fileId, userId);
            return ResponseEntity.ok().build();

        } catch (Exception e) {
            log.error("Error deleting file {}: ", fileId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Share file
     */
    @PostMapping("/{fileId}/share")
    public ResponseEntity<String> shareFile(
            @PathVariable String fileId,
            @RequestParam String targetUserId,
            @RequestParam(value = "permission", defaultValue = "READ") String permission,
            Authentication authentication) {
        
        try {
            String userId = authentication.getName();
            
            if (!fileStorageService.hasAccessPermission(fileId, userId)) {
                return ResponseEntity.status(403).build();
            }

            String shareToken = fileStorageService.shareFile(fileId, userId, targetUserId, permission);
            return ResponseEntity.ok(shareToken);

        } catch (Exception e) {
            log.error("Error sharing file {}: ", fileId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get file scan results
     */
    @GetMapping("/{fileId}/scan-result")
    public ResponseEntity<Object> getScanResult(
            @PathVariable String fileId,
            Authentication authentication) {
        
        try {
            String userId = authentication.getName();
            
            if (!fileStorageService.hasAccessPermission(fileId, userId)) {
                return ResponseEntity.status(403).build();
            }

            Object scanResult = securityScanService.getScanResult(fileId);
            return ResponseEntity.ok(scanResult);

        } catch (Exception e) {
            log.error("Error getting scan result for {}: ", fileId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
