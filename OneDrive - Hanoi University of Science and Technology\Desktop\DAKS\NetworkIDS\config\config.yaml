# Network IDS Configuration
# <PERSON><PERSON><PERSON> hình hệ thống phát hiện xâm nhập mạng

# Logging Configuration
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/network_ids.log"
  max_size: 100MB
  backup_count: 5
  console: true

# Packet Capture Configuration
capture:
  interface: "auto"  # "auto" để tự động chọn interface chính
  promiscuous: true
  buffer_size: 65536
  timeout: 1000
  filter: ""  # BPF filter, để trống để capture tất cả
  max_packets: 0  # 0 = unlimited
  protocols:
    - tcp
    - udp
    - icmp
    - arp

# Traffic Analysis Configuration
analysis:
  window_size: 60  # seconds
  features:
    - packet_size
    - inter_arrival_time
    - protocol_distribution
    - port_distribution
    - connection_duration
    - byte_frequency
    - packet_count
    - flow_duration
  aggregation_interval: 10  # seconds
  baseline_period: 300  # seconds for establishing baseline

# Machine Learning Configuration
ml:
  models:
    isolation_forest:
      enabled: true
      contamination: 0.1
      n_estimators: 100
      max_samples: 256
    
    autoencoder:
      enabled: true
      encoding_dim: 32
      epochs: 50
      batch_size: 32
      validation_split: 0.2
    
    lstm:
      enabled: true
      sequence_length: 10
      hidden_units: 50
      epochs: 30
      batch_size: 32
    
    random_forest:
      enabled: true
      n_estimators: 100
      max_depth: 10
      min_samples_split: 2
  
  training:
    data_path: "data/training/"
    model_path: "models/"
    retrain_interval: 86400  # seconds (24 hours)
    min_samples: 1000
  
  detection:
    threshold: 0.7
    confidence_threshold: 0.8
    ensemble_voting: true

# Signature Detection Configuration
signature:
  enabled: true
  rules_path: "rules/"
  update_interval: 3600  # seconds
  rules:
    - ddos_detection
    - port_scan_detection
    - sql_injection
    - xss_detection
    - malware_communication
    - brute_force_detection
    - arp_spoofing
    - dns_poisoning

# Alert Configuration
alerts:
  enabled: true
  channels:
    email:
      enabled: true
      smtp_server: "smtp.gmail.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "your_app_password"
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"
    
    sms:
      enabled: false
      provider: "twilio"
      account_sid: "your_account_sid"
      auth_token: "your_auth_token"
      from_number: "+**********"
      to_numbers:
        - "+**********"
    
    webhook:
      enabled: false
      url: "https://your-webhook-url.com/alerts"
      headers:
        Authorization: "Bearer your_token"
    
    slack:
      enabled: false
      webhook_url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
      channel: "#security-alerts"
  
  severity_levels:
    LOW: 
      throttle: 300  # seconds
      channels: ["email"]
    MEDIUM:
      throttle: 60
      channels: ["email", "slack"]
    HIGH:
      throttle: 0
      channels: ["email", "sms", "slack", "webhook"]
    CRITICAL:
      throttle: 0
      channels: ["email", "sms", "slack", "webhook"]

# Dashboard Configuration
dashboard:
  enabled: true
  host: "0.0.0.0"
  port: 8080
  debug: false
  secret_key: "your_secret_key_here"
  
  authentication:
    enabled: true
    users:
      admin:
        password: "admin123"  # Change this!
        role: "admin"
      analyst:
        password: "analyst123"
        role: "analyst"
  
  features:
    real_time_monitoring: true
    historical_analysis: true
    threat_intelligence: true
    system_health: true
    configuration_management: true
  
  refresh_rate: 5  # seconds

# Database Configuration
database:
  type: "sqlite"  # sqlite, postgresql, mysql
  path: "data/network_ids.db"
  
  # For PostgreSQL/MySQL
  host: "localhost"
  port: 5432
  username: "ids_user"
  password: "ids_password"
  database: "network_ids"
  
  # Connection pool
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30

# Performance Configuration
performance:
  max_memory_usage: 2048  # MB
  max_cpu_usage: 80  # percentage
  packet_queue_size: 10000
  analysis_queue_size: 1000
  worker_threads: 4
  cache_size: 1000  # number of entries
  
  # Monitoring intervals
  health_check_interval: 30  # seconds
  performance_log_interval: 300  # seconds

# Security Configuration
security:
  encryption:
    enabled: true
    algorithm: "AES-256"
    key_file: "config/encryption.key"
  
  api_security:
    rate_limiting: true
    max_requests_per_minute: 100
    authentication_required: true
  
  data_retention:
    packets: 7  # days
    alerts: 30  # days
    logs: 90  # days
    reports: 365  # days

# Threat Intelligence Configuration
threat_intelligence:
  enabled: true
  sources:
    - name: "abuse.ch"
      url: "https://feodotracker.abuse.ch/downloads/ipblocklist.csv"
      format: "csv"
      update_interval: 3600
    
    - name: "malware_domains"
      url: "https://mirror1.malwaredomains.com/files/justdomains"
      format: "text"
      update_interval: 3600
  
  local_sources:
    - "threat_feeds/custom_indicators.json"
    - "threat_feeds/internal_blacklist.txt"

# Reporting Configuration
reporting:
  enabled: true
  output_path: "reports/"
  formats:
    - json
    - html
    - pdf
  
  scheduled_reports:
    daily:
      enabled: true
      time: "08:00"
      recipients: ["<EMAIL>"]
    
    weekly:
      enabled: true
      day: "monday"
      time: "09:00"
      recipients: ["<EMAIL>"]
    
    monthly:
      enabled: true
      day: 1
      time: "10:00"
      recipients: ["<EMAIL>"]

# Network Configuration
network:
  interfaces:
    - name: "eth0"
      description: "Primary network interface"
      monitor: true
    
    - name: "wlan0"
      description: "Wireless interface"
      monitor: false
  
  subnets:
    internal:
      - "***********/24"
      - "10.0.0.0/8"
      - "**********/12"
    
    dmz:
      - "*************/24"
    
    external:
      - "0.0.0.0/0"
  
  ports:
    monitored:
      - 22    # SSH
      - 23    # Telnet
      - 25    # SMTP
      - 53    # DNS
      - 80    # HTTP
      - 110   # POP3
      - 143   # IMAP
      - 443   # HTTPS
      - 993   # IMAPS
      - 995   # POP3S
      - 1433  # SQL Server
      - 3306  # MySQL
      - 3389  # RDP
      - 5432  # PostgreSQL
    
    high_priority:
      - 22
      - 443
      - 3389

# Advanced Configuration
advanced:
  experimental_features:
    deep_packet_inspection: true
    behavioral_analysis: true
    network_topology_mapping: true
    threat_hunting: true
  
  optimization:
    packet_filtering: true
    flow_caching: true
    parallel_processing: true
    memory_optimization: true
  
  integration:
    siem_export: true
    api_endpoints: true
    plugin_support: true
