#!/usr/bin/env python3
"""
Network Intrusion Detection System (IDS)
=========================================

Hệ thống phát hiện xâm nhập mạng sử dụng AI/ML
Tác giả: AI Assistant
<PERSON><PERSON><PERSON>: 1.0.0
"""

import argparse
import os
import sys
import signal
import threading
import time
from pathlib import Path

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.logger import setup_logger
from utils.config import Config
from capture.packet_capture import PacketCapture
from analysis.traffic_analyzer import TrafficAnalyzer
from detection.ml_detector import MLDetector
from detection.signature_detector import SignatureDetector
from dashboard.web_dashboard import WebDashboard
from alerts.alert_manager import AlertManager

class NetworkIDS:
    """
    <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> c<PERSON> hệ thống Network IDS
    """
    
    def __init__(self, config_path="config/config.yaml"):
        """
        Khởi tạo hệ thống IDS
        
        Args:
            config_path (str): Đ<PERSON>ờng dẫn đến file cấu hình
        """
        self.config = Config(config_path)
        self.logger = setup_logger("NetworkIDS", self.config.get_log_config())
        
        # Khởi tạo các component
        self.packet_capture = None
        self.traffic_analyzer = None
        self.ml_detector = None
        self.signature_detector = None
        self.dashboard = None
        self.alert_manager = None
        
        # Threading control
        self.running = False
        self.threads = []
        
        # Signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("Network IDS initialized successfully")
    
    def _signal_handler(self, signum, frame):
        """Xử lý tín hiệu dừng hệ thống"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
    
    def initialize_components(self):
        """Khởi tạo tất cả các component"""
        try:
            # Packet capture
            self.packet_capture = PacketCapture(self.config.get_capture_config())
            
            # Traffic analyzer
            self.traffic_analyzer = TrafficAnalyzer(self.config.get_analysis_config())
            
            # ML detector
            self.ml_detector = MLDetector(self.config.get_ml_config())
            
            # Signature detector
            self.signature_detector = SignatureDetector(self.config.get_signature_config())
            
            # Alert manager
            self.alert_manager = AlertManager(self.config.get_alert_config())
            
            # Dashboard (nếu được kích hoạt)
            if self.config.get_dashboard_config().get('enabled', False):
                self.dashboard = WebDashboard(self.config.get_dashboard_config())
            
            self.logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing components: {str(e)}")
            return False
    
    def start_monitoring(self):
        """Bắt đầu giám sát mạng"""
        if not self.initialize_components():
            return False
        
        self.running = True
        self.logger.info("Starting network monitoring...")
        
        # Start packet capture thread
        capture_thread = threading.Thread(
            target=self._capture_worker,
            name="PacketCapture"
        )
        capture_thread.daemon = True
        capture_thread.start()
        self.threads.append(capture_thread)
        
        # Start analysis thread
        analysis_thread = threading.Thread(
            target=self._analysis_worker,
            name="TrafficAnalysis"
        )
        analysis_thread.daemon = True
        analysis_thread.start()
        self.threads.append(analysis_thread)
        
        # Start dashboard if enabled
        if self.dashboard:
            dashboard_thread = threading.Thread(
                target=self._dashboard_worker,
                name="WebDashboard"
            )
            dashboard_thread.daemon = True
            dashboard_thread.start()
            self.threads.append(dashboard_thread)
        
        # Main monitoring loop
        try:
            while self.running:
                time.sleep(1)
                self._check_system_health()
        except KeyboardInterrupt:
            self.logger.info("Monitoring stopped by user")
        finally:
            self.stop()
        
        return True
    
    def _capture_worker(self):
        """Worker thread cho packet capture"""
        try:
            self.packet_capture.start_capture(self._packet_callback)
        except Exception as e:
            self.logger.error(f"Packet capture error: {str(e)}")
    
    def _analysis_worker(self):
        """Worker thread cho traffic analysis"""
        try:
            while self.running:
                # Phân tích traffic từ queue
                self.traffic_analyzer.process_queue()
                time.sleep(0.1)
        except Exception as e:
            self.logger.error(f"Traffic analysis error: {str(e)}")
    
    def _dashboard_worker(self):
        """Worker thread cho web dashboard"""
        try:
            self.dashboard.run()
        except Exception as e:
            self.logger.error(f"Dashboard error: {str(e)}")
    
    def _packet_callback(self, packet):
        """
        Callback function cho packet capture
        
        Args:
            packet: Gói tin đã capture
        """
        try:
            # Phân tích gói tin
            analysis_result = self.traffic_analyzer.analyze_packet(packet)
            
            # Phát hiện anomaly với ML
            ml_result = self.ml_detector.detect_anomaly(analysis_result)
            
            # Phát hiện signature
            sig_result = self.signature_detector.detect_attack(packet)
            
            # Xử lý kết quả
            if ml_result.get('is_anomaly') or sig_result.get('is_attack'):
                self._handle_detection(packet, ml_result, sig_result)
            
        except Exception as e:
            self.logger.error(f"Error processing packet: {str(e)}")
    
    def _handle_detection(self, packet, ml_result, sig_result):
        """
        Xử lý khi phát hiện anomaly hoặc attack
        
        Args:
            packet: Gói tin
            ml_result: Kết quả ML detection
            sig_result: Kết quả signature detection
        """
        # Tạo alert
        alert_data = {
            'timestamp': time.time(),
            'packet_info': self.traffic_analyzer.extract_packet_info(packet),
            'ml_result': ml_result,
            'signature_result': sig_result,
            'severity': self._calculate_severity(ml_result, sig_result)
        }
        
        # Gửi alert
        self.alert_manager.send_alert(alert_data)
        
        # Log detection
        self.logger.warning(f"Threat detected: {alert_data}")
    
    def _calculate_severity(self, ml_result, sig_result):
        """Tính toán mức độ nghiêm trọng"""
        severity = 'LOW'
        
        if sig_result.get('is_attack'):
            severity = 'HIGH'
        elif ml_result.get('confidence', 0) > 0.8:
            severity = 'MEDIUM'
        
        return severity
    
    def _check_system_health(self):
        """Kiểm tra tình trạng hệ thống"""
        # Implement system health checks
        pass
    
    def train_models(self):
        """Huấn luyện các mô hình ML"""
        self.logger.info("Starting model training...")
        
        if not self.ml_detector:
            self.ml_detector = MLDetector(self.config.get_ml_config())
        
        success = self.ml_detector.train_models()
        
        if success:
            self.logger.info("Model training completed successfully")
        else:
            self.logger.error("Model training failed")
        
        return success
    
    def analyze_pcap(self, pcap_file):
        """
        Phân tích file PCAP offline
        
        Args:
            pcap_file (str): Đường dẫn đến file PCAP
        """
        self.logger.info(f"Analyzing PCAP file: {pcap_file}")
        
        if not self.initialize_components():
            return False
        
        try:
            # Đọc và phân tích file PCAP
            results = self.traffic_analyzer.analyze_pcap_file(pcap_file)
            
            # Xuất báo cáo
            report_path = f"reports/pcap_analysis_{int(time.time())}.json"
            self._save_analysis_report(results, report_path)
            
            self.logger.info(f"Analysis completed. Report saved to: {report_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error analyzing PCAP: {str(e)}")
            return False
    
    def _save_analysis_report(self, results, report_path):
        """Lưu báo cáo phân tích"""
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        import json
        with open(report_path, 'w') as f:
            json.dump(results, f, indent=2)
    
    def stop(self):
        """Dừng hệ thống"""
        self.logger.info("Stopping Network IDS...")
        self.running = False
        
        # Stop components
        if self.packet_capture:
            self.packet_capture.stop()
        
        if self.dashboard:
            self.dashboard.stop()
        
        # Wait for threads to finish
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        self.logger.info("Network IDS stopped successfully")

def main():
    """Hàm main"""
    parser = argparse.ArgumentParser(description="Network Intrusion Detection System")
    parser.add_argument('--start', action='store_true', help='Start monitoring')
    parser.add_argument('--train', action='store_true', help='Train ML models')
    parser.add_argument('--analyze-pcap', metavar='FILE', help='Analyze PCAP file')
    parser.add_argument('--dashboard', action='store_true', help='Start dashboard only')
    parser.add_argument('--config', default='config/config.yaml', help='Configuration file')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    # Khởi tạo hệ thống
    ids = NetworkIDS(args.config)
    
    try:
        if args.start:
            ids.start_monitoring()
        elif args.train:
            ids.train_models()
        elif args.analyze_pcap:
            ids.analyze_pcap(args.analyze_pcap)
        elif args.dashboard:
            if not ids.initialize_components():
                sys.exit(1)
            if ids.dashboard:
                ids.dashboard.run()
            else:
                print("Dashboard is not enabled in configuration")
        else:
            parser.print_help()
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
