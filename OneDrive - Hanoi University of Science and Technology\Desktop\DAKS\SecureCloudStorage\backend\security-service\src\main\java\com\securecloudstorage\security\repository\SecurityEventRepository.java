package com.securecloudstorage.security.repository;

import com.securecloudstorage.security.entity.SecurityEvent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Security Event Repository
 * Repository cho SecurityEvent entity
 */
@Repository
public interface SecurityEventRepository extends JpaRepository<SecurityEvent, Long> {

    // Find methods
    Page<SecurityEvent> findBySeverity(String severity, Pageable pageable);
    
    Page<SecurityEvent> findByEventType(String eventType, Pageable pageable);
    
    Page<SecurityEvent> findByUserId(String userId, Pageable pageable);
    
    Page<SecurityEvent> findBySeverityAndEventType(String severity, String eventType, Pageable pageable);
    
    Page<SecurityEvent> findBySeverityAndEventTypeAndUserId(String severity, String eventType, String userId, Pageable pageable);
    
    List<SecurityEvent> findByTimestampBetween(LocalDateTime start, LocalDateTime end);
    
    List<SecurityEvent> findByIpAddressAndTimestampAfter(String ipAddress, LocalDateTime timestamp);
    
    List<SecurityEvent> findTop10ByUserIdOrderByTimestampDesc(String userId);
    
    List<SecurityEvent> findTop10ByThreatLevelNotNullOrderByTimestampDesc();
    
    List<SecurityEvent> findTop20ByThreatLevelNotNullOrderByTimestampDesc();
    
    List<SecurityEvent> findTop10ByUserIdAndGeolocationNotNullOrderByTimestampDesc(String userId);

    // Count methods
    long countByUserId(String userId);
    
    long countBySeverity(String severity);
    
    long countByEventType(String eventType);
    
    long countByTimestampAfter(LocalDateTime timestamp);
    
    long countByThreatLevelNotNull();
    
    long countByUserIdAndThreatLevelNotNull(String userId);
    
    long countByUserIdAndEventType(String userId, String eventType);
    
    long countByUserIdAndEventTypeAndIsSuccessful(String userId, String eventType, Boolean isSuccessful);
    
    long countByIpAddressAndTimestampAfter(String ipAddress, LocalDateTime timestamp);
    
    long countByIpAddressAndIsSuccessfulAndTimestampAfter(String ipAddress, Boolean isSuccessful, LocalDateTime timestamp);
    
    long countByUserIdAndEventTypeAndIsSuccessfulAndTimestampAfter(String userId, String eventType, Boolean isSuccessful, LocalDateTime timestamp);
    
    long countByIpAddressAndEventTypeAndIsSuccessfulAndTimestampAfter(String ipAddress, String eventType, Boolean isSuccessful, LocalDateTime timestamp);
    
    long countByThreatLevelNotNullAndTimestampBetween(LocalDateTime start, LocalDateTime end);

    // Custom queries
    @Query("SELECT COUNT(DISTINCT e.userId) FROM SecurityEvent e WHERE e.userId IS NOT NULL")
    long countDistinctUsers();
    
    @Query("SELECT e.eventType, COUNT(e) FROM SecurityEvent e GROUP BY e.eventType")
    List<Object[]> getEventTypeStats();
    
    @Query("SELECT e.severity, COUNT(e) FROM SecurityEvent e GROUP BY e.severity")
    List<Object[]> getSeverityStats();
    
    @Query("SELECT e.source, COUNT(e) FROM SecurityEvent e GROUP BY e.source")
    List<Object[]> getSourceStats();
    
    @Query("SELECT e.userId, COUNT(e) FROM SecurityEvent e WHERE e.userId IS NOT NULL GROUP BY e.userId ORDER BY COUNT(e) DESC")
    List<Object[]> getTopUsersByEventCount();
    
    @Query("SELECT e.ipAddress, COUNT(e) FROM SecurityEvent e WHERE e.ipAddress IS NOT NULL GROUP BY e.ipAddress ORDER BY COUNT(e) DESC")
    List<Object[]> getTopIpsByEventCount();
    
    @Query("SELECT DISTINCT e.geolocation FROM SecurityEvent e WHERE e.userId = :userId AND e.geolocation IS NOT NULL")
    List<String> findDistinctGeolocationsByUserId(@Param("userId") String userId);
    
    @Query("SELECT DISTINCT e.deviceInfo FROM SecurityEvent e WHERE e.userId = :userId AND e.deviceInfo IS NOT NULL")
    List<String> findDistinctDeviceInfoByUserId(@Param("userId") String userId);
    
    @Query("SELECT COUNT(e) FROM SecurityEvent e WHERE e.userId = :userId AND e.eventType = :eventType AND e.timestamp > :timestamp")
    long countByUserIdAndEventTypeAndTimestampAfter(@Param("userId") String userId, @Param("eventType") String eventType, @Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT e FROM SecurityEvent e WHERE e.severity = 'CRITICAL' AND e.status = 'NEW' ORDER BY e.timestamp DESC")
    List<SecurityEvent> findCriticalUnprocessedEvents();
    
    @Query("SELECT e FROM SecurityEvent e WHERE e.threatLevel IS NOT NULL AND e.isProcessed = false ORDER BY e.timestamp DESC")
    List<SecurityEvent> findUnprocessedThreats();
    
    @Query("SELECT e FROM SecurityEvent e WHERE e.ipAddress = :ipAddress AND e.timestamp > :timestamp ORDER BY e.timestamp DESC")
    List<SecurityEvent> findRecentEventsByIp(@Param("ipAddress") String ipAddress, @Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT e FROM SecurityEvent e WHERE e.userId = :userId AND e.eventType = 'LOGIN' AND e.isSuccessful = false AND e.timestamp > :timestamp")
    List<SecurityEvent> findFailedLoginsByUser(@Param("userId") String userId, @Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT e FROM SecurityEvent e WHERE e.eventType = 'FILE_ACCESS' AND e.userId = :userId AND e.timestamp > :timestamp")
    List<SecurityEvent> findFileAccessByUser(@Param("userId") String userId, @Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT DATE(e.timestamp) as date, COUNT(e) as count FROM SecurityEvent e WHERE e.timestamp > :timestamp GROUP BY DATE(e.timestamp) ORDER BY date DESC")
    List<Object[]> getDailyEventCounts(@Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT e.threatLevel, COUNT(e) FROM SecurityEvent e WHERE e.threatLevel IS NOT NULL GROUP BY e.threatLevel")
    List<Object[]> getThreatLevelStats();
    
    @Query("SELECT e FROM SecurityEvent e WHERE e.correlationId = :correlationId ORDER BY e.timestamp")
    List<SecurityEvent> findByCorrelationId(@Param("correlationId") String correlationId);
    
    @Query("SELECT e FROM SecurityEvent e WHERE e.status = 'NEW' AND e.timestamp < :timestamp")
    List<SecurityEvent> findStaleEvents(@Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT e FROM SecurityEvent e WHERE e.riskScore IS NOT NULL AND CAST(e.riskScore AS integer) > :threshold ORDER BY CAST(e.riskScore AS integer) DESC")
    List<SecurityEvent> findHighRiskEvents(@Param("threshold") int threshold);
}
