@echo off
REM MariaDB Connection Test Script (Windows)
REM Tests MariaDB connection and shows database status

echo ======================================
echo MariaDB Connection Test
echo ======================================

REM Test MariaDB connection
echo Testing MariaDB connection...

REM Wait for MariaDB to be ready
echo Waiting for MariaDB to start...
set timeout=60

:check_loop
docker-compose exec mariadb mysql -u root -proot -e "SELECT 1;" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MariaDB is ready
    goto :continue
)
timeout /t 2 /nobreak >nul
set /a timeout-=2
if %timeout% gtr 0 goto :check_loop

echo ✗ MariaDB connection timeout
exit /b 1

:continue
REM Show database information
echo.
echo Database Information:
echo ====================
docker-compose exec mariadb mysql -u root -proot -e "SHOW DATABASES;"

echo.
echo Storage Database Tables:
echo =======================
docker-compose exec mariadb mysql -u root -proot storagedb -e "SHOW TABLES;"

echo.
echo Storage Database Status:
echo =======================
docker-compose exec mariadb mysql -u root -proot storagedb -e "SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH, CREATE_TIME FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'storagedb' ORDER BY TABLE_NAME;"

echo.
echo Storage Configuration:
echo ====================
docker-compose exec mariadb mysql -u root -proot storagedb -e "SELECT * FROM storage_config;"

echo.
echo System Variables:
echo ================
docker-compose exec mariadb mysql -u root -proot -e "SELECT VARIABLE_NAME, VARIABLE_VALUE FROM information_schema.GLOBAL_VARIABLES WHERE VARIABLE_NAME IN ('version', 'max_connections', 'innodb_buffer_pool_size', 'wait_timeout', 'interactive_timeout') ORDER BY VARIABLE_NAME;"

echo.
echo ======================================
echo MariaDB test completed
echo ======================================
