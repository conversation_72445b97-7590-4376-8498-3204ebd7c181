# Network IDS Dependencies

# Core networking libraries
scapy==2.4.5                  # Packet capture and analysis
dpkt==1.9.8                   # Packet parsing
# pypcap==1.2.3               # Low-level packet capture (Linux only)
# netfilterqueue==1.1.0       # Linux netfilter integration

# Machine Learning
scikit-learn==1.1.3           # ML algorithms
# tensorflow==2.10.0          # Deep learning (optional)
# keras==2.10.0               # Neural networks (optional)
# xgboost==1.7.1              # Gradient boosting (optional)
pandas==1.5.2                 # Data manipulation
numpy==1.23.5                 # Numerical computing
matplotlib==3.6.2             # Plotting
seaborn==0.12.1               # Statistical visualization

# Web Dashboard
flask==2.2.2                  # Web framework
flask-socketio==5.3.2         # Real-time communication
# dash==2.7.0                 # Interactive dashboards (optional)
# plotly==5.11.0              # Interactive plots (optional)
# gunicorn==20.1.0            # WSGI server (Linux only)

# Database
# sqlite3                     # Built-in database
sqlalchemy==1.4.44            # ORM
# redis==4.4.0                # Caching and message broker (optional)

# Configuration and Logging
pyyaml==6.0                   # YAML configuration
loguru==0.6.0                 # Advanced logging
colorlog==6.7.0               # Colored console logs

# Security and Encryption
cryptography==38.0.4          # Encryption utilities
bcrypt==4.0.1                 # Password hashing
jwt==1.3.1                    # JSON Web Tokens

# Monitoring and Alerts
psutil==5.9.4                 # System monitoring
requests==2.28.1              # HTTP requests
# smtplib                     # Email alerts (built-in)

# Threading and Async
# threading                   # Built-in threading
# asyncio                     # Asynchronous programming (built-in)
# concurrent.futures          # Parallel execution (built-in)

# Network utilities
# ipaddress                   # IP address manipulation (built-in)
# socket                      # Network sockets (built-in)
# netifaces==0.11.0           # Network interface info (Windows build issues)
# geoip2==4.6.0               # GeoIP lookups (optional)

# Data Processing
joblib==1.2.0                 # Parallel processing
tqdm==4.64.1                  # Progress bars
schedule==1.1.0               # Task scheduling

# Testing
# pytest==7.2.0              # Testing framework (optional)
# pytest-cov==4.0.0          # Coverage reporting (optional)
# mock==4.0.3                 # Mocking (optional)

# Development tools
# black==22.10.0              # Code formatting (optional)
# flake8==5.0.4               # Linting (optional)
# mypy==0.991                 # Type checking (optional)

# Platform specific
# pywin32==305; sys_platform == "win32"  # Windows API (optional)
# python-libpcap==0.2.1; sys_platform == "linux"  # Linux packet capture
