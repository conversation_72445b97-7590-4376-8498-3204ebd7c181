package com.securecloudstorage.security.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Security Stats Response DTO
 * Thống kê về security
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecurityStatsResponse {
    
    // Thống kê tổng quan
    private Long totalEvents;                      // Tổng số events
    private Long totalUsers;                       // Tổng số users
    private Long totalThreats;                     // Tổng số threats
    private Long totalBlockedIps;                  // Tổng số IPs bị block
    
    // Thống kê theo thời gian
    private Long eventsToday;                      // Events hôm nay
    private Long eventsThisWeek;                   // Events tuần này
    private Long eventsThisMonth;                  // Events tháng này
    private Long threatsToday;                     // Threats hôm nay
    private Long threatsThisWeek;                  // Threats tuần này
    private Long threatsThisMonth;                 // Threats tháng này
    
    // Thống kê theo severity
    private Long criticalEvents;                   // Events critical
    private Long highSeverityEvents;               // Events high severity
    private Long mediumSeverityEvents;             // Events medium severity
    private Long lowSeverityEvents;                // Events low severity
    
    // Thống kê theo event type
    private Map<String, Long> eventTypeStats;     // Thống kê theo loại event
    private Map<String, Long> severityStats;      // Thống kê theo severity
    private Map<String, Long> sourceStats;        // Thống kê theo source
    
    // Top threats
    private List<Map<String, Object>> topThreats; // Top threats
    private List<Map<String, Object>> topUsers;   // Top users có nhiều events
    private List<Map<String, Object>> topIps;     // Top IPs có nhiều events
    
    // Thống kê thời gian thực
    private Long activeUsers;                      // Số users đang active
    private Long activeSessions;                   // Số sessions đang active
    private Long ongoingThreats;                   // Số threats đang diễn ra
    private Long pendingAlerts;                    // Số alerts đang chờ xử lý
    
    // Thống kê hiệu suất
    private Double avgResponseTime;                // Thời gian phản hồi trung bình
    private Double threatDetectionRate;            // Tỷ lệ phát hiện threat
    private Double falsePositiveRate;              // Tỷ lệ false positive
    private Double systemUptime;                   // Uptime của hệ thống
    
    // Thống kê compliance
    private Long complianceViolations;             // Số vi phạm compliance
    private Long auditLogEntries;                  // Số audit log entries
    private Long dataBreachIncidents;              // Số incidents data breach
    private Long unauthorizedAccess;               // Số unauthorized access
    
    // Thống kê network
    private Long networkIntrusions;                // Số network intrusions
    private Long malwareDetections;                // Số malware detections
    private Long suspiciousActivities;             // Số suspicious activities
    private Long blockedConnections;               // Số connections bị block
    
    // Thống kê vulnerability
    private Long vulnerabilitiesFound;             // Số vulnerabilities tìm thấy
    private Long vulnerabilitiesPatched;           // Số vulnerabilities đã patch
    private Long pendingPatches;                   // Số patches đang chờ
    private Long riskAssessments;                  // Số risk assessments
    
    // Thống kê theo địa lý
    private Map<String, Long> geolocationStats;    // Thống kê theo vị trí
    private Map<String, Long> countryStats;        // Thống kê theo quốc gia
    private Map<String, Long> cityStats;           // Thống kê theo thành phố
    
    // Thống kê theo device
    private Map<String, Long> deviceStats;         // Thống kê theo thiết bị
    private Map<String, Long> browserStats;        // Thống kê theo browser
    private Map<String, Long> osStats;             // Thống kê theo OS
    
    // Thống kê trend
    private List<Map<String, Object>> dailyTrends;   // Trend theo ngày
    private List<Map<String, Object>> weeklyTrends;  // Trend theo tuần
    private List<Map<String, Object>> monthlyTrends; // Trend theo tháng
    
    // Thống kê alerts
    private Long totalAlerts;                      // Tổng số alerts
    private Long openAlerts;                       // Số alerts đang mở
    private Long closedAlerts;                     // Số alerts đã đóng
    private Long acknowledgedAlerts;               // Số alerts đã acknowledge
    
    // Thống kê incidents
    private Long totalIncidents;                   // Tổng số incidents
    private Long openIncidents;                    // Số incidents đang mở
    private Long resolvedIncidents;                // Số incidents đã resolve
    private Long inProgressIncidents;              // Số incidents đang xử lý
    
    // Health metrics
    private String systemHealth;                   // Tình trạng hệ thống
    private Double cpuUsage;                       // CPU usage
    private Double memoryUsage;                    // Memory usage
    private Double diskUsage;                      // Disk usage
    private Double networkLatency;                 // Network latency
}
