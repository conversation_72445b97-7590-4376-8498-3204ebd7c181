// API Response Types
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
    timestamp?: string;
}

export interface PaginatedResponse<T> {
    content: T[];
    totalElements: number;
    totalPages: number;
    size: number;
    number: number;
    first: boolean;
    last: boolean;
}

// User Types
export interface User {
    id: string;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    createdAt: string;
    updatedAt: string;
    lastLogin?: string;
    profilePicture?: string;
}

export enum UserRole {
    ADMIN = 'ADMIN',
    USER = 'USER',
    MODERATOR = 'MODERATOR'
}

export enum UserStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    SUSPENDED = 'SUSPENDED',
    PENDING = 'PENDING'
}

export interface LoginRequest {
    username: string;
    password: string;
}

export interface RegisterRequest {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
}

export interface LoginResponse {
    token: string;
    refreshToken: string;
    user: User;
    expiresIn: number;
}

export interface UpdateUserRequest {
    firstName?: string;
    lastName?: string;
    email?: string;
    profilePicture?: string;
}

export interface ChangePasswordRequest {
    currentPassword: string;
    newPassword: string;
}

// File/Storage Types
export interface FileInfo {
    id: string;
    filename: string;
    originalName: string;
    size: number;
    mimeType: string;
    uploadedAt: string;
    uploadedBy: string;
    isEncrypted: boolean;
    isQuarantined: boolean;
    scanStatus: ScanStatus;
    scanResult?: ScanResult;
    downloadCount: number;
    lastAccessed?: string;
    metadata?: FileMetadata;
}

export enum ScanStatus {
    PENDING = 'PENDING',
    SCANNING = 'SCANNING',
    CLEAN = 'CLEAN',
    INFECTED = 'INFECTED',
    ERROR = 'ERROR'
}

export interface ScanResult {
    isClean: boolean;
    threats: string[];
    scanEngine: string;
    scanTime: string;
    confidence: number;
}

export interface FileMetadata {
    tags?: string[];
    description?: string;
    category?: string;
    isPublic?: boolean;
    sharedWith?: string[];
}

export interface UploadRequest {
    file: File;
    metadata?: FileMetadata;
    encrypt?: boolean;
}

export interface UploadResponse {
    fileId: string;
    filename: string;
    size: number;
    uploadTime: string;
    scanStatus: ScanStatus;
}

export interface FileListRequest {
    page?: number;
    size?: number;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
    filter?: string;
    category?: string;
    scanStatus?: ScanStatus;
}

// Security Types
export interface SecurityEvent {
    id: string;
    type: SecurityEventType;
    severity: SecuritySeverity;
    source: string;
    target?: string;
    description: string;
    timestamp: string;
    userId?: string;
    ipAddress: string;
    userAgent?: string;
    resolved: boolean;
    resolvedAt?: string;
    resolvedBy?: string;
    metadata?: Record<string, any>;
}

export enum SecurityEventType {
    LOGIN_ATTEMPT = 'LOGIN_ATTEMPT',
    LOGIN_SUCCESS = 'LOGIN_SUCCESS',
    LOGIN_FAILURE = 'LOGIN_FAILURE',
    UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
    MALWARE_DETECTED = 'MALWARE_DETECTED',
    SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
    BRUTE_FORCE = 'BRUTE_FORCE',
    DATA_BREACH = 'DATA_BREACH',
    SYSTEM_INTRUSION = 'SYSTEM_INTRUSION'
}

export enum SecuritySeverity {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH',
    CRITICAL = 'CRITICAL'
}

export interface SecurityAlert {
    id: string;
    title: string;
    description: string;
    severity: SecuritySeverity;
    type: SecurityEventType;
    status: AlertStatus;
    createdAt: string;
    updatedAt: string;
    acknowledgedAt?: string;
    acknowledgedBy?: string;
    resolvedAt?: string;
    resolvedBy?: string;
    affectedSystems: string[];
    recommendations: string[];
}

export enum AlertStatus {
    OPEN = 'OPEN',
    ACKNOWLEDGED = 'ACKNOWLEDGED',
    RESOLVED = 'RESOLVED',
    CLOSED = 'CLOSED'
}

export interface ThreatIntelligence {
    id: string;
    type: string;
    indicator: string;
    confidence: number;
    severity: SecuritySeverity;
    description: string;
    source: string;
    firstSeen: string;
    lastSeen: string;
    isActive: boolean;
}

// Monitoring Types
export interface SystemMetrics {
    timestamp: string;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkUsage: number;
    activeConnections: number;
    requestCount: number;
    errorCount: number;
    responseTime: number;
}

export interface ServiceHealth {
    serviceName: string;
    status: HealthStatus;
    uptime: number;
    lastCheck: string;
    version: string;
    dependencies: DependencyHealth[];
    metrics: ServiceMetrics;
}

export enum HealthStatus {
    UP = 'UP',
    DOWN = 'DOWN',
    DEGRADED = 'DEGRADED',
    UNKNOWN = 'UNKNOWN'
}

export interface DependencyHealth {
    name: string;
    status: HealthStatus;
    responseTime: number;
    lastCheck: string;
}

export interface ServiceMetrics {
    requestCount: number;
    errorCount: number;
    averageResponseTime: number;
    throughput: number;
    errorRate: number;
}

export interface Alert {
    id: string;
    title: string;
    description: string;
    severity: AlertSeverity;
    type: AlertType;
    status: AlertStatus;
    source: string;
    createdAt: string;
    updatedAt: string;
    acknowledgedAt?: string;
    acknowledgedBy?: string;
    resolvedAt?: string;
    resolvedBy?: string;
    metadata?: Record<string, any>;
}

export enum AlertSeverity {
    INFO = 'INFO',
    WARNING = 'WARNING',
    ERROR = 'ERROR',
    CRITICAL = 'CRITICAL'
}

export enum AlertType {
    SYSTEM = 'SYSTEM',
    SECURITY = 'SECURITY',
    PERFORMANCE = 'PERFORMANCE',
    AVAILABILITY = 'AVAILABILITY',
    CAPACITY = 'CAPACITY'
}

export interface DashboardData {
    overview: {
        totalUsers: number;
        totalFiles: number;
        totalStorage: number;
        activeAlerts: number;
    };
    systemHealth: ServiceHealth[];
    recentAlerts: Alert[];
    metrics: SystemMetrics[];
    securityEvents: SecurityEvent[];
}

// Network IDS Types
export interface NetworkEvent {
    id: string;
    timestamp: string;
    sourceIp: string;
    destinationIp: string;
    sourcePort: number;
    destinationPort: number;
    protocol: string;
    action: string;
    severity: SecuritySeverity;
    description: string;
    ruleId?: string;
    blocked: boolean;
}

// AI Malware Detection Types
export interface MalwareAnalysis {
    id: string;
    fileId: string;
    filename: string;
    analysisType: string;
    status: AnalysisStatus;
    result: MalwareResult;
    confidence: number;
    threats: ThreatInfo[];
    analysisTime: string;
    engine: string;
}

export enum AnalysisStatus {
    PENDING = 'PENDING',
    ANALYZING = 'ANALYZING',
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED'
}

export interface MalwareResult {
    isClean: boolean;
    isMalware: boolean;
    riskScore: number;
    category?: string;
    family?: string;
}

export interface ThreatInfo {
    type: string;
    name: string;
    description: string;
    severity: SecuritySeverity;
    confidence: number;
}