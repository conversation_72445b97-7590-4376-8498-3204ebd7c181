package com.securecloudstorage.monitoring.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * ServiceHealth Entity
 * Thực thể lưu trữ thông tin health check của services
 */
@Entity
@Table(name = "service_health")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceHealth {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "health_check_id", unique = true, nullable = false)
    private String healthCheckId;

    @Column(name = "service_name", nullable = false)
    private String serviceName;

    @Column(name = "service_url", nullable = false)
    private String serviceUrl;

    @Column(name = "health_endpoint")
    private String healthEndpoint;

    @Column(name = "status", nullable = false)
    private String status; // UP, DOWN, DEGRADED, UNKNOWN

    @Column(name = "health_score")
    private Double healthScore; // 0.0 to 1.0

    @Column(name = "response_time")
    private Long responseTime; // milliseconds

    @Column(name = "timeout")
    private Long timeout; // milliseconds

    @Column(name = "check_type")
    private String checkType; // HTTP, TCP, PING, CUSTOM

    @Column(name = "check_method")
    private String checkMethod; // GET, POST, PUT, HEAD

    @Column(name = "expected_status_code")
    private Integer expectedStatusCode;

    @Column(name = "actual_status_code")
    private Integer actualStatusCode;

    @Column(name = "expected_response")
    private String expectedResponse;

    @Column(name = "actual_response", columnDefinition = "TEXT")
    private String actualResponse;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "error_details", columnDefinition = "TEXT")
    private String errorDetails;

    @Column(name = "environment")
    private String environment; // DEV, STAGING, PROD

    @Column(name = "region")
    private String region;

    @Column(name = "availability_zone")
    private String availabilityZone;

    @Column(name = "instance_id")
    private String instanceId;

    @Column(name = "host")
    private String host;

    @Column(name = "port")
    private Integer port;

    @Column(name = "version")
    private String version;

    @Column(name = "build_number")
    private String buildNumber;

    @Column(name = "deployment_id")
    private String deploymentId;

    @Column(name = "uptime")
    private Long uptime; // seconds

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "last_restart")
    private LocalDateTime lastRestart;

    @Column(name = "restart_count")
    private Integer restartCount;

    @Column(name = "cpu_usage")
    private Double cpuUsage;

    @Column(name = "memory_usage")
    private Double memoryUsage;

    @Column(name = "disk_usage")
    private Double diskUsage;

    @Column(name = "network_usage")
    private Double networkUsage;

    @Column(name = "active_connections")
    private Integer activeConnections;

    @Column(name = "max_connections")
    private Integer maxConnections;

    @Column(name = "thread_count")
    private Integer threadCount;

    @Column(name = "max_threads")
    private Integer maxThreads;

    @Column(name = "heap_usage")
    private Double heapUsage;

    @Column(name = "non_heap_usage")
    private Double nonHeapUsage;

    @Column(name = "gc_count")
    private Long gcCount;

    @Column(name = "gc_time")
    private Long gcTime;

    @Column(name = "load_average")
    private Double loadAverage;

    @Column(name = "request_count")
    private Long requestCount;

    @Column(name = "error_count")
    private Long errorCount;

    @Column(name = "error_rate")
    private Double errorRate;

    @Column(name = "throughput")
    private Double throughput;

    @Column(name = "latency_p50")
    private Double latencyP50;

    @Column(name = "latency_p90")
    private Double latencyP90;

    @Column(name = "latency_p95")
    private Double latencyP95;

    @Column(name = "latency_p99")
    private Double latencyP99;

    @Column(name = "database_connections")
    private Integer databaseConnections;

    @Column(name = "database_connection_pool_size")
    private Integer databaseConnectionPoolSize;

    @Column(name = "cache_hit_rate")
    private Double cacheHitRate;

    @Column(name = "cache_miss_rate")
    private Double cacheMissRate;

    @Column(name = "queue_size")
    private Integer queueSize;

    @Column(name = "queue_capacity")
    private Integer queueCapacity;

    @Column(name = "dependencies", columnDefinition = "TEXT")
    private String dependencies;

    @Column(name = "dependency_status", columnDefinition = "TEXT")
    private String dependencyStatus;

    @Column(name = "circuit_breaker_state")
    private String circuitBreakerState; // CLOSED, OPEN, HALF_OPEN

    @Column(name = "circuit_breaker_failure_rate")
    private Double circuitBreakerFailureRate;

    @Column(name = "circuit_breaker_slow_call_rate")
    private Double circuitBreakerSlowCallRate;

    @Column(name = "check_interval")
    private Integer checkInterval; // seconds

    @Column(name = "check_count")
    private Long checkCount;

    @Column(name = "success_count")
    private Long successCount;

    @Column(name = "failure_count")
    private Long failureCount;

    @Column(name = "consecutive_failures")
    private Integer consecutiveFailures;

    @Column(name = "max_consecutive_failures")
    private Integer maxConsecutiveFailures;

    @Column(name = "uptime_percentage")
    private Double uptimePercentage;

    @Column(name = "availability_sla")
    private Double availabilitySla;

    @Column(name = "sla_compliance")
    private Boolean slaCompliance;

    @Column(name = "maintenance_mode")
    private Boolean maintenanceMode;

    @Column(name = "maintenance_window_start")
    private LocalDateTime maintenanceWindowStart;

    @Column(name = "maintenance_window_end")
    private LocalDateTime maintenanceWindowEnd;

    @Column(name = "alert_enabled")
    private Boolean alertEnabled;

    @Column(name = "alert_threshold")
    private Integer alertThreshold;

    @Column(name = "alert_count")
    private Integer alertCount;

    @Column(name = "last_alert_at")
    private LocalDateTime lastAlertAt;

    @Column(name = "auto_healing_enabled")
    private Boolean autoHealingEnabled;

    @Column(name = "auto_healing_attempts")
    private Integer autoHealingAttempts;

    @Column(name = "max_auto_healing_attempts")
    private Integer maxAutoHealingAttempts;

    @Column(name = "last_auto_healing_at")
    private LocalDateTime lastAutoHealingAt;

    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags;

    @Column(name = "labels", columnDefinition = "TEXT")
    private String labels;

    @Column(name = "custom_metrics", columnDefinition = "TEXT")
    private String customMetrics;

    @Column(name = "business_impact")
    private String businessImpact; // LOW, MEDIUM, HIGH, CRITICAL

    @Column(name = "priority")
    private String priority; // P1, P2, P3, P4

    @Column(name = "escalation_policy")
    private String escalationPolicy;

    @Column(name = "runbook_url")
    private String runbookUrl;

    @Column(name = "dashboard_url")
    private String dashboardUrl;

    @Column(name = "active")
    private Boolean active;

    @Column(name = "archived")
    private Boolean archived;

    @Column(name = "archived_at")
    private LocalDateTime archivedAt;

    @Column(name = "archived_by")
    private String archivedBy;

    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata;

    @Column(name = "external_id")
    private String externalId;

    @Column(name = "external_system")
    private String externalSystem;

    @Column(name = "integration_data", columnDefinition = "TEXT")
    private String integrationData;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "last_checked_at")
    private LocalDateTime lastCheckedAt;

    @Column(name = "last_success_at")
    private LocalDateTime lastSuccessAt;

    @Column(name = "last_failure_at")
    private LocalDateTime lastFailureAt;

    @Column(name = "status_changed_at")
    private LocalDateTime statusChangedAt;

    @Column(name = "previous_status")
    private String previousStatus;

    // Helper methods

    @JsonIgnore
    public boolean isHealthy() {
        return "UP".equals(status);
    }

    @JsonIgnore
    public boolean isUnhealthy() {
        return "DOWN".equals(status);
    }

    @JsonIgnore
    public boolean isDegraded() {
        return "DEGRADED".equals(status);
    }

    @JsonIgnore
    public boolean isUnknown() {
        return "UNKNOWN".equals(status);
    }

    @JsonIgnore
    public boolean isActive() {
        return Boolean.TRUE.equals(active);
    }

    @JsonIgnore
    public boolean isArchived() {
        return Boolean.TRUE.equals(archived);
    }

    @JsonIgnore
    public boolean isMaintenanceMode() {
        return Boolean.TRUE.equals(maintenanceMode);
    }

    @JsonIgnore
    public boolean isAlertEnabled() {
        return Boolean.TRUE.equals(alertEnabled);
    }

    @JsonIgnore
    public boolean isAutoHealingEnabled() {
        return Boolean.TRUE.equals(autoHealingEnabled);
    }

    @JsonIgnore
    public boolean isSlaCompliant() {
        return Boolean.TRUE.equals(slaCompliance);
    }

    @JsonIgnore
    public boolean isHighPriority() {
        return "P1".equals(priority) || "P2".equals(priority);
    }

    @JsonIgnore
    public boolean isCriticalImpact() {
        return "CRITICAL".equals(businessImpact);
    }

    @JsonIgnore
    public boolean needsAutoHealing() {
        return isAutoHealingEnabled() && isUnhealthy() && 
               (autoHealingAttempts == null || autoHealingAttempts < maxAutoHealingAttempts);
    }

    @JsonIgnore
    public boolean isStale() {
        if (lastCheckedAt == null) return true;
        int stalenessThreshold = checkInterval != null ? checkInterval * 3 : 300; // 5 minutes default
        return LocalDateTime.now().isAfter(lastCheckedAt.plusSeconds(stalenessThreshold));
    }

    @JsonProperty("uptimeInMinutes")
    public long getUptimeInMinutes() {
        if (uptime != null) {
            return uptime / 60;
        }
        return 0;
    }

    @JsonProperty("downtimeInMinutes")
    public long getDowntimeInMinutes() {
        if (lastFailureAt != null && lastSuccessAt != null) {
            return java.time.Duration.between(lastFailureAt, lastSuccessAt).toMinutes();
        }
        return 0;
    }

    @JsonProperty("healthGrade")
    public String getHealthGrade() {
        if (healthScore == null) return "UNKNOWN";
        if (healthScore >= 0.95) return "EXCELLENT";
        if (healthScore >= 0.9) return "GOOD";
        if (healthScore >= 0.8) return "FAIR";
        if (healthScore >= 0.6) return "POOR";
        return "CRITICAL";
    }

    @JsonProperty("availabilityGrade")
    public String getAvailabilityGrade() {
        if (uptimePercentage == null) return "UNKNOWN";
        if (uptimePercentage >= 99.9) return "EXCELLENT";
        if (uptimePercentage >= 99.5) return "GOOD";
        if (uptimePercentage >= 99.0) return "FAIR";
        if (uptimePercentage >= 95.0) return "POOR";
        return "CRITICAL";
    }

    @JsonProperty("performanceGrade")
    public String getPerformanceGrade() {
        if (responseTime == null) return "UNKNOWN";
        if (responseTime <= 100) return "EXCELLENT";
        if (responseTime <= 300) return "GOOD";
        if (responseTime <= 1000) return "FAIR";
        if (responseTime <= 3000) return "POOR";
        return "CRITICAL";
    }

    @JsonProperty("successRate")
    public double getSuccessRate() {
        if (checkCount == null || checkCount == 0) return 0.0;
        if (successCount == null) return 0.0;
        return (double) successCount / checkCount * 100;
    }

    @JsonProperty("failureRate")
    public double getFailureRate() {
        if (checkCount == null || checkCount == 0) return 0.0;
        if (failureCount == null) return 0.0;
        return (double) failureCount / checkCount * 100;
    }

    @JsonProperty("timeSinceLastCheck")
    public long getTimeSinceLastCheckInMinutes() {
        if (lastCheckedAt != null) {
            return java.time.Duration.between(lastCheckedAt, LocalDateTime.now()).toMinutes();
        }
        return 0;
    }

    @JsonProperty("timeSinceLastSuccess")
    public long getTimeSinceLastSuccessInMinutes() {
        if (lastSuccessAt != null) {
            return java.time.Duration.between(lastSuccessAt, LocalDateTime.now()).toMinutes();
        }
        return 0;
    }

    @JsonProperty("timeSinceLastFailure")
    public long getTimeSinceLastFailureInMinutes() {
        if (lastFailureAt != null) {
            return java.time.Duration.between(lastFailureAt, LocalDateTime.now()).toMinutes();
        }
        return 0;
    }

    @PrePersist
    public void prePersist() {
        if (healthCheckId == null) {
            healthCheckId = "HEALTH-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        }
        if (active == null) {
            active = true;
        }
        if (archived == null) {
            archived = false;
        }
        if (alertEnabled == null) {
            alertEnabled = true;
        }
        if (autoHealingEnabled == null) {
            autoHealingEnabled = false;
        }
        if (maintenanceMode == null) {
            maintenanceMode = false;
        }
        if (slaCompliance == null) {
            slaCompliance = true;
        }
        if (checkCount == null) {
            checkCount = 0L;
        }
        if (successCount == null) {
            successCount = 0L;
        }
        if (failureCount == null) {
            failureCount = 0L;
        }
        if (consecutiveFailures == null) {
            consecutiveFailures = 0;
        }
        if (alertCount == null) {
            alertCount = 0;
        }
        if (autoHealingAttempts == null) {
            autoHealingAttempts = 0;
        }
        if (restartCount == null) {
            restartCount = 0;
        }
        if (environment == null) {
            environment = "PROD";
        }
        if (businessImpact == null) {
            businessImpact = "MEDIUM";
        }
        if (priority == null) {
            priority = "P3";
        }
        if (checkInterval == null) {
            checkInterval = 60; // 1 minute default
        }
        if (timeout == null) {
            timeout = 30000L; // 30 seconds default
        }
        if (expectedStatusCode == null) {
            expectedStatusCode = 200;
        }
        if (availabilitySla == null) {
            availabilitySla = 99.9;
        }
        if (alertThreshold == null) {
            alertThreshold = 3;
        }
        if (maxConsecutiveFailures == null) {
            maxConsecutiveFailures = 5;
        }
        if (maxAutoHealingAttempts == null) {
            maxAutoHealingAttempts = 3;
        }
        if (checkType == null) {
            checkType = "HTTP";
        }
        if (checkMethod == null) {
            checkMethod = "GET";
        }
        if (healthEndpoint == null) {
            healthEndpoint = "/health";
        }
        if (lastCheckedAt == null) {
            lastCheckedAt = LocalDateTime.now();
        }
    }

    @PreUpdate
    public void preUpdate() {
        if (archived != null && archived && archivedAt == null) {
            archivedAt = LocalDateTime.now();
        }
        
        // Update status change tracking
        if (status != null && !status.equals(previousStatus)) {
            previousStatus = status;
            statusChangedAt = LocalDateTime.now();
        }
        
        // Update uptime percentage
        if (checkCount != null && checkCount > 0 && successCount != null) {
            uptimePercentage = (double) successCount / checkCount * 100;
        }
        
        // Update SLA compliance
        if (uptimePercentage != null && availabilitySla != null) {
            slaCompliance = uptimePercentage >= availabilitySla;
        }
        
        // Update health score based on various factors
        calculateHealthScore();
    }

    private void calculateHealthScore() {
        double score = 1.0;
        
        // Status weight (50%)
        if ("UP".equals(status)) {
            score *= 1.0;
        } else if ("DEGRADED".equals(status)) {
            score *= 0.7;
        } else if ("DOWN".equals(status)) {
            score *= 0.0;
        } else {
            score *= 0.5; // UNKNOWN
        }
        
        // Response time weight (20%)
        if (responseTime != null) {
            if (responseTime <= 100) {
                score *= 1.0;
            } else if (responseTime <= 300) {
                score *= 0.9;
            } else if (responseTime <= 1000) {
                score *= 0.7;
            } else if (responseTime <= 3000) {
                score *= 0.5;
            } else {
                score *= 0.3;
            }
        }
        
        // Error rate weight (20%)
        if (errorRate != null) {
            if (errorRate <= 0.1) {
                score *= 1.0;
            } else if (errorRate <= 1.0) {
                score *= 0.9;
            } else if (errorRate <= 5.0) {
                score *= 0.7;
            } else if (errorRate <= 10.0) {
                score *= 0.5;
            } else {
                score *= 0.3;
            }
        }
        
        // Consecutive failures weight (10%)
        if (consecutiveFailures != null && maxConsecutiveFailures != null) {
            if (consecutiveFailures == 0) {
                score *= 1.0;
            } else {
                double failureRatio = (double) consecutiveFailures / maxConsecutiveFailures;
                score *= Math.max(0.1, 1.0 - failureRatio);
            }
        }
        
        healthScore = Math.max(0.0, Math.min(1.0, score));
    }
}
