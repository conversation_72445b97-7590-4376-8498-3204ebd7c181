import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ApiResponse } from '../types';

// API Configuration
const API_CONFIG = {
    BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:8080',
    TIMEOUT: 30000,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000,
};

// Token management
class TokenManager {
    private static readonly TOKEN_KEY = 'auth_token';
    private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

    static getToken(): string | null {
        return localStorage.getItem(this.TOKEN_KEY);
    }

    static setToken(token: string): void {
        localStorage.setItem(this.TOKEN_KEY, token);
    }

    static getRefreshToken(): string | null {
        return localStorage.getItem(this.REFRESH_TOKEN_KEY);
    }

    static setRefreshToken(refreshToken: string): void {
        localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    }

    static clearTokens(): void {
        localStorage.removeItem(this.TOKEN_KEY);
        localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    }

    static isTokenExpired(token: string): boolean {
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;
            return payload.exp < currentTime;
        } catch {
            return true;
        }
    }
}

// API Service Class
class ApiService {
    private axiosInstance: AxiosInstance;
    private isRefreshing = false;
    private failedQueue: Array<{
        resolve: (value?: any) => void;
        reject: (reason?: any) => void;
    }> = [];

    constructor() {
        this.axiosInstance = axios.create({
            baseURL: API_CONFIG.BASE_URL,
            timeout: API_CONFIG.TIMEOUT,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        });

        this.setupInterceptors();
    }

    private setupInterceptors(): void {
        // Request interceptor
        this.axiosInstance.interceptors.request.use(
            (config) => {
                const token = TokenManager.getToken();
                if (token && !TokenManager.isTokenExpired(token)) {
                    config.headers.Authorization = `Bearer ${token}`;
                }

                // Add request ID for tracking
                config.headers['X-Request-ID'] = this.generateRequestId();

                console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
                    headers: config.headers,
                    data: config.data,
                });

                return config;
            },
            (error) => {
                console.error('[API Request Error]', error);
                return Promise.reject(error);
            }
        );

        // Response interceptor
        this.axiosInstance.interceptors.response.use(
            (response) => {
                console.log(`[API Response] ${response.status} ${response.config.url}`, {
                    data: response.data,
                });
                return response;
            },
            async (error: AxiosError) => {
                const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

                console.error(`[API Response Error] ${error.response?.status} ${error.config?.url}`, {
                    error: error.response?.data,
                    status: error.response?.status,
                });

                // Handle 401 Unauthorized
                if (error.response?.status === 401 && !originalRequest._retry) {
                    if (this.isRefreshing) {
                        return new Promise((resolve, reject) => {
                            this.failedQueue.push({ resolve, reject });
                        }).then(() => {
                            return this.axiosInstance(originalRequest);
                        }).catch(err => {
                            return Promise.reject(err);
                        });
                    }

                    originalRequest._retry = true;
                    this.isRefreshing = true;

                    try {
                        const refreshToken = TokenManager.getRefreshToken();
                        if (refreshToken) {
                            const response = await this.refreshAccessToken(refreshToken);
                            TokenManager.setToken(response.data.token);
                            TokenManager.setRefreshToken(response.data.refreshToken);

                            this.processQueue(null);
                            return this.axiosInstance(originalRequest);
                        }
                    } catch (refreshError) {
                        this.processQueue(refreshError);
                        this.handleAuthenticationError();
                        return Promise.reject(refreshError);
                    } finally {
                        this.isRefreshing = false;
                    }
                }

                // Handle other errors
                return Promise.reject(this.handleError(error));
            }
        );
    }

    private processQueue(error: any): void {
        this.failedQueue.forEach(({ resolve, reject }) => {
            if (error) {
                reject(error);
            } else {
                resolve();
            }
        });

        this.failedQueue = [];
    }

    private async refreshAccessToken(refreshToken: string): Promise<AxiosResponse> {
        return axios.post(`${API_CONFIG.BASE_URL}/api/users/refresh`, {
            refreshToken,
        });
    }

    private handleAuthenticationError(): void {
        TokenManager.clearTokens();
        // Redirect to login page
        window.location.href = '/login';
    }

    private handleError(error: AxiosError): Error {
        const response = error.response;
        const request = error.request;

        if (response) {
            // Server responded with error status
            const errorMessage = (response.data as any)?.message ||
                (response.data as any)?.error ||
                `HTTP ${response.status}: ${response.statusText}`;

            return new Error(errorMessage);
        } else if (request) {
            // Request was made but no response received
            return new Error('Network error: No response from server');
        } else {
            // Something else happened
            return new Error(error.message || 'Unknown error occurred');
        }
    }

    private generateRequestId(): string {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Retry mechanism
    private async retryRequest<T>(
        requestFn: () => Promise<T>,
        attempts: number = API_CONFIG.RETRY_ATTEMPTS
    ): Promise<T> {
        try {
            return await requestFn();
        } catch (error) {
            if (attempts > 1 && this.isRetryableError(error as AxiosError)) {
                await this.delay(API_CONFIG.RETRY_DELAY);
                return this.retryRequest(requestFn, attempts - 1);
            }
            throw error;
        }
    }

    private isRetryableError(error: AxiosError): boolean {
        return !error.response ||
            error.response.status >= 500 ||
            error.response.status === 429;
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Public API methods
    async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        return this.retryRequest(async () => {
            const response = await this.axiosInstance.get<ApiResponse<T>>(url, config);
            return response.data;
        });
    }

    async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        return this.retryRequest(async () => {
            const response = await this.axiosInstance.post<ApiResponse<T>>(url, data, config);
            return response.data;
        });
    }

    async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        return this.retryRequest(async () => {
            const response = await this.axiosInstance.put<ApiResponse<T>>(url, data, config);
            return response.data;
        });
    }

    async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        return this.retryRequest(async () => {
            const response = await this.axiosInstance.patch<ApiResponse<T>>(url, data, config);
            return response.data;
        });
    }

    async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        return this.retryRequest(async () => {
            const response = await this.axiosInstance.delete<ApiResponse<T>>(url, config);
            return response.data;
        });
    }

    // File upload with progress
    async uploadFile<T = any>(
        url: string,
        file: File,
        onProgress?: (progressEvent: ProgressEvent) => void,
        additionalData?: Record<string, any>
    ): Promise<ApiResponse<T>> {
        const formData = new FormData();
        formData.append('file', file);

        if (additionalData) {
            Object.keys(additionalData).forEach(key => {
                formData.append(key, additionalData[key]);
            });
        }

        return this.retryRequest(async () => {
            const response = await this.axiosInstance.post<ApiResponse<T>>(url, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                onUploadProgress: onProgress,
            });
            return response.data;
        });
    }

    // Download file
    async downloadFile(url: string, filename?: string): Promise<void> {
        const response = await this.axiosInstance.get(url, {
            responseType: 'blob',
        });

        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename || 'download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
    }

    // Get axios instance for custom requests
    getAxiosInstance(): AxiosInstance {
        return this.axiosInstance;
    }

    // Update base URL
    setBaseURL(baseURL: string): void {
        this.axiosInstance.defaults.baseURL = baseURL;
    }

    // Set authentication token
    setAuthToken(token: string): void {
        TokenManager.setToken(token);
    }

    // Clear authentication
    clearAuth(): void {
        TokenManager.clearTokens();
    }

    // Check if authenticated
    isAuthenticated(): boolean {
        const token = TokenManager.getToken();
        return token !== null && !TokenManager.isTokenExpired(token);
    }
}

// Export singleton instance
export const apiService = new ApiService();
export { TokenManager };
export default apiService;