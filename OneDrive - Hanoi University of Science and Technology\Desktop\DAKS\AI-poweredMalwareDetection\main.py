#!/usr/bin/env python3
"""
AI-Powered Malware Detection System
Main entry point for the malware detection system
"""

import argparse
import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.analysis.static_analyzer import StaticAnalyzer
from src.analysis.dynamic_analyzer import DynamicAnalyzer
from src.models.ensemble_model import EnsembleDetector
# from src.monitoring.realtime_monitor import RealtimeMonitor  # Temporarily disabled
from src.utils.logger import setup_logging
from src.utils.config import load_config

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='AI-Powered Malware Detection System')
    parser.add_argument('--file', '-f', help='File to analyze')
    parser.add_argument('--directory', '-d', help='Directory to scan')
    parser.add_argument('--monitor', '-m', action='store_true', help='Start real-time monitoring')
    parser.add_argument('--train', '-t', action='store_true', help='Train new models')
    parser.add_argument('--config', '-c', default='config/config.yaml', help='Configuration file')
    parser.add_argument('--output', '-o', help='Output file for results')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(verbose=args.verbose)
    logger = logging.getLogger(__name__)
    
    # Load configuration
    config = load_config(args.config)
    
    try:
        # Initialize detector
        detector = EnsembleDetector(config)
        
        if args.train:
            logger.info("Starting model training...")
            detector.train()
            logger.info("Model training completed")
            
        elif args.file:
            logger.info(f"Analyzing file: {args.file}")
            result = analyze_file(args.file, detector, config)
            print_results(result, args.output)
            
        elif args.directory:
            logger.info(f"Scanning directory: {args.directory}")
            results = scan_directory(args.directory, detector, config)
            print_results(results, args.output)
            
        elif args.monitor:
            logger.info("Real-time monitoring temporarily disabled")
            logger.info("Use --train to train models or --file to analyze files")
            # monitor = RealtimeMonitor(detector, config)
            # monitor.start()
            
        else:
            parser.print_help()
            
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        sys.exit(1)

def analyze_file(file_path, detector, config):
    """Analyze a single file"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    # Static analysis
    static_analyzer = StaticAnalyzer(config)
    static_features = static_analyzer.analyze(file_path)
    
    # Dynamic analysis (if enabled)
    dynamic_features = None
    if config.get('dynamic_analysis', {}).get('enabled', False):
        dynamic_analyzer = DynamicAnalyzer(config)
        dynamic_features = dynamic_analyzer.analyze(file_path)
    
    # Prediction
    result = detector.predict(file_path, static_features, dynamic_features)
    
    return {
        'file': file_path,
        'prediction': result['prediction'],
        'confidence': result['confidence'],
        'features': result.get('features', {}),
        'threats': result.get('threats', [])
    }

def scan_directory(directory, detector, config):
    """Scan a directory for malware"""
    results = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                result = analyze_file(file_path, detector, config)
                results.append(result)
            except Exception as e:
                logging.warning(f"Error analyzing {file_path}: {str(e)}")
                
    return results

def print_results(results, output_file=None):
    """Print analysis results"""
    if isinstance(results, dict):
        results = [results]
    
    output_lines = []
    
    for result in results:
        output_lines.append(f"File: {result['file']}")
        output_lines.append(f"Prediction: {result['prediction']}")
        output_lines.append(f"Confidence: {result['confidence']:.2f}")
        
        if result.get('threats'):
            output_lines.append("Detected Threats:")
            for threat in result['threats']:
                output_lines.append(f"  - {threat}")
        
        output_lines.append("-" * 50)
    
    output_text = '\n'.join(output_lines)
    
    if output_file:
        with open(output_file, 'w') as f:
            f.write(output_text)
        print(f"Results saved to: {output_file}")
    else:
        print(output_text)

if __name__ == "__main__":
    main()
