package com.securecloudstorage.monitoring.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Alert Entity
 * Thực thể lưu trữ thông tin alerts
 */
@Entity
@Table(name = "alerts")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Alert {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "alert_id", unique = true, nullable = false)
    private String alertId;

    @Column(name = "title", nullable = false)
    private String title;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "severity", nullable = false)
    private String severity; // LOW, MEDIUM, HIGH, CRITICAL

    @Column(name = "service", nullable = false)
    private String service;

    @Column(name = "type", nullable = false)
    private String type; // SYSTEM, SECURITY, PERFORMANCE, AVAILABILITY

    @Column(name = "status", nullable = false)
    private String status; // ACTIVE, ACKNOWLEDGED, RESOLVED, SUPPRESSED

    @Column(name = "priority")
    private String priority; // P1, P2, P3, P4

    @Column(name = "category")
    private String category;

    @Column(name = "source")
    private String source;

    @Column(name = "host")
    private String host;

    @Column(name = "environment")
    private String environment; // DEV, STAGING, PROD

    @Column(name = "metric_name")
    private String metricName;

    @Column(name = "metric_value")
    private Double metricValue;

    @Column(name = "threshold")
    private Double threshold;

    @Column(name = "condition")
    private String condition; // GREATER_THAN, LESS_THAN, EQUALS

    @Column(name = "assigned_to")
    private String assignedTo;

    @Column(name = "assigned_by")
    private String assignedBy;

    @Column(name = "resolved_by")
    private String resolvedBy;

    @Column(name = "resolution_notes", columnDefinition = "TEXT")
    private String resolutionNotes;

    @Column(name = "escalation_level")
    private String escalationLevel; // L1, L2, L3, MANAGEMENT

    @Column(name = "escalated_by")
    private String escalatedBy;

    @Column(name = "escalated_at")
    private LocalDateTime escalatedAt;

    @Column(name = "auto_resolved")
    private Boolean autoResolved;

    @Column(name = "auto_resolution_reason")
    private String autoResolutionReason;

    @Column(name = "suppressed_by")
    private String suppressedBy;

    @Column(name = "suppressed_until")
    private LocalDateTime suppressedUntil;

    @Column(name = "suppression_reason")
    private String suppressionReason;

    @Column(name = "notification_sent")
    private Boolean notificationSent;

    @Column(name = "last_notification_sent")
    private LocalDateTime lastNotificationSent;

    @Column(name = "notification_count")
    private Integer notificationCount;

    @Column(name = "sla_deadline")
    private LocalDateTime slaDeadline;

    @Column(name = "sla_breached")
    private Boolean slaBreached;

    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags;

    @Column(name = "labels", columnDefinition = "TEXT")
    private String labels;

    @Column(name = "runbook_url")
    private String runbookUrl;

    @Column(name = "dashboard_url")
    private String dashboardUrl;

    @Column(name = "correlation_id")
    private String correlationId;

    @Column(name = "parent_alert_id")
    private String parentAlertId;

    @Column(name = "child_alert_count")
    private Integer childAlertCount;

    @Column(name = "acknowledgment_required")
    private Boolean acknowledgmentRequired;

    @Column(name = "acknowledged_by")
    private String acknowledgedBy;

    @Column(name = "acknowledged_at")
    private LocalDateTime acknowledgedAt;

    @Column(name = "acknowledgment_notes", columnDefinition = "TEXT")
    private String acknowledgmentNotes;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "resolved_at")
    private LocalDateTime resolvedAt;

    @Column(name = "duration_seconds")
    private Long durationSeconds;

    @Column(name = "first_occurrence")
    private LocalDateTime firstOccurrence;

    @Column(name = "last_occurrence")
    private LocalDateTime lastOccurrence;

    @Column(name = "occurrence_count")
    private Integer occurrenceCount;

    @Column(name = "active")
    private Boolean active;

    @Column(name = "archived")
    private Boolean archived;

    @Column(name = "archived_at")
    private LocalDateTime archivedAt;

    @Column(name = "archived_by")
    private String archivedBy;

    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata;

    @Column(name = "external_id")
    private String externalId;

    @Column(name = "external_system")
    private String externalSystem;

    @Column(name = "integration_data", columnDefinition = "TEXT")
    private String integrationData;

    // Helper methods

    @JsonIgnore
    public boolean isActive() {
        return Boolean.TRUE.equals(active) && "ACTIVE".equals(status);
    }

    @JsonIgnore
    public boolean isResolved() {
        return "RESOLVED".equals(status);
    }

    @JsonIgnore
    public boolean isCritical() {
        return "CRITICAL".equals(severity);
    }

    @JsonIgnore
    public boolean isAcknowledged() {
        return "ACKNOWLEDGED".equals(status);
    }

    @JsonIgnore
    public boolean isSuppressed() {
        return "SUPPRESSED".equals(status);
    }

    @JsonIgnore
    public boolean isSlaBreached() {
        return Boolean.TRUE.equals(slaBreached);
    }

    @JsonIgnore
    public boolean requiresEscalation() {
        return isCritical() && !isAcknowledged() && 
               slaDeadline != null && LocalDateTime.now().isAfter(slaDeadline);
    }

    @JsonIgnore
    public boolean isAutoResolved() {
        return Boolean.TRUE.equals(autoResolved);
    }

    @JsonIgnore
    public boolean hasParent() {
        return parentAlertId != null && !parentAlertId.isEmpty();
    }

    @JsonIgnore
    public boolean hasChildren() {
        return childAlertCount != null && childAlertCount > 0;
    }

    @JsonProperty("ageInMinutes")
    public long getAgeInMinutes() {
        if (createdAt != null) {
            return java.time.Duration.between(createdAt, LocalDateTime.now()).toMinutes();
        }
        return 0;
    }

    @JsonProperty("timeToResolution")
    public long getTimeToResolutionInMinutes() {
        if (createdAt != null && resolvedAt != null) {
            return java.time.Duration.between(createdAt, resolvedAt).toMinutes();
        }
        return 0;
    }

    @JsonProperty("timeToAcknowledgment")
    public long getTimeToAcknowledgmentInMinutes() {
        if (createdAt != null && acknowledgedAt != null) {
            return java.time.Duration.between(createdAt, acknowledgedAt).toMinutes();
        }
        return 0;
    }

    @PrePersist
    public void prePersist() {
        if (alertId == null) {
            alertId = "ALERT-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        }
        if (active == null) {
            active = true;
        }
        if (archived == null) {
            archived = false;
        }
        if (notificationSent == null) {
            notificationSent = false;
        }
        if (notificationCount == null) {
            notificationCount = 0;
        }
        if (slaBreached == null) {
            slaBreached = false;
        }
        if (autoResolved == null) {
            autoResolved = false;
        }
        if (acknowledgmentRequired == null) {
            acknowledgmentRequired = true;
        }
        if (occurrenceCount == null) {
            occurrenceCount = 1;
        }
        if (childAlertCount == null) {
            childAlertCount = 0;
        }
        if (firstOccurrence == null) {
            firstOccurrence = LocalDateTime.now();
        }
        if (lastOccurrence == null) {
            lastOccurrence = LocalDateTime.now();
        }
        if (environment == null) {
            environment = "PROD";
        }
        if (priority == null) {
            priority = mapSeverityToPriority(severity);
        }
    }

    @PreUpdate
    public void preUpdate() {
        if ("RESOLVED".equals(status) && resolvedAt == null) {
            resolvedAt = LocalDateTime.now();
            if (createdAt != null) {
                durationSeconds = java.time.Duration.between(createdAt, resolvedAt).getSeconds();
            }
        }
        if ("ACKNOWLEDGED".equals(status) && acknowledgedAt == null) {
            acknowledgedAt = LocalDateTime.now();
        }
        if (archived != null && archived && archivedAt == null) {
            archivedAt = LocalDateTime.now();
        }
    }

    private String mapSeverityToPriority(String severity) {
        if (severity == null) return "P3";
        switch (severity.toUpperCase()) {
            case "CRITICAL": return "P1";
            case "HIGH": return "P2";
            case "MEDIUM": return "P3";
            case "LOW": return "P4";
            default: return "P3";
        }
    }
}
