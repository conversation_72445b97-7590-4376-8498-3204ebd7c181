package com.securecloudstorage.storage.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * File Entity for MariaDB
 */
@Entity
@Table(name = "file_entity", indexes = {
    @Index(name = "idx_file_user_id", columnList = "user_id"),
    @Index(name = "idx_file_upload_date", columnList = "upload_date"),
    @Index(name = "idx_file_quarantined", columnList = "quarantined"),
    @Index(name = "idx_file_id", columnList = "file_id", unique = true)
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "file_id", nullable = false, unique = true, length = 255)
    private String fileId;
    
    @Column(name = "file_name", nullable = false, length = 500)
    private String fileName;
    
    @Column(name = "file_path", nullable = false, length = 1000)
    private String filePath;
    
    @Column(name = "file_size", nullable = false)
    private Long fileSize;
    
    @Column(name = "content_type", length = 255)
    private String contentType;
    
    @Column(name = "user_id", nullable = false, length = 255)
    private String userId;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @CreationTimestamp
    @Column(name = "upload_date", nullable = false)
    private LocalDateTime uploadDate;
    
    @UpdateTimestamp
    @Column(name = "updated_date")
    private LocalDateTime updatedDate;
    
    @Column(name = "encrypted", nullable = false)
    private boolean encrypted = false;
    
    @Column(name = "quarantined", nullable = false)
    private boolean quarantined = false;
    
    @Column(name = "checksum", length = 64)
    private String checksum;
    
    @Column(name = "mime_type", length = 255)
    private String mimeType;
    
    @Column(name = "scan_status", length = 50)
    @Enumerated(EnumType.STRING)
    private ScanStatus scanStatus = ScanStatus.PENDING;
    
    @Column(name = "access_count", nullable = false)
    private Long accessCount = 0L;
    
    @Column(name = "last_accessed")
    private LocalDateTime lastAccessed;
    
    @Column(name = "tags", length = 500)
    private String tags;
    
    @Column(name = "is_public", nullable = false)
    private boolean isPublic = false;
    
    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;
    
    public enum ScanStatus {
        PENDING,
        SCANNING,
        SAFE,
        THREAT,
        ERROR
    }
}
