package com.securecloudstorage.storage.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * File Upload Response DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileUploadResponse {
    
    private boolean success;
    private String fileId;
    private String fileName;
    private Long fileSize;
    private String message;
    private boolean scanned;
    private boolean safe;
    private String threatType;
    private Double confidence;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadDate;
    
    private String downloadUrl;
    private String shareUrl;
    private boolean encrypted;
    private String checksum;
    
    // Error information
    private String errorCode;
    private String errorMessage;
    
    // Additional metadata
    private String contentType;
    private String mimeType;
    private String tags;
    
    public static FileUploadResponse success(String fileId, String fileName, Long fileSize) {
        return FileUploadResponse.builder()
                .success(true)
                .fileId(fileId)
                .fileName(fileName)
                .fileSize(fileSize)
                .message("File uploaded successfully")
                .uploadDate(LocalDateTime.now())
                .build();
    }
    
    public static FileUploadResponse error(String errorCode, String errorMessage) {
        return FileUploadResponse.builder()
                .success(false)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build();
    }
}
