package com.securecloudstorage.monitoring.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Retryer;
import feign.codec.ErrorDecoder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * Feign Configuration Components
 * <PERSON><PERSON><PERSON> thành phần cấu hình cho Feign clients
 */
@Component
public class FeignRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            
            // Add correlation ID
            String correlationId = request.getHeader("X-Correlation-ID");
            if (correlationId != null) {
                requestTemplate.header("X-Correlation-ID", correlationId);
            }
            
            // Add user context
            String userId = request.getHeader("X-User-ID");
            if (userId != null) {
                requestTemplate.header("X-User-ID", userId);
            }
            
            // Add service identification
            requestTemplate.header("X-Service-Name", "monitoring-service");
            requestTemplate.header("X-Service-Version", "1.0.0");
        }
    }
}

@Component
class FeignErrorDecoder implements ErrorDecoder {

    @Override
    public Exception decode(String methodKey, feign.Response response) {
        if (response.status() >= 400 && response.status() < 500) {
            return new RuntimeException("Client error: " + response.status() + " " + response.reason());
        } else if (response.status() >= 500) {
            return new RuntimeException("Server error: " + response.status() + " " + response.reason());
        }
        return new Default().decode(methodKey, response);
    }
}

@Component
class FeignRetryer implements Retryer {

    private final int maxAttempts;
    private final long backoff;
    private int attempt;

    public FeignRetryer() {
        this(3, 1000);
    }

    public FeignRetryer(int maxAttempts, long backoff) {
        this.maxAttempts = maxAttempts;
        this.backoff = backoff;
        this.attempt = 1;
    }

    @Override
    public void continueOrPropagate(RetryableException e) {
        if (attempt++ >= maxAttempts) {
            throw e;
        }
        try {
            Thread.sleep(backoff * attempt);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw e;
        }
    }

    @Override
    public Retryer clone() {
        return new FeignRetryer(maxAttempts, backoff);
    }
}
