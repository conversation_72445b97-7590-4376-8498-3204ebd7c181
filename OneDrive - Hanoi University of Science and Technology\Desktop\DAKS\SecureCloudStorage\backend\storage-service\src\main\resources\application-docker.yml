server:
  port: 8082
  
spring:
  application:
    name: secure-cloud-storage-service
  
  # Database configuration for Docker
  datasource:
    url: *************************************
    driver-class-name: org.mariadb.jdbc.Driver
    username: storage_user
    password: storage_password
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
    
  # JPA/Hibernate configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MariaDBDialect
        format_sql: false
        jdbc:
          batch_size: 20
        cache:
          use_second_level_cache: true
          use_query_cache: true
    defer-datasource-initialization: false
        
  # File upload configuration
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 10MB

# File storage configuration
file:
  upload-dir: /app/uploads
  quarantine-dir: /app/quarantine
  max-file-size: 104857600 # 100MB
  allowed-extensions: 
    - jpg
    - jpeg
    - png
    - gif
    - pdf
    - docx
    - xlsx
    - pptx
    - txt
    - zip
    - rar

# Encryption configuration
encryption:
  algorithm: AES
  key-size: 256
  secret-key: ${ENCRYPTION_SECRET_KEY:mySecretKey123456789012345678901234567890}

# AI-Malware Detection integration
ai-malware:
  service-url: http://ai-malware-service:8086
  enabled: true
  timeout: 30s

# Network IDS integration
network-ids:
  service-url: http://network-ids-service:5000
  enabled: true
  timeout: 10s

# Security configuration
security:
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    allow-credentials: true

# Actuator configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Logging configuration for Docker
logging:
  level:
    com.securecloudstorage: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/storage-service.log
    max-size: 10MB
    max-history: 30
