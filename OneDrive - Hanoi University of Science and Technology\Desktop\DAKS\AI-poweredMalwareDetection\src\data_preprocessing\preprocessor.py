"""
Data preprocessing module for malware detection
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import joblib

class DataPreprocessor:
    """Data preprocessing for malware detection"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
    def preprocess_dataset(self, input_path, output_path):
        """Preprocess raw dataset"""
        
        self.logger.info(f"Preprocessing dataset: {input_path}")
        
        # Load raw data
        df = pd.read_csv(input_path)
        
        # Clean data
        df_clean = self._clean_data(df)
        
        # Handle missing values
        df_clean = self._handle_missing_values(df_clean)
        
        # Feature engineering
        df_features = self._feature_engineering(df_clean)
        
        # Encode categorical variables
        df_encoded = self._encode_categorical(df_features)
        
        # Save preprocessed data
        df_encoded.to_csv(output_path, index=False)
        
        self.logger.info(f"Preprocessed dataset saved: {output_path}")
        
        return df_encoded
    
    def _clean_data(self, df):
        """Clean raw data"""
        
        # Remove duplicates
        df = df.drop_duplicates()
        
        # Remove invalid entries
        df = df.dropna(subset=['file_path', 'label'])
        
        # Normalize labels
        df['label'] = df['label'].map({'malware': 1, 'clean': 0})
        
        return df
    
    def _handle_missing_values(self, df):
        """Handle missing values"""
        
        # Fill numeric columns with median
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col != 'label':
                df[col] = df[col].fillna(df[col].median())
        
        # Fill categorical columns with mode
        categorical_columns = df.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            if col not in ['file_path', 'label']:
                df[col] = df[col].fillna(df[col].mode()[0] if not df[col].mode().empty else 'unknown')
        
        return df
    
    def _feature_engineering(self, df):
        """Feature engineering"""
        
        # Create new features
        if 'file_size' in df.columns:
            df['file_size_log'] = np.log1p(df['file_size'])
            df['file_size_category'] = pd.cut(df['file_size'], 
                                            bins=[0, 1024, 10240, 102400, 1048576, float('inf')],
                                            labels=['very_small', 'small', 'medium', 'large', 'very_large'])
        
        if 'file_entropy' in df.columns:
            df['entropy_category'] = pd.cut(df['file_entropy'],
                                          bins=[0, 4, 6, 7, 8],
                                          labels=['low', 'medium', 'high', 'very_high'])
        
        # PE-specific features
        if 'pe_sections' in df.columns and 'pe_import_functions' in df.columns:
            df['functions_per_section'] = df['pe_import_functions'] / (df['pe_sections'] + 1)
        
        # Ratio features
        if 'pe_executable_sections' in df.columns and 'pe_sections' in df.columns:
            df['executable_section_ratio'] = df['pe_executable_sections'] / (df['pe_sections'] + 1)
        
        return df
    
    def _encode_categorical(self, df):
        """Encode categorical variables"""
        
        categorical_columns = df.select_dtypes(include=['object', 'category']).columns
        
        for col in categorical_columns:
            if col not in ['file_path', 'label']:
                # One-hot encoding for categorical variables
                dummies = pd.get_dummies(df[col], prefix=col)
                df = pd.concat([df, dummies], axis=1)
                df = df.drop(col, axis=1)
        
        return df
    
    def create_training_data(self, features_list, labels_list, output_path):
        """Create training data from feature lists"""
        
        self.logger.info("Creating training dataset...")
        
        # Combine features and labels
        df = pd.DataFrame(features_list)
        df['label'] = labels_list
        
        # Preprocess
        df = self._handle_missing_values(df)
        df = self._feature_engineering(df)
        
        # Save
        df.to_csv(output_path, index=False)
        
        self.logger.info(f"Training data created: {output_path}")
        
        return df
    
    def split_data(self, df, test_size=0.2, val_size=0.1):
        """Split data into train/validation/test sets"""
        
        X = df.drop('label', axis=1)
        y = df['label']
        
        # First split: train+val / test
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=y
        )
        
        # Second split: train / val
        val_size_adjusted = val_size / (1 - test_size)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, random_state=42, stratify=y_temp
        )
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def save_preprocessors(self, output_dir):
        """Save preprocessing objects"""
        
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # Save scaler
        joblib.dump(self.scaler, output_dir / 'scaler.pkl')
        
        # Save label encoder
        joblib.dump(self.label_encoder, output_dir / 'label_encoder.pkl')
        
        self.logger.info(f"Preprocessors saved to: {output_dir}")
    
    def load_preprocessors(self, input_dir):
        """Load preprocessing objects"""
        
        input_dir = Path(input_dir)
        
        # Load scaler
        scaler_path = input_dir / 'scaler.pkl'
        if scaler_path.exists():
            self.scaler = joblib.load(scaler_path)
        
        # Load label encoder
        encoder_path = input_dir / 'label_encoder.pkl'
        if encoder_path.exists():
            self.label_encoder = joblib.load(encoder_path)
        
        self.logger.info(f"Preprocessors loaded from: {input_dir}")

def generate_sample_data(output_path, num_samples=1000):
    """Generate sample training data for testing"""
    
    np.random.seed(42)
    
    # Generate synthetic features
    data = {
        'file_size': np.random.lognormal(10, 2, num_samples),
        'file_entropy': np.random.uniform(0, 8, num_samples),
        'pe_sections': np.random.randint(1, 20, num_samples),
        'pe_import_dlls': np.random.randint(0, 50, num_samples),
        'pe_import_functions': np.random.randint(0, 200, num_samples),
        'pe_export_functions': np.random.randint(0, 50, num_samples),
        'string_count': np.random.randint(10, 1000, num_samples),
        'url_count': np.random.randint(0, 20, num_samples),
        'ip_count': np.random.randint(0, 10, num_samples),
        'registry_count': np.random.randint(0, 30, num_samples),
        'suspicious_strings': np.random.randint(0, 15, num_samples),
        'yara_matches': np.random.randint(0, 10, num_samples),
        'pe_executable_sections': np.random.randint(0, 5, num_samples),
        'pe_writable_sections': np.random.randint(0, 3, num_samples),
        'avg_section_entropy': np.random.uniform(2, 8, num_samples),
        'max_section_entropy': np.random.uniform(4, 8, num_samples),
        'suspicious_imports': np.random.randint(0, 20, num_samples)
    }
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Generate labels based on features (simple heuristic)
    malware_score = (
        (df['file_entropy'] > 6.5) * 2 +
        (df['suspicious_strings'] > 5) * 2 +
        (df['yara_matches'] > 0) * 3 +
        (df['suspicious_imports'] > 10) * 1 +
        (df['url_count'] > 5) * 1 +
        (df['registry_count'] > 10) * 1
    )
    
    # Add some randomness
    malware_score += np.random.normal(0, 1, num_samples)
    
    # Create binary labels
    threshold = np.percentile(malware_score, 70)  # 30% malware
    df['label'] = (malware_score > threshold).astype(int)
    
    # Save
    df.to_csv(output_path, index=False)
    
    print(f"Generated {num_samples} samples with {df['label'].sum()} malware samples")
    print(f"Saved to: {output_path}")
    
    return df

if __name__ == "__main__":
    # Generate sample data
    output_file = "data/training_data.csv"
    Path("data").mkdir(exist_ok=True)
    generate_sample_data(output_file)
