package com.securecloudstorage.gateway.client;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * Network IDS Client
 * Tích hợp với Network IDS Python service
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class NetworkIDSClient {

    private final WebClient webClient;
    
    public NetworkIDSClient() {
        this.webClient = WebClient.builder()
            .baseUrl("http://localhost:5000")
            .build();
    }

    public Mono<NetworkIDSResult> checkThreat(String clientIp, HttpHeaders headers) {
        return webClient.post()
            .uri("/api/check-threat")
            .bodyValue(NetworkIDSRequest.builder()
                .clientIp(clientIp)
                .headers(headers.toSingleValueMap())
                .timestamp(System.currentTimeMillis())
                .build())
            .retrieve()
            .bodyToMono(NetworkIDSResult.class)
            .doOnSuccess(result -> log.debug("Network IDS result for IP {}: {}", clientIp, result))
            .doOnError(error -> log.error("Network IDS check failed for IP {}: {}", clientIp, error.getMessage()))
            .onErrorReturn(NetworkIDSResult.builder()
                .threat(false)
                .threatType("check_failed")
                .threatScore(0.0)
                .build());
    }

    public Mono<NetworkIDSStats> getStats() {
        return webClient.get()
            .uri("/api/stats")
            .retrieve()
            .bodyToMono(NetworkIDSStats.class)
            .doOnError(error -> log.error("Failed to get Network IDS stats: {}", error.getMessage()));
    }

    public Mono<String> getAlerts() {
        return webClient.get()
            .uri("/api/alerts")
            .retrieve()
            .bodyToMono(String.class)
            .doOnError(error -> log.error("Failed to get Network IDS alerts: {}", error.getMessage()));
    }
}
