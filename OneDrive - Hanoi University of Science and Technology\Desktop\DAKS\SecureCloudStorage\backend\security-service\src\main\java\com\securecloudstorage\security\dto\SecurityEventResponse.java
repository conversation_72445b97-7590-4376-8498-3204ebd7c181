package com.securecloudstorage.security.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Security Event Response DTO
 * Dữ liệu response cho security event
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecurityEventResponse {
    
    private Long id;                    // ID của event
    private String eventType;           // Loại event
    private String severity;            // Mức độ nghiêm trọng
    private String description;         // Mô tả chi tiết
    private String userId;              // ID người dùng
    private String resourceId;          // ID tài nguyên
    private String ipAddress;           // IP address
    private String userAgent;           // User-Agent
    private String sessionId;           // Session ID
    private Map<String, Object> metadata; // Metadata
    private LocalDateTime timestamp;    // Thời gian
    private String source;              // Nguồn gốc
    private String action;              // Hành động
    private String targetEntity;        // Entity target
    private String targetEntityId;      // ID entity target
    private Boolean isSuccessful;       // Trạng thái thành công
    private String failureReason;       // Lý do thất bại
    private String geolocation;         // Vị trí địa lý
    private String deviceInfo;          // Thông tin thiết bị
    private String applicationVersion;  // Phiên bản app
    private String riskScore;           // Điểm rủi ro
    private String threatLevel;         // Mức độ nguy hiểm
    private String correlationId;       // Correlation ID
    private String customData;          // Dữ liệu tùy chỉnh
    private LocalDateTime createdAt;    // Thời gian tạo
    private LocalDateTime updatedAt;    // Thời gian cập nhật
    private String status;              // Trạng thái xử lý
    private String assignedTo;          // Được assign cho ai
    private String resolution;          // Cách giải quyết
    private String notes;               // Ghi chú
    private Integer priority;           // Độ ưu tiên
    private String category;            // Danh mục
    private String subcategory;         // Danh mục con
    private Boolean isProcessed;        // Đã xử lý chưa
    private String processedBy;         // Được xử lý bởi ai
    private LocalDateTime processedAt;  // Thời gian xử lý
    private String tags;                // Tags (comma-separated)
    private String alertId;             // ID alert liên quan
    private String incidentId;          // ID incident liên quan
}
