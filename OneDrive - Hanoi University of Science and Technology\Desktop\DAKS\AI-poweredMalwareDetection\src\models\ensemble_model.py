"""
Ensemble model for malware detection
"""

import os
import pickle
import numpy as np
import pandas as pd
import logging
from pathlib import Path
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense, Dropout
import xgboost as xgb
import lightgbm as lgb
from imblearn.over_sampling import SMOTE

class EnsembleDetector:
    """Ensemble detector combining multiple ML models"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.models = {}
        self.scaler = StandardScaler()
        self.is_trained = False
        
        # Initialize models
        self._initialize_models()
        
        # Load pre-trained models if available
        self._load_models()
    
    def _initialize_models(self):
        """Initialize ML models"""
        
        model_config = self.config.get('models', {})
        
        # Random Forest
        if model_config.get('random_forest', {}).get('enabled', True):
            rf_config = model_config['random_forest']
            self.models['random_forest'] = RandomForestClassifier(
                n_estimators=rf_config.get('n_estimators', 100),
                max_depth=rf_config.get('max_depth', 20),
                random_state=rf_config.get('random_state', 42),
                n_jobs=-1
            )
        
        # SVM
        if model_config.get('svm', {}).get('enabled', True):
            svm_config = model_config['svm']
            self.models['svm'] = SVC(
                kernel=svm_config.get('kernel', 'rbf'),
                C=svm_config.get('C', 1.0),
                gamma=svm_config.get('gamma', 'scale'),
                probability=True
            )
        
        # XGBoost
        if model_config.get('xgboost', {}).get('enabled', True):
            xgb_config = model_config['xgboost']
            self.models['xgboost'] = xgb.XGBClassifier(
                n_estimators=xgb_config.get('n_estimators', 100),
                max_depth=xgb_config.get('max_depth', 6),
                learning_rate=xgb_config.get('learning_rate', 0.1),
                random_state=42
            )
        
        # LightGBM
        if model_config.get('lightgbm', {}).get('enabled', False):
            lgb_config = model_config.get('lightgbm', {})
            self.models['lightgbm'] = lgb.LGBMClassifier(
                n_estimators=lgb_config.get('n_estimators', 100),
                max_depth=lgb_config.get('max_depth', 6),
                learning_rate=lgb_config.get('learning_rate', 0.1),
                random_state=42
            )
        
        # Neural Network
        if model_config.get('neural_network', {}).get('enabled', True):
            self.nn_config = model_config['neural_network']
            self.models['neural_network'] = None  # Will be created during training
        
        self.logger.info(f"Initialized {len(self.models)} models")
    
    def train(self, data_path='data/training_data.csv'):
        """Train ensemble models"""
        
        self.logger.info("Starting ensemble training...")
        
        # Load training data
        X, y = self._load_training_data(data_path)
        
        if X is None or y is None:
            self.logger.error("No training data available")
            return
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Save feature names for later use
        self.feature_names = X_train.columns.tolist()
        
        # Handle class imbalance
        smote = SMOTE(random_state=42)
        X_train_balanced, y_train_balanced = smote.fit_resample(X_train, y_train)
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train_balanced)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train each model
        for model_name, model in self.models.items():
            if model_name == 'neural_network':
                self._train_neural_network(X_train_scaled, y_train_balanced, X_test_scaled, y_test)
            else:
                self._train_model(model_name, model, X_train_scaled, y_train_balanced, X_test_scaled, y_test)
        
        self.is_trained = True
        
        # Save models
        self._save_models()
        
        self.logger.info("Ensemble training completed")
    
    def _train_model(self, model_name, model, X_train, y_train, X_test, y_test):
        """Train a single model"""
        
        self.logger.info(f"Training {model_name}...")
        
        try:
            # Train model
            model.fit(X_train, y_train)
            
            # Evaluate model
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted')
            recall = recall_score(y_test, y_pred, average='weighted')
            f1 = f1_score(y_test, y_pred, average='weighted')
            
            self.logger.info(f"{model_name} - Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, "
                           f"Recall: {recall:.4f}, F1: {f1:.4f}")
            
        except Exception as e:
            self.logger.error(f"Error training {model_name}: {str(e)}")
            self.models[model_name] = None
    
    def _train_neural_network(self, X_train, y_train, X_test, y_test):
        """Train neural network model"""
        
        self.logger.info("Training neural network...")
        
        try:
            # Create model
            model = Sequential()
            
            # Input layer
            model.add(Dense(self.nn_config.get('hidden_layers', [128, 64, 32])[0], 
                          input_dim=X_train.shape[1], 
                          activation=self.nn_config.get('activation', 'relu')))
            model.add(Dropout(0.3))
            
            # Hidden layers
            for units in self.nn_config.get('hidden_layers', [128, 64, 32])[1:]:
                model.add(Dense(units, activation=self.nn_config.get('activation', 'relu')))
                model.add(Dropout(0.3))
            
            # Output layer
            model.add(Dense(1, activation='sigmoid'))
            
            # Compile model
            model.compile(
                optimizer=self.nn_config.get('optimizer', 'adam'),
                loss='binary_crossentropy',
                metrics=['accuracy']
            )
            
            # Train model
            history = model.fit(
                X_train, y_train,
                epochs=self.nn_config.get('epochs', 50),
                batch_size=self.nn_config.get('batch_size', 32),
                validation_data=(X_test, y_test),
                verbose=0
            )
            
            # Evaluate
            test_loss, test_accuracy = model.evaluate(X_test, y_test, verbose=0)
            self.logger.info(f"Neural Network - Test Accuracy: {test_accuracy:.4f}")
            
            self.models['neural_network'] = model
            
        except Exception as e:
            self.logger.error(f"Error training neural network: {str(e)}")
            self.models['neural_network'] = None
    
    def predict(self, file_path, static_features=None, dynamic_features=None):
        """Predict if file is malware using ensemble"""
        
        if not self.is_trained:
            self._load_models()
        
        if not self.is_trained:
            self.logger.error("Models not trained. Please train models first.")
            return None
        
        try:
            # Combine features
            features = self._combine_features(static_features, dynamic_features)
            
            if features is None:
                self.logger.error("No features available for prediction")
                return None
            
            # Convert to DataFrame
            feature_df = pd.DataFrame([features])
            
            # Handle missing values
            feature_df = feature_df.fillna(0)
            
            # Ensure features are in the same order as training
            if hasattr(self, 'feature_names'):
                # Reorder columns to match training order
                feature_df = feature_df.reindex(columns=self.feature_names, fill_value=0)
            
            # Scale features
            features_scaled = self.scaler.transform(feature_df)
            
            # Get predictions from each model
            predictions = []
            confidences = []
            
            for model_name, model in self.models.items():
                if model is None:
                    continue
                
                try:
                    if model_name == 'neural_network':
                        pred_proba = model.predict(features_scaled)[0][0]
                        pred = 1 if pred_proba > 0.5 else 0
                    else:
                        pred = model.predict(features_scaled)[0]
                        pred_proba = model.predict_proba(features_scaled)[0][1]
                    
                    predictions.append(pred)
                    confidences.append(pred_proba)
                    
                except Exception as e:
                    self.logger.warning(f"Error predicting with {model_name}: {str(e)}")
            
            if not predictions:
                self.logger.error("No models available for prediction")
                return None
            
            # Ensemble prediction (majority voting)
            ensemble_pred = 1 if sum(predictions) > len(predictions) / 2 else 0
            ensemble_confidence = sum(confidences) / len(confidences)
            
            # Threat analysis
            threats = self._analyze_threats(static_features, dynamic_features)
            
            result = {
                'prediction': 'malware' if ensemble_pred == 1 else 'clean',
                'confidence': ensemble_confidence,
                'model_predictions': dict(zip(self.models.keys(), predictions)),
                'model_confidences': dict(zip(self.models.keys(), confidences)),
                'threats': threats,
                'features': features
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in ensemble prediction: {str(e)}")
            return None
    
    def _combine_features(self, static_features, dynamic_features):
        """Combine static and dynamic features"""
        
        features = {}
        
        # Add static features
        if static_features:
            features.update(static_features)
        
        # Add dynamic features
        if dynamic_features:
            features.update(dynamic_features)
        
        # Ensure all expected features are present with default values
        expected_features = self._get_expected_features()
        
        combined_features = {}
        for feature in expected_features:
            if feature in features:
                combined_features[feature] = features[feature]
            else:
                combined_features[feature] = 0
        
        return combined_features
    
    def _get_expected_features(self):
        """Get list of expected features"""
        
        # This should match the training data features
        features = [
            'file_size', 'pe_sections', 'pe_import_dlls', 'pe_import_functions',
            'pe_export_functions', 'string_count', 'url_count', 'ip_count',
            'registry_count', 'suspicious_strings', 'yara_matches',
            'pe_executable_sections', 'pe_writable_sections', 'avg_section_entropy',
            'max_section_entropy', 'suspicious_imports', 'file_entropy'
        ]
        
        # Add dynamic features if enabled
        if self.config.get('features', {}).get('dynamic_features', {}).get('enabled', False):
            features.extend([
                'file_creates', 'file_modifies', 'file_deletes',
                'network_connections', 'unique_remote_ips',
                'registry_changes', 'process_events',
                'avg_cpu_usage', 'max_cpu_usage',
                'avg_memory_usage', 'max_memory_usage'
            ])
        
        return features
    
    def _analyze_threats(self, static_features, dynamic_features):
        """Analyze specific threats"""
        
        threats = []
        
        if static_features:
            # High entropy might indicate packed/encrypted malware
            if static_features.get('file_entropy', 0) > 7.0:
                threats.append("High entropy - possible packed/encrypted malware")
            
            # Many suspicious imports
            if static_features.get('suspicious_imports', 0) > 5:
                threats.append("Multiple suspicious API imports")
            
            # YARA rule matches
            if static_features.get('yara_matches', 0) > 0:
                threats.append("Matched YARA rules")
            
            # Suspicious strings
            if static_features.get('suspicious_strings', 0) > 0:
                threats.append("Contains suspicious strings")
        
        if dynamic_features:
            # Excessive file operations
            if dynamic_features.get('file_creates', 0) > 10:
                threats.append("Excessive file creation activity")
            
            # Network connections
            if dynamic_features.get('network_connections', 0) > 0:
                threats.append("Establishes network connections")
            
            # Registry modifications
            if dynamic_features.get('registry_changes', 0) > 0:
                threats.append("Modifies registry keys")
        
        return threats
    
    def _load_training_data(self, data_path):
        """Load training data"""
        
        if not os.path.exists(data_path):
            self.logger.warning(f"Training data not found: {data_path}")
            return None, None
        
        try:
            df = pd.read_csv(data_path)
            
            if 'label' not in df.columns:
                self.logger.error("Training data must have 'label' column")
                return None, None
            
            y = df['label']
            X = df.drop('label', axis=1)
            
            self.logger.info(f"Loaded training data: {X.shape[0]} samples, {X.shape[1]} features")
            return X, y
            
        except Exception as e:
            self.logger.error(f"Error loading training data: {str(e)}")
            return None, None
    
    def _save_models(self):
        """Save trained models"""
        
        models_dir = Path('models')
        models_dir.mkdir(exist_ok=True)
        
        try:
            # Save scaler
            with open(models_dir / 'scaler.pkl', 'wb') as f:
                pickle.dump(self.scaler, f)
            
            # Save feature names
            if hasattr(self, 'feature_names'):
                with open(models_dir / 'feature_names.pkl', 'wb') as f:
                    pickle.dump(self.feature_names, f)
            
            # Save models
            for model_name, model in self.models.items():
                if model is None:
                    continue
                
                if model_name == 'neural_network':
                    model.save(models_dir / 'neural_network.h5')
                else:
                    with open(models_dir / f'{model_name}.pkl', 'wb') as f:
                        pickle.dump(model, f)
            
            self.logger.info("Models saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving models: {str(e)}")
    
    def _load_models(self):
        """Load pre-trained models"""
        
        models_dir = Path('models')
        
        if not models_dir.exists():
            self.logger.warning("Models directory not found")
            return
        
        try:
            # Load scaler
            scaler_path = models_dir / 'scaler.pkl'
            if scaler_path.exists():
                with open(scaler_path, 'rb') as f:
                    self.scaler = pickle.load(f)
            
            # Load feature names
            feature_names_path = models_dir / 'feature_names.pkl'
            if feature_names_path.exists():
                with open(feature_names_path, 'rb') as f:
                    self.feature_names = pickle.load(f)
            
            # Load models
            for model_name in list(self.models.keys()):
                if model_name == 'neural_network':
                    nn_path = models_dir / 'neural_network.h5'
                    if nn_path.exists():
                        self.models[model_name] = tf.keras.models.load_model(nn_path)
                else:
                    model_path = models_dir / f'{model_name}.pkl'
                    if model_path.exists():
                        with open(model_path, 'rb') as f:
                            self.models[model_name] = pickle.load(f)
            
            # Check if any models were loaded
            loaded_models = [name for name, model in self.models.items() if model is not None]
            
            if loaded_models:
                self.is_trained = True
                self.logger.info(f"Loaded models: {loaded_models}")
            else:
                self.logger.warning("No pre-trained models found")
            
        except Exception as e:
            self.logger.error(f"Error loading models: {str(e)}")
    
    def get_model_info(self):
        """Get information about loaded models"""
        
        info = {
            'is_trained': self.is_trained,
            'available_models': list(self.models.keys()),
            'loaded_models': [name for name, model in self.models.items() if model is not None]
        }
        
        return info
