package com.securecloudstorage.monitoring.service;

import com.securecloudstorage.monitoring.dto.SystemMetricsResponse;
import com.securecloudstorage.monitoring.entity.SystemMetric;
import com.securecloudstorage.monitoring.entity.ServiceMetric;
import com.securecloudstorage.monitoring.repository.SystemMetricRepository;
import com.securecloudstorage.monitoring.repository.ServiceMetricRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.cache.annotation.Cacheable;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Gauge;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.ThreadMXBean;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Metrics Service
 * Dịch vụ thu thập và xử lý metrics hệ thống
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MetricsService {

    private final SystemMetricRepository systemMetricRepository;
    private final ServiceMetricRepository serviceMetricRepository;
    private final MeterRegistry meterRegistry;
    private final HealthCheckService healthCheckService;

    // JVM Management Beans
    private final OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    private final ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();

    // Cache for metrics
    private final Map<String, Object> metricsCache = new HashMap<>();
    private LocalDateTime lastCacheUpdate = LocalDateTime.now().minusHours(1);

    /**
     * Lấy system metrics tổng quan
     */
    @Cacheable(value = "systemMetrics", unless = "#result == null")
    public SystemMetricsResponse getSystemMetrics() {
        try {
            SystemMetricsResponse response = new SystemMetricsResponse();
            
            // Basic system info
            response.setSystemStatus(getSystemStatus());
            response.setSystemHealth(calculateSystemHealth());
            response.setTimestamp(LocalDateTime.now());
            response.setUptime(getSystemUptime());
            response.setVersion(getSystemVersion());
            
            // CPU metrics
            setCpuMetrics(response);
            
            // Memory metrics
            setMemoryMetrics(response);
            
            // Disk metrics
            setDiskMetrics(response);
            
            // Network metrics
            setNetworkMetrics(response);
            
            // Application metrics
            setApplicationMetrics(response);
            
            // Database metrics
            setDatabaseMetrics(response);
            
            // JVM metrics
            setJVMMetrics(response);
            
            // Security metrics
            setSecurityMetrics(response);
            
            // Custom metrics
            setCustomMetrics(response);
            
            // Save to database
            saveSystemMetrics(response);
            
            return response;
            
        } catch (Exception e) {
            log.error("Error getting system metrics: ", e);
            return createErrorResponse();
        }
    }

    /**
     * Lấy metrics cho service cụ thể
     */
    public Map<String, Object> getServiceMetrics(String serviceName) {
        Map<String, Object> metrics = new HashMap<>();
        
        try {
            // Get service health
            var health = healthCheckService.checkService(serviceName);
            metrics.put("health", health);
            
            // Get service-specific metrics
            ServiceMetric serviceMetric = serviceMetricRepository.findTopByServiceNameOrderByTimestampDesc(serviceName);
            if (serviceMetric != null) {
                metrics.put("cpuUsage", serviceMetric.getCpuUsage());
                metrics.put("memoryUsage", serviceMetric.getMemoryUsage());
                metrics.put("requestCount", serviceMetric.getRequestCount());
                metrics.put("errorRate", serviceMetric.getErrorRate());
                metrics.put("responseTime", serviceMetric.getAverageResponseTime());
            }
            
            // Get custom metrics for service
            metrics.putAll(getCustomServiceMetrics(serviceName));
            
            return metrics;
            
        } catch (Exception e) {
            log.error("Error getting metrics for service {}: ", serviceName, e);
            metrics.put("error", "Failed to get service metrics");
            return metrics;
        }
    }

    /**
     * Lấy performance data
     */
    public Map<String, Object> getPerformanceData(String timeRange) {
        Map<String, Object> performance = new HashMap<>();
        
        try {
            LocalDateTime since = getTimeRangeStart(timeRange);
            
            // Get system metrics over time
            List<SystemMetric> metrics = systemMetricRepository.findByTimestampAfterOrderByTimestampDesc(since);
            
            // CPU performance
            List<Double> cpuUsage = metrics.stream()
                .map(SystemMetric::getCpuUsage)
                .collect(Collectors.toList());
            performance.put("cpuUsage", cpuUsage);
            
            // Memory performance
            List<Double> memoryUsage = metrics.stream()
                .map(SystemMetric::getMemoryUsagePercent)
                .collect(Collectors.toList());
            performance.put("memoryUsage", memoryUsage);
            
            // Response time performance
            List<ServiceMetric> serviceMetrics = serviceMetricRepository.findByTimestampAfterOrderByTimestampDesc(since);
            List<Double> responseTimes = serviceMetrics.stream()
                .map(ServiceMetric::getAverageResponseTime)
                .collect(Collectors.toList());
            performance.put("responseTimes", responseTimes);
            
            // Error rate performance
            List<Double> errorRates = serviceMetrics.stream()
                .map(ServiceMetric::getErrorRate)
                .collect(Collectors.toList());
            performance.put("errorRates", errorRates);
            
            // Throughput performance
            List<Long> throughput = serviceMetrics.stream()
                .map(ServiceMetric::getRequestCount)
                .collect(Collectors.toList());
            performance.put("throughput", throughput);
            
            // Timestamps
            List<LocalDateTime> timestamps = metrics.stream()
                .map(SystemMetric::getTimestamp)
                .collect(Collectors.toList());
            performance.put("timestamps", timestamps);
            
            return performance;
            
        } catch (Exception e) {
            log.error("Error getting performance data: ", e);
            performance.put("error", "Failed to get performance data");
            return performance;
        }
    }

    /**
     * Lấy logs
     */
    public Map<String, Object> getLogs(String service, String level, int lines) {
        Map<String, Object> logs = new HashMap<>();
        
        try {
            // Mock log data - in real implementation, integrate with logging system
            List<String> logEntries = new ArrayList<>();
            
            for (int i = 0; i < lines; i++) {
                logEntries.add(String.format("[%s] %s - Sample log entry %d", 
                    LocalDateTime.now().minusMinutes(i).toString(), 
                    level != null ? level : "INFO", 
                    i + 1));
            }
            
            logs.put("service", service);
            logs.put("level", level);
            logs.put("lines", lines);
            logs.put("entries", logEntries);
            logs.put("timestamp", LocalDateTime.now());
            
            return logs;
            
        } catch (Exception e) {
            log.error("Error getting logs: ", e);
            logs.put("error", "Failed to get logs");
            return logs;
        }
    }

    /**
     * Lấy capacity data
     */
    public Map<String, Object> getCapacityData() {
        Map<String, Object> capacity = new HashMap<>();
        
        try {
            // CPU capacity
            capacity.put("cpuCores", osBean.getAvailableProcessors());
            capacity.put("cpuUsage", osBean.getProcessCpuLoad() * 100);
            
            // Memory capacity
            long totalMemory = memoryBean.getHeapMemoryUsage().getMax();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            capacity.put("totalMemory", totalMemory);
            capacity.put("usedMemory", usedMemory);
            capacity.put("memoryUsage", (double) usedMemory / totalMemory * 100);
            
            // Disk capacity (mock data)
            capacity.put("totalDisk", 1000000000000L); // 1TB
            capacity.put("usedDisk", 500000000000L);   // 500GB
            capacity.put("diskUsage", 50.0);
            
            // Network capacity (mock data)
            capacity.put("networkBandwidth", 1000000000L); // 1Gbps
            capacity.put("networkUsage", 250000000L);      // 250Mbps
            capacity.put("networkUtilization", 25.0);
            
            // Database capacity (mock data)
            capacity.put("databaseConnections", 100);
            capacity.put("usedConnections", 45);
            capacity.put("connectionUsage", 45.0);
            
            // Predictions
            capacity.put("predictions", getCapacityPredictions());
            
            return capacity;
            
        } catch (Exception e) {
            log.error("Error getting capacity data: ", e);
            capacity.put("error", "Failed to get capacity data");
            return capacity;
        }
    }

    /**
     * Lấy SLA metrics
     */
    public Map<String, Object> getSLAMetrics() {
        Map<String, Object> sla = new HashMap<>();
        
        try {
            // Availability SLA
            sla.put("availabilityTarget", 99.9);
            sla.put("availabilityCurrent", 99.85);
            sla.put("availabilityStatus", "MEETING");
            
            // Response time SLA
            sla.put("responseTimeTarget", 200.0);
            sla.put("responseTimeCurrent", 185.0);
            sla.put("responseTimeStatus", "MEETING");
            
            // Error rate SLA
            sla.put("errorRateTarget", 1.0);
            sla.put("errorRateCurrent", 0.8);
            sla.put("errorRateStatus", "MEETING");
            
            // Throughput SLA
            sla.put("throughputTarget", 1000.0);
            sla.put("throughputCurrent", 1150.0);
            sla.put("throughputStatus", "EXCEEDING");
            
            // Overall SLA compliance
            sla.put("overallCompliance", 98.5);
            sla.put("slaViolations", 2);
            sla.put("lastViolation", LocalDateTime.now().minusDays(3));
            
            return sla;
            
        } catch (Exception e) {
            log.error("Error getting SLA metrics: ", e);
            sla.put("error", "Failed to get SLA metrics");
            return sla;
        }
    }

    /**
     * Lấy security metrics
     */
    public Map<String, Object> getSecurityMetrics() {
        Map<String, Object> security = new HashMap<>();
        
        try {
            // Mock security metrics - in real implementation, integrate with security service
            security.put("securityScore", 95.0);
            security.put("threatsDetected", 12);
            security.put("blockedIPs", 156);
            security.put("securityEvents", 2341);
            security.put("vulnerabilities", 3);
            security.put("securityAlerts", 8);
            security.put("lastSecurityScan", LocalDateTime.now().minusHours(2));
            security.put("securityStatus", "GOOD");
            
            return security;
            
        } catch (Exception e) {
            log.error("Error getting security metrics: ", e);
            security.put("error", "Failed to get security metrics");
            return security;
        }
    }

    /**
     * Lấy application metrics
     */
    public Map<String, Object> getApplicationMetrics() {
        Map<String, Object> application = new HashMap<>();
        
        try {
            // JVM metrics
            application.put("jvmVersion", System.getProperty("java.version"));
            application.put("threadCount", threadBean.getThreadCount());
            application.put("heapUsed", memoryBean.getHeapMemoryUsage().getUsed());
            application.put("heapMax", memoryBean.getHeapMemoryUsage().getMax());
            application.put("nonHeapUsed", memoryBean.getNonHeapMemoryUsage().getUsed());
            
            // Application-specific metrics
            application.put("totalRequests", getTotalRequests());
            application.put("requestsPerSecond", getRequestsPerSecond());
            application.put("averageResponseTime", getAverageResponseTime());
            application.put("errorRate", getErrorRate());
            
            // Database metrics
            application.put("databaseConnections", getDatabaseConnections());
            application.put("databaseQueries", getDatabaseQueries());
            
            return application;
            
        } catch (Exception e) {
            log.error("Error getting application metrics: ", e);
            application.put("error", "Failed to get application metrics");
            return application;
        }
    }

    /**
     * Lấy network metrics
     */
    public Map<String, Object> getNetworkMetrics() {
        Map<String, Object> network = new HashMap<>();
        
        try {
            // Mock network metrics - in real implementation, use system monitoring
            network.put("bytesIn", 1024000000L);
            network.put("bytesOut", 512000000L);
            network.put("packetsIn", 1000000L);
            network.put("packetsOut", 800000L);
            network.put("latency", 15.5);
            network.put("bandwidth", 1000000000L);
            network.put("utilization", 45.0);
            network.put("errors", 12);
            network.put("drops", 3);
            
            return network;
            
        } catch (Exception e) {
            log.error("Error getting network metrics: ", e);
            network.put("error", "Failed to get network metrics");
            return network;
        }
    }

    /**
     * Lấy database metrics
     */
    public Map<String, Object> getDatabaseMetrics() {
        Map<String, Object> database = new HashMap<>();
        
        try {
            // Mock database metrics - in real implementation, integrate with database monitoring
            database.put("totalConnections", 100);
            database.put("activeConnections", 45);
            database.put("idleConnections", 55);
            database.put("totalQueries", 1000000L);
            database.put("queryTime", 25.5);
            database.put("slowQueries", 156);
            database.put("cacheHitRate", 95.8);
            database.put("storage", 500000000L);
            database.put("storageUsage", 65.0);
            
            return database;
            
        } catch (Exception e) {
            log.error("Error getting database metrics: ", e);
            database.put("error", "Failed to get database metrics");
            return database;
        }
    }

    /**
     * Lấy custom metric
     */
    public Map<String, Object> getCustomMetric(String metricName) {
        Map<String, Object> metric = new HashMap<>();
        
        try {
            // Get custom metric from registry
            var meterValue = meterRegistry.find(metricName).gauge();
            if (meterValue != null) {
                metric.put("name", metricName);
                metric.put("value", meterValue.value());
                metric.put("timestamp", LocalDateTime.now());
            } else {
                metric.put("error", "Metric not found");
            }
            
            return metric;
            
        } catch (Exception e) {
            log.error("Error getting custom metric {}: ", metricName, e);
            metric.put("error", "Failed to get custom metric");
            return metric;
        }
    }

    /**
     * Export metrics
     */
    public String exportMetrics(String format) {
        try {
            SystemMetricsResponse metrics = getSystemMetrics();
            
            if ("json".equalsIgnoreCase(format)) {
                return convertToJson(metrics);
            } else if ("csv".equalsIgnoreCase(format)) {
                return convertToCsv(metrics);
            } else if ("xml".equalsIgnoreCase(format)) {
                return convertToXml(metrics);
            } else {
                return "Unsupported format: " + format;
            }
            
        } catch (Exception e) {
            log.error("Error exporting metrics: ", e);
            return "Error exporting metrics";
        }
    }

    /**
     * Thu thập metrics định kỳ
     */
    @Scheduled(fixedRate = 60000) // Every minute
    public void collectMetrics() {
        try {
            log.debug("Starting metrics collection...");
            
            // Collect system metrics
            SystemMetricsResponse metrics = getSystemMetrics();
            
            // Collect service metrics
            collectServiceMetrics();
            
            // Update cache
            updateMetricsCache(metrics);
            
            log.debug("Metrics collection completed");
            
        } catch (Exception e) {
            log.error("Error during metrics collection: ", e);
        }
    }

    // Private helper methods

    private String getSystemStatus() {
        double health = calculateSystemHealth();
        if (health >= 90) return "HEALTHY";
        if (health >= 70) return "WARNING";
        return "CRITICAL";
    }

    private Double calculateSystemHealth() {
        double cpuHealth = Math.max(0, 100 - (osBean.getProcessCpuLoad() * 100));
        double memoryHealth = Math.max(0, 100 - (getMemoryUsagePercent()));
        return (cpuHealth + memoryHealth) / 2;
    }

    private Long getSystemUptime() {
        return ManagementFactory.getRuntimeMXBean().getUptime() / 1000;
    }

    private String getSystemVersion() {
        return "1.0.0";
    }

    private void setCpuMetrics(SystemMetricsResponse response) {
        response.setCpuUsage(osBean.getProcessCpuLoad() * 100);
        response.setCpuCores(osBean.getAvailableProcessors());
        response.setCpuLoad1m(osBean.getSystemLoadAverage());
    }

    private void setMemoryMetrics(SystemMetricsResponse response) {
        var heapMemory = memoryBean.getHeapMemoryUsage();
        response.setMemoryTotal(heapMemory.getMax());
        response.setMemoryUsed(heapMemory.getUsed());
        response.setMemoryFree(heapMemory.getMax() - heapMemory.getUsed());
        response.setMemoryUsagePercent(getMemoryUsagePercent());
    }

    private void setDiskMetrics(SystemMetricsResponse response) {
        // Mock disk metrics - in real implementation, use system monitoring
        response.setDiskTotalSpace(1000000000000L);
        response.setDiskUsedSpace(500000000000L);
        response.setDiskFreeSpace(500000000000L);
        response.setDiskUsagePercent(50.0);
    }

    private void setNetworkMetrics(SystemMetricsResponse response) {
        // Mock network metrics
        response.setNetworkBytesIn(1024000000L);
        response.setNetworkBytesOut(512000000L);
        response.setNetworkLatency(15.5);
    }

    private void setApplicationMetrics(SystemMetricsResponse response) {
        response.setTotalRequests(getTotalRequests());
        response.setRequestsPerSecond(getRequestsPerSecond());
        response.setAverageResponseTime(getAverageResponseTime());
        response.setErrorRate(getErrorRate());
    }

    private void setDatabaseMetrics(SystemMetricsResponse response) {
        response.setActiveConnections(getDatabaseConnections());
        response.setTotalQueries(getDatabaseQueries());
    }

    private void setJVMMetrics(SystemMetricsResponse response) {
        SystemMetricsResponse.JVMMetrics jvmMetrics = new SystemMetricsResponse.JVMMetrics();
        jvmMetrics.setJvmVersion(System.getProperty("java.version"));
        jvmMetrics.setThreadCount(threadBean.getThreadCount());
        jvmMetrics.setHeapUsed(memoryBean.getHeapMemoryUsage().getUsed());
        jvmMetrics.setHeapMax(memoryBean.getHeapMemoryUsage().getMax());
        response.setJvmMetrics(jvmMetrics);
    }

    private void setSecurityMetrics(SystemMetricsResponse response) {
        response.setSecurityEvents(2341L);
        response.setThreatsDetected(12L);
        response.setBlockedIPs(156L);
        response.setSecurityAlerts(8L);
        response.setSecurityScore(95.0);
    }

    private void setCustomMetrics(SystemMetricsResponse response) {
        Map<String, Object> customMetrics = new HashMap<>();
        customMetrics.put("businessMetric1", 123.45);
        customMetrics.put("businessMetric2", "ACTIVE");
        response.setCustomMetrics(customMetrics);
    }

    private void saveSystemMetrics(SystemMetricsResponse response) {
        SystemMetric metric = new SystemMetric();
        metric.setTimestamp(response.getTimestamp());
        metric.setCpuUsage(response.getCpuUsage());
        metric.setMemoryUsagePercent(response.getMemoryUsagePercent());
        metric.setDiskUsagePercent(response.getDiskUsagePercent());
        metric.setSystemStatus(response.getSystemStatus());
        systemMetricRepository.save(metric);
    }

    private SystemMetricsResponse createErrorResponse() {
        SystemMetricsResponse response = new SystemMetricsResponse();
        response.setSystemStatus("UNKNOWN");
        response.setTimestamp(LocalDateTime.now());
        return response;
    }

    private Map<String, Object> getCustomServiceMetrics(String serviceName) {
        Map<String, Object> metrics = new HashMap<>();
        // Get custom metrics for specific service
        return metrics;
    }

    private LocalDateTime getTimeRangeStart(String timeRange) {
        switch (timeRange.toLowerCase()) {
            case "1h": return LocalDateTime.now().minusHours(1);
            case "24h": return LocalDateTime.now().minusDays(1);
            case "7d": return LocalDateTime.now().minusDays(7);
            case "30d": return LocalDateTime.now().minusDays(30);
            default: return LocalDateTime.now().minusHours(1);
        }
    }

    private Map<String, Object> getCapacityPredictions() {
        Map<String, Object> predictions = new HashMap<>();
        predictions.put("cpuPrediction", "CPU usage will reach 80% in 2 weeks");
        predictions.put("memoryPrediction", "Memory usage stable");
        predictions.put("diskPrediction", "Disk will be 70% full in 1 month");
        return predictions;
    }

    private void collectServiceMetrics() {
        // Collect metrics for each service
        String[] services = {"api-gateway", "storage-service", "user-service", "security-service"};
        
        for (String service : services) {
            try {
                ServiceMetric metric = new ServiceMetric();
                metric.setServiceName(service);
                metric.setTimestamp(LocalDateTime.now());
                metric.setCpuUsage(Math.random() * 100);
                metric.setMemoryUsage((long) (Math.random() * 1000000000));
                metric.setRequestCount((long) (Math.random() * 1000));
                metric.setErrorRate(Math.random() * 5);
                metric.setAverageResponseTime(Math.random() * 500);
                
                serviceMetricRepository.save(metric);
            } catch (Exception e) {
                log.error("Error collecting metrics for service {}: ", service, e);
            }
        }
    }

    private void updateMetricsCache(SystemMetricsResponse metrics) {
        metricsCache.put("systemMetrics", metrics);
        metricsCache.put("lastUpdate", LocalDateTime.now());
        lastCacheUpdate = LocalDateTime.now();
    }

    private double getMemoryUsagePercent() {
        var heapMemory = memoryBean.getHeapMemoryUsage();
        return (double) heapMemory.getUsed() / heapMemory.getMax() * 100;
    }

    private Long getTotalRequests() {
        return 1000000L + (long) (Math.random() * 100000);
    }

    private Long getRequestsPerSecond() {
        return 100L + (long) (Math.random() * 50);
    }

    private Double getAverageResponseTime() {
        return 150.0 + (Math.random() * 100);
    }

    private Double getErrorRate() {
        return Math.random() * 5;
    }

    private Integer getDatabaseConnections() {
        return 45 + (int) (Math.random() * 20);
    }

    private Long getDatabaseQueries() {
        return 50000L + (long) (Math.random() * 10000);
    }

    private String convertToJson(SystemMetricsResponse metrics) {
        // Convert to JSON format
        return "{}"; // Placeholder
    }

    private String convertToCsv(SystemMetricsResponse metrics) {
        // Convert to CSV format
        return "timestamp,cpu,memory,disk\n" + 
               metrics.getTimestamp() + "," + 
               metrics.getCpuUsage() + "," + 
               metrics.getMemoryUsagePercent() + "," + 
               metrics.getDiskUsagePercent();
    }

    private String convertToXml(SystemMetricsResponse metrics) {
        // Convert to XML format
        return "<metrics></metrics>"; // Placeholder
    }
}
