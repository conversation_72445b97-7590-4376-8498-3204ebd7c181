"""
Real-time monitoring system for malware detection
"""

import os
import time
import logging
import threading
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import shutil
from datetime import datetime

class RealtimeMonitor:
    """Real-time file system monitor for malware detection"""
    
    def __init__(self, detector, config):
        self.detector = detector
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.observer = Observer()
        self.is_running = False
        self.quarantine_dir = Path(config.get('monitoring', {}).get('quarantine_directory', 'quarantine'))
        self.quarantine_dir.mkdir(exist_ok=True)
        
        # Initialize handler
        self.handler = MalwareDetectionHandler(detector, config, self.quarantine_dir)
        
    def start(self):
        """Start real-time monitoring"""
        
        if self.is_running:
            self.logger.warning("Monitor is already running")
            return
        
        monitor_config = self.config.get('monitoring', {})
        watch_directories = monitor_config.get('watch_directories', [])
        file_extensions = monitor_config.get('real_time', {}).get('file_extensions', [])
        
        if not watch_directories:
            self.logger.error("No directories configured for monitoring")
            return
        
        self.logger.info("Starting real-time malware monitoring...")
        self.logger.info(f"Monitoring extensions: {file_extensions}")
        
        # Setup watchers for each directory
        for directory in watch_directories:
            if os.path.exists(directory):
                self.observer.schedule(self.handler, directory, recursive=True)
                self.logger.info(f"Monitoring directory: {directory}")
            else:
                self.logger.warning(f"Directory not found: {directory}")
        
        # Start observer
        self.observer.start()
        self.is_running = True
        
        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("Monitoring interrupted by user")
        finally:
            self.stop()
    
    def stop(self):
        """Stop real-time monitoring"""
        
        if not self.is_running:
            return
        
        self.logger.info("Stopping real-time monitoring...")
        self.observer.stop()
        self.observer.join()
        self.is_running = False
        
        # Print statistics
        self.handler.print_statistics()
    
    def get_statistics(self):
        """Get monitoring statistics"""
        return self.handler.get_statistics()

class MalwareDetectionHandler(FileSystemEventHandler):
    """File system event handler for malware detection"""
    
    def __init__(self, detector, config, quarantine_dir):
        self.detector = detector
        self.config = config
        self.quarantine_dir = quarantine_dir
        self.logger = logging.getLogger(__name__)
        
        # Statistics
        self.stats = {
            'files_scanned': 0,
            'malware_detected': 0,
            'files_quarantined': 0,
            'scan_errors': 0,
            'start_time': time.time()
        }
        
        # Get monitored file extensions
        self.monitored_extensions = set(
            config.get('monitoring', {}).get('real_time', {}).get('file_extensions', [])
        )
        
        # Performance settings
        self.max_file_size = config.get('performance', {}).get('max_file_size', 104857600)  # 100MB
        
        # Scan queue and thread pool
        self.scan_queue = []
        self.scan_lock = threading.Lock()
        self.scan_thread = threading.Thread(target=self._scan_worker, daemon=True)
        self.scan_thread.start()
        
    def on_created(self, event):
        """Handle file creation events"""
        if not event.is_directory:
            self._queue_file_for_scan(event.src_path, 'created')
    
    def on_modified(self, event):
        """Handle file modification events"""
        if not event.is_directory:
            self._queue_file_for_scan(event.src_path, 'modified')
    
    def on_moved(self, event):
        """Handle file move events"""
        if not event.is_directory:
            self._queue_file_for_scan(event.dest_path, 'moved')
    
    def _queue_file_for_scan(self, file_path, event_type):
        """Queue file for scanning"""
        
        # Check if file should be monitored
        if not self._should_monitor_file(file_path):
            return
        
        # Check file size
        try:
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                self.logger.info(f"Skipping large file: {file_path} ({file_size} bytes)")
                return
        except OSError:
            return
        
        # Add to scan queue
        with self.scan_lock:
            self.scan_queue.append({
                'path': file_path,
                'event_type': event_type,
                'timestamp': time.time()
            })
    
    def _should_monitor_file(self, file_path):
        """Check if file should be monitored"""
        
        # Check file extension
        if self.monitored_extensions:
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.monitored_extensions:
                return False
        
        # Skip quarantine directory
        if str(self.quarantine_dir) in file_path:
            return False
        
        # Skip system files
        system_paths = ['Windows\\System32', 'Windows\\SysWOW64']
        if any(sys_path in file_path for sys_path in system_paths):
            return False
        
        return True
    
    def _scan_worker(self):
        """Worker thread for scanning files"""
        
        while True:
            # Get next file to scan
            scan_item = None
            with self.scan_lock:
                if self.scan_queue:
                    scan_item = self.scan_queue.pop(0)
            
            if scan_item:
                self._scan_file(scan_item)
            else:
                time.sleep(0.1)  # Small delay when queue is empty
    
    def _scan_file(self, scan_item):
        """Scan a single file for malware"""
        
        file_path = scan_item['path']
        event_type = scan_item['event_type']
        
        try:
            # Check if file still exists
            if not os.path.exists(file_path):
                return
            
            # Wait for file to be fully written
            if event_type in ['created', 'modified']:
                time.sleep(0.5)
                if not os.path.exists(file_path):
                    return
            
            self.logger.info(f"Scanning file: {file_path}")
            
            # Import analyzers here to avoid circular imports
            from ..analysis.static_analyzer import StaticAnalyzer
            from ..analysis.dynamic_analyzer import DynamicAnalyzer
            
            # Perform static analysis
            static_analyzer = StaticAnalyzer(self.config)
            static_features = static_analyzer.analyze(file_path)
            
            # Perform dynamic analysis if enabled
            dynamic_features = None
            if self.config.get('features', {}).get('dynamic_features', {}).get('enabled', False):
                dynamic_analyzer = DynamicAnalyzer(self.config)
                dynamic_features = dynamic_analyzer.analyze(file_path, timeout=60)
            
            # Predict using ensemble model
            result = self.detector.predict(file_path, static_features, dynamic_features)
            
            if result:
                self.stats['files_scanned'] += 1
                
                if result['prediction'] == 'malware':
                    self.stats['malware_detected'] += 1
                    self._handle_malware_detection(file_path, result)
                else:
                    self.logger.info(f"File is clean: {file_path}")
            
        except Exception as e:
            self.stats['scan_errors'] += 1
            self.logger.error(f"Error scanning {file_path}: {str(e)}")
    
    def _handle_malware_detection(self, file_path, result):
        """Handle malware detection"""
        
        self.logger.warning(f"MALWARE DETECTED: {file_path}")
        self.logger.warning(f"Confidence: {result['confidence']:.2f}")
        
        if result.get('threats'):
            self.logger.warning("Detected threats:")
            for threat in result['threats']:
                self.logger.warning(f"  - {threat}")
        
        # Quarantine file
        if self._quarantine_file(file_path):
            self.stats['files_quarantined'] += 1
        
        # Send alert
        self._send_alert(file_path, result)
        
        # Log to file
        self._log_detection(file_path, result)
    
    def _quarantine_file(self, file_path):
        """Quarantine malicious file"""
        
        try:
            # Create quarantine filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            original_name = Path(file_path).name
            quarantine_name = f"{timestamp}_{original_name}"
            quarantine_path = self.quarantine_dir / quarantine_name
            
            # Move file to quarantine
            shutil.move(file_path, quarantine_path)
            
            # Create info file
            info_path = quarantine_path.with_suffix('.info')
            with open(info_path, 'w') as f:
                f.write(f"Original path: {file_path}\n")
                f.write(f"Quarantine time: {datetime.now()}\n")
                f.write(f"Quarantine name: {quarantine_name}\n")
            
            self.logger.info(f"File quarantined: {file_path} -> {quarantine_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error quarantining file {file_path}: {str(e)}")
            return False
    
    def _send_alert(self, file_path, result):
        """Send alert for malware detection"""
        
        # This could be extended to send email, SMS, or other notifications
        alert_msg = f"MALWARE ALERT: {file_path} detected as malware with confidence {result['confidence']:.2f}"
        
        # For now, just log the alert
        self.logger.critical(alert_msg)
        
        # Could integrate with external alerting systems here
        # Example: send_email_alert(alert_msg)
        # Example: send_slack_notification(alert_msg)
    
    def _log_detection(self, file_path, result):
        """Log malware detection to file"""
        
        try:
            log_dir = Path('logs/detections')
            log_dir.mkdir(parents=True, exist_ok=True)
            
            log_file = log_dir / 'malware_detections.log'
            
            with open(log_file, 'a') as f:
                f.write(f"[{datetime.now()}] MALWARE DETECTED\n")
                f.write(f"File: {file_path}\n")
                f.write(f"Confidence: {result['confidence']:.4f}\n")
                f.write(f"Threats: {', '.join(result.get('threats', []))}\n")
                f.write(f"Model predictions: {result.get('model_predictions', {})}\n")
                f.write("-" * 50 + "\n")
            
        except Exception as e:
            self.logger.error(f"Error logging detection: {str(e)}")
    
    def get_statistics(self):
        """Get monitoring statistics"""
        
        current_time = time.time()
        runtime = current_time - self.stats['start_time']
        
        stats = self.stats.copy()
        stats['runtime_seconds'] = runtime
        stats['runtime_formatted'] = self._format_runtime(runtime)
        
        if runtime > 0:
            stats['files_per_minute'] = (stats['files_scanned'] / runtime) * 60
            stats['detection_rate'] = (stats['malware_detected'] / max(stats['files_scanned'], 1)) * 100
        
        return stats
    
    def print_statistics(self):
        """Print monitoring statistics"""
        
        stats = self.get_statistics()
        
        print("\n" + "="*50)
        print("MONITORING STATISTICS")
        print("="*50)
        print(f"Runtime: {stats['runtime_formatted']}")
        print(f"Files scanned: {stats['files_scanned']}")
        print(f"Malware detected: {stats['malware_detected']}")
        print(f"Files quarantined: {stats['files_quarantined']}")
        print(f"Scan errors: {stats['scan_errors']}")
        
        if stats.get('files_per_minute'):
            print(f"Scan rate: {stats['files_per_minute']:.1f} files/minute")
        
        if stats.get('detection_rate'):
            print(f"Detection rate: {stats['detection_rate']:.2f}%")
        
        print("="*50)
    
    def _format_runtime(self, seconds):
        """Format runtime in human-readable format"""
        
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
