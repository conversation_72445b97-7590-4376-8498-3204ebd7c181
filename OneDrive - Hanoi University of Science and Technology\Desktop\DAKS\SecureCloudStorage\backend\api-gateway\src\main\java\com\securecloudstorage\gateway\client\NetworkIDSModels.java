package com.securecloudstorage.gateway.client;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Network IDS Request/Response DTOs
 */
@Data
@Builder
public class NetworkIDSRequest {
    private String clientIp;
    private Map<String, String> headers;
    private long timestamp;
}

@Data
@Builder
class NetworkIDSResult {
    private boolean threat;
    private String threatType;
    private double threatScore;
    private String description;
    private long timestamp;
    
    public boolean isThreat() {
        return threat;
    }
}

@Data
@Builder
class NetworkIDSStats {
    private long totalConnections;
    private long suspiciousConnections;
    private long alertsCount;
    private long blockedIpsCount;
    private String lastAlert;
}
