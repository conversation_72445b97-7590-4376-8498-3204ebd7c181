import { apiService, TokenManager } from './apiService';
import {
    LoginRequest,
    LoginResponse,
    RegisterRequest,
    User,
    ChangePasswordRequest,
    ApiResponse
} from '../types';

// Authentication Events
export enum AuthEvent {
    LOGIN = 'auth:login',
    LOGOUT = 'auth:logout',
    TOKEN_REFRESH = 'auth:token_refresh',
    SESSION_EXPIRED = 'auth:session_expired'
}

// Event emitter for authentication events
class AuthEventEmitter {
    private listeners: Map<string, Array<(data?: any) => void>> = new Map();

    on(event: string, callback: (data?: any) => void): void {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event)!.push(callback);
    }

    off(event: string, callback: (data?: any) => void): void {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            const index = eventListeners.indexOf(callback);
            if (index > -1) {
                eventListeners.splice(index, 1);
            }
        }
    }

    emit(event: string, data?: any): void {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            eventListeners.forEach(callback => callback(data));
        }
    }
}

// Authentication Service Class
class AuthService {
    private eventEmitter = new AuthEventEmitter();
    private currentUser: User | null = null;
    private refreshTimer: NodeJS.Timeout | null = null;

    constructor() {
        this.initializeAuth();
    }

    private initializeAuth(): void {
        // Check if user is already authenticated
        if (this.isAuthenticated()) {
            this.loadCurrentUser();
            this.scheduleTokenRefresh();
        }
    }

    // Authentication methods
    async login(credentials: LoginRequest): Promise<LoginResponse> {
        try {
            const response = await apiService.post<LoginResponse>('/api/users/login', credentials);

            if (response.success && response.data) {
                const loginData = response.data;

                // Store tokens
                TokenManager.setToken(loginData.token);
                TokenManager.setRefreshToken(loginData.refreshToken);

                // Set current user
                this.currentUser = loginData.user;

                // Schedule token refresh
                this.scheduleTokenRefresh();

                // Emit login event
                this.eventEmitter.emit(AuthEvent.LOGIN, loginData.user);

                return loginData;
            } else {
                throw new Error(response.message || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    async register(userData: RegisterRequest): Promise<ApiResponse<{ userId: string }>> {
        try {
            const response = await apiService.post<{ userId: string }>('/api/users/register', userData);

            if (response.success) {
                return response;
            } else {
                throw new Error(response.message || 'Registration failed');
            }
        } catch (error) {
            console.error('Registration error:', error);
            throw error;
        }
    }

    async logout(): Promise<void> {
        try {
            // Call logout endpoint to invalidate token on server
            const token = TokenManager.getToken();
            if (token) {
                await apiService.post('/api/users/logout', { token });
            }
        } catch (error) {
            console.error('Logout API error:', error);
            // Continue with local logout even if API call fails
        } finally {
            this.performLocalLogout();
        }
    }

    private performLocalLogout(): void {
        // Clear tokens
        TokenManager.clearTokens();

        // Clear current user
        this.currentUser = null;

        // Clear refresh timer
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
            this.refreshTimer = null;
        }

        // Emit logout event
        this.eventEmitter.emit(AuthEvent.LOGOUT);
    }

    async refreshToken(): Promise<void> {
        try {
            const refreshToken = TokenManager.getRefreshToken();
            if (!refreshToken) {
                throw new Error('No refresh token available');
            }

            const response = await apiService.post<LoginResponse>('/api/users/refresh', {
                refreshToken
            });

            if (response.success && response.data) {
                const { token, refreshToken: newRefreshToken, user } = response.data;

                // Update tokens
                TokenManager.setToken(token);
                TokenManager.setRefreshToken(newRefreshToken);

                // Update current user
                this.currentUser = user;

                // Schedule next refresh
                this.scheduleTokenRefresh();

                // Emit refresh event
                this.eventEmitter.emit(AuthEvent.TOKEN_REFRESH, user);
            } else {
                throw new Error('Token refresh failed');
            }
        } catch (error) {
            console.error('Token refresh error:', error);
            this.handleSessionExpired();
            throw error;
        }
    }

    private scheduleTokenRefresh(): void {
        const token = TokenManager.getToken();
        if (!token) return;

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const expirationTime = payload.exp * 1000; // Convert to milliseconds
            const currentTime = Date.now();
            const timeUntilExpiry = expirationTime - currentTime;

            // Refresh token 5 minutes before expiry
            const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 0);

            if (this.refreshTimer) {
                clearTimeout(this.refreshTimer);
            }

            this.refreshTimer = setTimeout(() => {
                this.refreshToken().catch(() => {
                    // Handle refresh failure
                    this.handleSessionExpired();
                });
            }, refreshTime);
        } catch (error) {
            console.error('Error scheduling token refresh:', error);
        }
    }

    private handleSessionExpired(): void {
        this.performLocalLogout();
        this.eventEmitter.emit(AuthEvent.SESSION_EXPIRED);
    }

    // User management methods
    async getCurrentUser(): Promise<User | null> {
        if (this.currentUser) {
            return this.currentUser;
        }

        if (this.isAuthenticated()) {
            await this.loadCurrentUser();
            return this.currentUser;
        }

        return null;
    }

    private async loadCurrentUser(): Promise<void> {
        try {
            const response = await apiService.get<User>('/api/users/me');
            if (response.success && response.data) {
                this.currentUser = response.data;
            }
        } catch (error) {
            console.error('Error loading current user:', error);
            this.handleSessionExpired();
        }
    }

    async updateProfile(userData: Partial<User>): Promise<User> {
        try {
            const response = await apiService.put<User>('/api/users/me', userData);

            if (response.success && response.data) {
                this.currentUser = response.data;
                return response.data;
            } else {
                throw new Error(response.message || 'Profile update failed');
            }
        } catch (error) {
            console.error('Profile update error:', error);
            throw error;
        }
    }

    async changePassword(passwordData: ChangePasswordRequest): Promise<void> {
        try {
            const response = await apiService.post('/api/users/change-password', passwordData);

            if (!response.success) {
                throw new Error(response.message || 'Password change failed');
            }
        } catch (error) {
            console.error('Password change error:', error);
            throw error;
        }
    }

    async forgotPassword(email: string): Promise<void> {
        try {
            const response = await apiService.post('/api/users/forgot-password', { email });

            if (!response.success) {
                throw new Error(response.message || 'Password reset request failed');
            }
        } catch (error) {
            console.error('Forgot password error:', error);
            throw error;
        }
    }

    async resetPassword(token: string, newPassword: string): Promise<void> {
        try {
            const response = await apiService.post('/api/users/reset-password', {
                token,
                newPassword
            });

            if (!response.success) {
                throw new Error(response.message || 'Password reset failed');
            }
        } catch (error) {
            console.error('Password reset error:', error);
            throw error;
        }
    }

    // Utility methods
    isAuthenticated(): boolean {
        return apiService.isAuthenticated();
    }

    getToken(): string | null {
        return TokenManager.getToken();
    }

    getUserRole(): string | null {
        return this.currentUser?.role || null;
    }

    hasRole(role: string): boolean {
        return this.currentUser?.role === role;
    }

    hasAnyRole(roles: string[]): boolean {
        return this.currentUser ? roles.includes(this.currentUser.role) : false;
    }

    // Event subscription methods
    onLogin(callback: (user: User) => void): void {
        this.eventEmitter.on(AuthEvent.LOGIN, callback);
    }

    onLogout(callback: () => void): void {
        this.eventEmitter.on(AuthEvent.LOGOUT, callback);
    }

    onTokenRefresh(callback: (user: User) => void): void {
        this.eventEmitter.on(AuthEvent.TOKEN_REFRESH, callback);
    }

    onSessionExpired(callback: () => void): void {
        this.eventEmitter.on(AuthEvent.SESSION_EXPIRED, callback);
    }

    // Remove event listeners
    offLogin(callback: (user: User) => void): void {
        this.eventEmitter.off(AuthEvent.LOGIN, callback);
    }

    offLogout(callback: () => void): void {
        this.eventEmitter.off(AuthEvent.LOGOUT, callback);
    }

    offTokenRefresh(callback: (user: User) => void): void {
        this.eventEmitter.off(AuthEvent.TOKEN_REFRESH, callback);
    }

    offSessionExpired(callback: () => void): void {
        this.eventEmitter.off(AuthEvent.SESSION_EXPIRED, callback);
    }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;