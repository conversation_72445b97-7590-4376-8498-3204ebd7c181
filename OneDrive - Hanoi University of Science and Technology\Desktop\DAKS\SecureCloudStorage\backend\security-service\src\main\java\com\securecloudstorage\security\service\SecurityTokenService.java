package com.securecloudstorage.security.service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Security Token Service
 * Dịch vụ xử lý security tokens
 */
@Service
@Slf4j
public class SecurityTokenService {

    @Value("${security.jwt.secret:mySecretKey}")
    private String jwtSecret;

    @Value("${security.jwt.expiration:86400}")
    private Long jwtExpiration;

    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(jwtSecret.getBytes());
    }

    /**
     * Validate security token
     */
    public Map<String, Object> validateToken(String token) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
            
            result.put("isValid", true);
            result.put("username", claims.getSubject());
            result.put("roles", claims.get("roles"));
            result.put("issuedAt", claims.getIssuedAt());
            result.put("expiration", claims.getExpiration());
            
            // Check if token is expired
            if (claims.getExpiration().before(new Date())) {
                result.put("isValid", false);
                result.put("error", "Token is expired");
            }
            
        } catch (Exception e) {
            log.error("Error validating token: ", e);
            result.put("isValid", false);
            result.put("error", "Invalid token");
        }
        
        return result;
    }

    /**
     * Generate security token
     */
    public String generateToken(String username, String roles) {
        Date now = new Date();
        Date expiration = new Date(now.getTime() + jwtExpiration * 1000);
        
        return Jwts.builder()
            .setSubject(username)
            .claim("roles", roles)
            .setIssuedAt(now)
            .setExpiration(expiration)
            .signWith(getSigningKey(), SignatureAlgorithm.HS256)
            .compact();
    }
}
