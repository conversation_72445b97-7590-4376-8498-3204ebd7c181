import { apiService } from './apiService';
import {
    FileInfo,
    UploadRequest,
    UploadResponse,
    FileListRequest,
    PaginatedResponse,
    ScanStatus,
    FileMetadata,
    MalwareAnalysis,
    ApiResponse
} from '../types';

// File upload progress callback
export type UploadProgressCallback = (progress: {
    loaded: number;
    total: number;
    percentage: number;
}) => void;

// File sharing options
export interface ShareFileRequest {
    fileId: string;
    shareWith: string[]; // User IDs or email addresses
    permissions: 'read' | 'write' | 'admin';
    expiresAt?: string;
    message?: string;
}

// File sharing info
export interface FileShare {
    id: string;
    fileId: string;
    sharedBy: string;
    sharedWith: string;
    permissions: string;
    createdAt: string;
    expiresAt?: string;
    isActive: boolean;
}

// Storage statistics
export interface StorageStatistics {
    totalFiles: number;
    totalSize: number;
    usedSpace: number;
    availableSpace: number;
    filesByType: Record<string, number>;
    filesByScanStatus: Record<ScanStatus, number>;
    uploadTrend: Array<{
        date: string;
        count: number;
        size: number;
    }>;
}

// Storage Service Class
class StorageService {
    // File upload methods
    async uploadFile(
        file: File,
        options: {
            encrypt?: boolean;
            metadata?: FileMetadata;
            onProgress?: UploadProgressCallback;
        } = {}
    ): Promise<UploadResponse> {
        try {
            const { encrypt = false, metadata, onProgress } = options;

            const additionalData: Record<string, any> = {
                encrypt: encrypt.toString()
            };

            if (metadata) {
                additionalData.metadata = JSON.stringify(metadata);
            }

            const response = await apiService.uploadFile<UploadResponse>(
                '/api/storage/upload',
                file,
                (progressEvent) => {
                    if (onProgress && progressEvent.total) {
                        const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        onProgress({
                            loaded: progressEvent.loaded,
                            total: progressEvent.total,
                            percentage
                        });
                    }
                },
                additionalData
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'File upload failed');
            }
        } catch (error) {
            console.error('File upload error:', error);
            throw error;
        }
    }

    async uploadMultipleFiles(
        files: File[],
        options: {
            encrypt?: boolean;
            metadata?: FileMetadata;
            onProgress?: (fileIndex: number, progress: { loaded: number; total: number; percentage: number }) => void;
            onFileComplete?: (fileIndex: number, result: UploadResponse) => void;
            onFileError?: (fileIndex: number, error: Error) => void;
        } = {}
    ): Promise<UploadResponse[]> {
        const results: UploadResponse[] = [];
        const { encrypt, metadata, onProgress, onFileComplete, onFileError } = options;

        for (let i = 0; i < files.length; i++) {
            try {
                const result = await this.uploadFile(files[i], {
                    encrypt,
                    metadata,
                    onProgress: (progress) => onProgress?.(i, progress)
                });

                results.push(result);
                onFileComplete?.(i, result);
            } catch (error) {
                const errorObj = error as Error;
                onFileError?.(i, errorObj);
                throw errorObj;
            }
        }

        return results;
    }

    // File listing and search
    async getFiles(request: FileListRequest = {}): Promise<PaginatedResponse<FileInfo>> {
        try {
            const params = new URLSearchParams();

            if (request.page !== undefined) params.append('page', request.page.toString());
            if (request.size !== undefined) params.append('size', request.size.toString());
            if (request.sortBy) params.append('sortBy', request.sortBy);
            if (request.sortDirection) params.append('sortDirection', request.sortDirection);
            if (request.filter) params.append('filter', request.filter);
            if (request.category) params.append('category', request.category);
            if (request.scanStatus) params.append('scanStatus', request.scanStatus);

            const response = await apiService.get<PaginatedResponse<FileInfo>>(
                `/api/storage/files?${params.toString()}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch files');
            }
        } catch (error) {
            console.error('Get files error:', error);
            throw error;
        }
    }

    async getFileById(fileId: string): Promise<FileInfo> {
        try {
            const response = await apiService.get<FileInfo>(`/api/storage/files/${fileId}`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch file');
            }
        } catch (error) {
            console.error('Get file by ID error:', error);
            throw error;
        }
    }

    async searchFiles(query: string, limit: number = 20): Promise<FileInfo[]> {
        try {
            const response = await apiService.get<FileInfo[]>(
                `/api/storage/search?q=${encodeURIComponent(query)}&limit=${limit}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to search files');
            }
        } catch (error) {
            console.error('Search files error:', error);
            throw error;
        }
    }

    // File download and access
    async downloadFile(fileId: string, filename?: string): Promise<void> {
        try {
            await apiService.downloadFile(`/api/storage/files/${fileId}/download`, filename);
        } catch (error) {
            console.error('Download file error:', error);
            throw error;
        }
    }

    async getFileDownloadUrl(fileId: string): Promise<string> {
        try {
            const response = await apiService.get<{ downloadUrl: string }>(
                `/api/storage/files/${fileId}/download-url`
            );

            if (response.success && response.data) {
                return response.data.downloadUrl;
            } else {
                throw new Error(response.message || 'Failed to get download URL');
            }
        } catch (error) {
            console.error('Get file download URL error:', error);
            throw error;
        }
    }

    async previewFile(fileId: string): Promise<{ previewUrl: string; contentType: string }> {
        try {
            const response = await apiService.get<{ previewUrl: string; contentType: string }>(
                `/api/storage/files/${fileId}/preview`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to get file preview');
            }
        } catch (error) {
            console.error('Preview file error:', error);
            throw error;
        }
    }

    // File management
    async updateFileMetadata(fileId: string, metadata: FileMetadata): Promise<FileInfo> {
        try {
            const response = await apiService.put<FileInfo>(`/api/storage/files/${fileId}/metadata`, metadata);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to update file metadata');
            }
        } catch (error) {
            console.error('Update file metadata error:', error);
            throw error;
        }
    }

    async renameFile(fileId: string, newName: string): Promise<FileInfo> {
        try {
            const response = await apiService.put<FileInfo>(`/api/storage/files/${fileId}/rename`, {
                newName
            });

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to rename file');
            }
        } catch (error) {
            console.error('Rename file error:', error);
            throw error;
        }
    }

    async deleteFile(fileId: string): Promise<void> {
        try {
            const response = await apiService.delete(`/api/storage/files/${fileId}`);

            if (!response.success) {
                throw new Error(response.message || 'Failed to delete file');
            }
        } catch (error) {
            console.error('Delete file error:', error);
            throw error;
        }
    }

    async deleteMultipleFiles(fileIds: string[]): Promise<void> {
        try {
            const response = await apiService.delete('/api/storage/files/bulk', {
                data: { fileIds }
            });

            if (!response.success) {
                throw new Error(response.message || 'Failed to delete files');
            }
        } catch (error) {
            console.error('Delete multiple files error:', error);
            throw error;
        }
    }

    // File sharing
    async shareFile(request: ShareFileRequest): Promise<FileShare> {
        try {
            const response = await apiService.post<FileShare>('/api/storage/share', request);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to share file');
            }
        } catch (error) {
            console.error('Share file error:', error);
            throw error;
        }
    }

    async getFileShares(fileId: string): Promise<FileShare[]> {
        try {
            const response = await apiService.get<FileShare[]>(`/api/storage/files/${fileId}/shares`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to get file shares');
            }
        } catch (error) {
            console.error('Get file shares error:', error);
            throw error;
        }
    }

    async revokeFileShare(shareId: string): Promise<void> {
        try {
            const response = await apiService.delete(`/api/storage/shares/${shareId}`);

            if (!response.success) {
                throw new Error(response.message || 'Failed to revoke file share');
            }
        } catch (error) {
            console.error('Revoke file share error:', error);
            throw error;
        }
    }

    async getSharedWithMe(page: number = 0, size: number = 20): Promise<PaginatedResponse<FileInfo>> {
        try {
            const response = await apiService.get<PaginatedResponse<FileInfo>>(
                `/api/storage/shared-with-me?page=${page}&size=${size}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to get shared files');
            }
        } catch (error) {
            console.error('Get shared with me error:', error);
            throw error;
        }
    }

    // File security and scanning
    async rescanFile(fileId: string): Promise<void> {
        try {
            const response = await apiService.post(`/api/storage/files/${fileId}/rescan`);

            if (!response.success) {
                throw new Error(response.message || 'Failed to rescan file');
            }
        } catch (error) {
            console.error('Rescan file error:', error);
            throw error;
        }
    }

    async getFileScanResult(fileId: string): Promise<MalwareAnalysis> {
        try {
            const response = await apiService.get<MalwareAnalysis>(`/api/storage/files/${fileId}/scan-result`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to get scan result');
            }
        } catch (error) {
            console.error('Get file scan result error:', error);
            throw error;
        }
    }

    async quarantineFile(fileId: string, reason?: string): Promise<void> {
        try {
            const response = await apiService.post(`/api/storage/files/${fileId}/quarantine`, {
                reason
            });

            if (!response.success) {
                throw new Error(response.message || 'Failed to quarantine file');
            }
        } catch (error) {
            console.error('Quarantine file error:', error);
            throw error;
        }
    }

    async releaseFromQuarantine(fileId: string): Promise<void> {
        try {
            const response = await apiService.post(`/api/storage/files/${fileId}/release`);

            if (!response.success) {
                throw new Error(response.message || 'Failed to release file from quarantine');
            }
        } catch (error) {
            console.error('Release from quarantine error:', error);
            throw error;
        }
    }

    // Storage statistics and analytics
    async getStorageStatistics(): Promise<StorageStatistics> {
        try {
            const response = await apiService.get<StorageStatistics>('/api/storage/statistics');

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to get storage statistics');
            }
        } catch (error) {
            console.error('Get storage statistics error:', error);
            throw error;
        }
    }

    async getUserStorageUsage(): Promise<{
        usedSpace: number;
        totalSpace: number;
        fileCount: number;
        percentageUsed: number;
    }> {
        try {
            const response = await apiService.get<{
                usedSpace: number;
                totalSpace: number;
                fileCount: number;
                percentageUsed: number;
            }>('/api/storage/usage');

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to get storage usage');
            }
        } catch (error) {
            console.error('Get storage usage error:', error);
            throw error;
        }
    }

    // Utility methods
    formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getFileIcon(mimeType: string): string {
        const iconMap: Record<string, string> = {
            'application/pdf': '📄',
            'application/msword': '📝',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '📝',
            'application/vnd.ms-excel': '📊',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '📊',
            'application/vnd.ms-powerpoint': '📈',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': '📈',
            'image/jpeg': '🖼️',
            'image/png': '🖼️',
            'image/gif': '🖼️',
            'image/svg+xml': '🖼️',
            'video/mp4': '🎥',
            'video/avi': '🎥',
            'video/mov': '🎥',
            'audio/mp3': '🎵',
            'audio/wav': '🎵',
            'audio/mpeg': '🎵',
            'application/zip': '📦',
            'application/x-rar-compressed': '📦',
            'text/plain': '📄',
            'text/csv': '📊',
        };

        return iconMap[mimeType] || '📄';
    }

    isImageFile(mimeType: string): boolean {
        return mimeType.startsWith('image/');
    }

    isVideoFile(mimeType: string): boolean {
        return mimeType.startsWith('video/');
    }

    isAudioFile(mimeType: string): boolean {
        return mimeType.startsWith('audio/');
    }

    isDocumentFile(mimeType: string): boolean {
        const documentTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv'
        ];

        return documentTypes.includes(mimeType);
    }
}

// Export singleton instance
export const storageService = new StorageService();
export default storageService;