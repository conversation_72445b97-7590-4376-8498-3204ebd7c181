package com.securecloudstorage.storage.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.securecloudstorage.storage.entity.FileEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * File Info Response DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileInfoResponse {
    
    private String fileId;
    private String fileName;
    private Long fileSize;
    private String contentType;
    private String mimeType;
    private String description;
    private boolean encrypted;
    private boolean quarantined;
    private boolean isPublic;
    private String checksum;
    private String tags;
    private Long accessCount;
    private FileEntity.ScanStatus scanStatus;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessed;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiryDate;
    
    // File URLs
    private String downloadUrl;
    private String viewUrl;
    private String shareUrl;
    
    // Security information
    private ScanInfo scanInfo;
    
    // Share information
    private ShareInfo shareInfo;
    
    @Data
    @Builder
    public static class ScanInfo {
        private boolean scanned;
        private boolean safe;
        private String threatType;
        private Double confidence;
        private LocalDateTime scanDate;
    }
    
    @Data
    @Builder
    public static class ShareInfo {
        private boolean shared;
        private Long shareCount;
        private LocalDateTime lastShared;
    }
}
