package com.securecloudstorage.security.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Threat Intelligence Entity
 * Entity lưu trữ threat intelligence data
 */
@Entity
@Table(name = "threat_intelligence")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThreatIntelligence {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ip_address")
    private String ipAddress;

    @Column(name = "domain")
    private String domain;

    @Column(name = "url", length = 2000)
    private String url;

    @Column(name = "hash_value")
    private String hashValue;

    @Column(name = "threat_type", nullable = false)
    private String threatType; // MALWARE, BOTNET, PHISHING, SPAM, etc.

    @Column(name = "severity", nullable = false)
    private String severity; // LOW, MEDIUM, HIGH, CRITICAL

    @Column(name = "confidence")
    private Integer confidence; // 0-100

    @Column(name = "source", nullable = false)
    private String source;

    @Column(name = "description", length = 2000)
    private String description;

    @Column(name = "first_seen")
    private LocalDateTime firstSeen;

    @Column(name = "last_seen")
    private LocalDateTime lastSeen;

    @Column(name = "is_active")
    private Boolean isActive;

    @Column(name = "tags")
    private String tags;

    @Column(name = "indicators", length = 5000)
    private String indicators;

    @Column(name = "ttl")
    private Integer ttl; // Time to live in hours

    @Column(name = "reputation_score")
    private Integer reputationScore; // 0-100

    @Column(name = "country")
    private String country;

    @Column(name = "organization")
    private String organization;

    @Column(name = "attack_vector")
    private String attackVector;

    @Column(name = "mitigation", length = 2000)
    private String mitigation;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        if (isActive == null) {
            isActive = true;
        }
        if (firstSeen == null) {
            firstSeen = LocalDateTime.now();
        }
        if (lastSeen == null) {
            lastSeen = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
        lastSeen = LocalDateTime.now();
    }
}
