-- Monitoring Service Database Schema
-- Database: monitoring_db

-- Extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Drop existing tables if they exist
DROP TABLE IF EXISTS alerts CASCADE;
DROP TABLE IF EXISTS metrics CASCADE;
DROP TABLE IF EXISTS service_health CASCADE;

-- Create alerts table
CREATE TABLE alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    alert_id VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    severity VARCHAR(50) NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    service VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('SYSTEM', 'SECURITY', 'PERFORMANCE', 'AVAILABILITY')),
    status VARCHAR(50) NOT NULL CHECK (status IN ('ACTIVE', '<PERSON><PERSON><PERSON><PERSON>LEDGED', 'RESOLVED', 'SUPPRESSED')),
    priority VARCHAR(10) CHECK (priority IN ('P1', 'P2', 'P3', 'P4')),
    category VARCHAR(100),
    source VARCHAR(255),
    host VARCHAR(255),
    environment VARCHAR(50) CHECK (environment IN ('DEV', 'STAGING', 'PROD')),
    metric_name VARCHAR(255),
    metric_value DECIMAL(20,6),
    threshold DECIMAL(20,6),
    condition VARCHAR(50) CHECK (condition IN ('GREATER_THAN', 'LESS_THAN', 'EQUALS')),
    assigned_to VARCHAR(255),
    assigned_by VARCHAR(255),
    resolved_by VARCHAR(255),
    resolution_notes TEXT,
    escalation_level VARCHAR(50) CHECK (escalation_level IN ('L1', 'L2', 'L3', 'MANAGEMENT')),
    escalated_by VARCHAR(255),
    escalated_at TIMESTAMP,
    auto_resolved BOOLEAN DEFAULT FALSE,
    auto_resolution_reason TEXT,
    suppressed_by VARCHAR(255),
    suppressed_until TIMESTAMP,
    suppression_reason TEXT,
    notification_sent BOOLEAN DEFAULT FALSE,
    last_notification_sent TIMESTAMP,
    notification_count INTEGER DEFAULT 0,
    sla_deadline TIMESTAMP,
    sla_breached BOOLEAN DEFAULT FALSE,
    tags TEXT,
    labels TEXT,
    runbook_url TEXT,
    dashboard_url TEXT,
    correlation_id VARCHAR(255),
    parent_alert_id VARCHAR(255),
    child_alert_count INTEGER DEFAULT 0,
    acknowledgment_required BOOLEAN DEFAULT TRUE,
    acknowledged_by VARCHAR(255),
    acknowledged_at TIMESTAMP,
    acknowledgment_notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP,
    duration_seconds BIGINT,
    first_occurrence TIMESTAMP,
    last_occurrence TIMESTAMP,
    occurrence_count INTEGER DEFAULT 1,
    active BOOLEAN DEFAULT TRUE,
    archived BOOLEAN DEFAULT FALSE,
    archived_at TIMESTAMP,
    archived_by VARCHAR(255),
    metadata TEXT,
    external_id VARCHAR(255),
    external_system VARCHAR(255),
    integration_data TEXT
);

-- Create metrics table
CREATE TABLE metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_id VARCHAR(255) UNIQUE NOT NULL,
    metric_name VARCHAR(255) NOT NULL,
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('GAUGE', 'COUNTER', 'HISTOGRAM', 'SUMMARY')),
    value DECIMAL(20,6) NOT NULL,
    unit VARCHAR(50),
    service VARCHAR(255) NOT NULL,
    instance VARCHAR(255),
    host VARCHAR(255),
    environment VARCHAR(50) CHECK (environment IN ('DEV', 'STAGING', 'PROD')),
    namespace VARCHAR(255),
    category VARCHAR(100) CHECK (category IN ('SYSTEM', 'APPLICATION', 'BUSINESS')),
    subcategory VARCHAR(100),
    tags TEXT,
    labels TEXT,
    dimensions TEXT,
    description TEXT,
    source VARCHAR(255),
    collector VARCHAR(255),
    collection_interval INTEGER,
    retention_period INTEGER,
    aggregation_method VARCHAR(50) CHECK (aggregation_method IN ('AVG', 'SUM', 'MIN', 'MAX', 'COUNT')),
    aggregation_window INTEGER,
    sampling_rate DECIMAL(5,4),
    threshold_warning DECIMAL(20,6),
    threshold_critical DECIMAL(20,6),
    threshold_operator VARCHAR(10) CHECK (threshold_operator IN ('GT', 'LT', 'EQ', 'NEQ', 'GTE', 'LTE')),
    alert_enabled BOOLEAN DEFAULT FALSE,
    alert_rule_id VARCHAR(255),
    dashboard_id VARCHAR(255),
    chart_type VARCHAR(50) CHECK (chart_type IN ('LINE', 'BAR', 'PIE', 'GAUGE', 'HEATMAP')),
    display_name VARCHAR(255),
    display_unit VARCHAR(50),
    display_format VARCHAR(50),
    display_color VARCHAR(50),
    sort_order INTEGER,
    is_key_metric BOOLEAN DEFAULT FALSE,
    is_sla_metric BOOLEAN DEFAULT FALSE,
    sla_target DECIMAL(20,6),
    sla_operator VARCHAR(10),
    business_impact VARCHAR(50) CHECK (business_impact IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    trend_direction VARCHAR(50) CHECK (trend_direction IN ('UP', 'DOWN', 'STABLE')),
    trend_percentage DECIMAL(10,2),
    baseline_value DECIMAL(20,6),
    baseline_period INTEGER,
    anomaly_detection_enabled BOOLEAN DEFAULT FALSE,
    anomaly_sensitivity VARCHAR(50) CHECK (anomaly_sensitivity IN ('LOW', 'MEDIUM', 'HIGH')),
    anomaly_threshold DECIMAL(10,6),
    forecast_enabled BOOLEAN DEFAULT FALSE,
    forecast_horizon INTEGER,
    forecast_confidence DECIMAL(5,4),
    data_quality_score DECIMAL(5,4),
    data_completeness DECIMAL(5,4),
    data_accuracy DECIMAL(5,4),
    data_freshness INTEGER,
    validation_rule TEXT,
    validation_status VARCHAR(50) CHECK (validation_status IN ('VALID', 'INVALID', 'PENDING')),
    validation_error TEXT,
    active BOOLEAN DEFAULT TRUE,
    archived BOOLEAN DEFAULT FALSE,
    archived_at TIMESTAMP,
    archived_by VARCHAR(255),
    metadata TEXT,
    external_id VARCHAR(255),
    external_system VARCHAR(255),
    integration_data TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    measured_at TIMESTAMP,
    processed_at TIMESTAMP,
    last_alert_at TIMESTAMP,
    alert_count INTEGER DEFAULT 0,
    min_value DECIMAL(20,6),
    max_value DECIMAL(20,6),
    avg_value DECIMAL(20,6),
    sum_value DECIMAL(20,6),
    count_value BIGINT,
    percentile_50 DECIMAL(20,6),
    percentile_90 DECIMAL(20,6),
    percentile_95 DECIMAL(20,6),
    percentile_99 DECIMAL(20,6),
    standard_deviation DECIMAL(20,6),
    variance DECIMAL(20,6)
);

-- Create service_health table
CREATE TABLE service_health (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    health_check_id VARCHAR(255) UNIQUE NOT NULL,
    service_name VARCHAR(255) NOT NULL,
    service_url VARCHAR(500) NOT NULL,
    health_endpoint VARCHAR(500),
    status VARCHAR(50) NOT NULL CHECK (status IN ('UP', 'DOWN', 'DEGRADED', 'UNKNOWN')),
    health_score DECIMAL(5,4),
    response_time BIGINT,
    timeout BIGINT,
    check_type VARCHAR(50) CHECK (check_type IN ('HTTP', 'TCP', 'PING', 'CUSTOM')),
    check_method VARCHAR(10) CHECK (check_method IN ('GET', 'POST', 'PUT', 'HEAD')),
    expected_status_code INTEGER,
    actual_status_code INTEGER,
    expected_response TEXT,
    actual_response TEXT,
    error_message TEXT,
    error_details TEXT,
    environment VARCHAR(50) CHECK (environment IN ('DEV', 'STAGING', 'PROD')),
    region VARCHAR(100),
    availability_zone VARCHAR(100),
    instance_id VARCHAR(255),
    host VARCHAR(255),
    port INTEGER,
    version VARCHAR(100),
    build_number VARCHAR(100),
    deployment_id VARCHAR(255),
    uptime BIGINT,
    start_time TIMESTAMP,
    last_restart TIMESTAMP,
    restart_count INTEGER DEFAULT 0,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    disk_usage DECIMAL(5,2),
    network_usage DECIMAL(5,2),
    active_connections INTEGER,
    max_connections INTEGER,
    thread_count INTEGER,
    max_threads INTEGER,
    heap_usage DECIMAL(5,2),
    non_heap_usage DECIMAL(5,2),
    gc_count BIGINT,
    gc_time BIGINT,
    load_average DECIMAL(5,2),
    request_count BIGINT,
    error_count BIGINT,
    error_rate DECIMAL(5,2),
    throughput DECIMAL(10,2),
    latency_p50 DECIMAL(10,2),
    latency_p90 DECIMAL(10,2),
    latency_p95 DECIMAL(10,2),
    latency_p99 DECIMAL(10,2),
    database_connections INTEGER,
    database_connection_pool_size INTEGER,
    cache_hit_rate DECIMAL(5,2),
    cache_miss_rate DECIMAL(5,2),
    queue_size INTEGER,
    queue_capacity INTEGER,
    dependencies TEXT,
    dependency_status TEXT,
    circuit_breaker_state VARCHAR(50) CHECK (circuit_breaker_state IN ('CLOSED', 'OPEN', 'HALF_OPEN')),
    circuit_breaker_failure_rate DECIMAL(5,2),
    circuit_breaker_slow_call_rate DECIMAL(5,2),
    check_interval INTEGER,
    check_count BIGINT DEFAULT 0,
    success_count BIGINT DEFAULT 0,
    failure_count BIGINT DEFAULT 0,
    consecutive_failures INTEGER DEFAULT 0,
    max_consecutive_failures INTEGER DEFAULT 5,
    uptime_percentage DECIMAL(5,2),
    availability_sla DECIMAL(5,2),
    sla_compliance BOOLEAN DEFAULT TRUE,
    maintenance_mode BOOLEAN DEFAULT FALSE,
    maintenance_window_start TIMESTAMP,
    maintenance_window_end TIMESTAMP,
    alert_enabled BOOLEAN DEFAULT TRUE,
    alert_threshold INTEGER DEFAULT 3,
    alert_count INTEGER DEFAULT 0,
    last_alert_at TIMESTAMP,
    auto_healing_enabled BOOLEAN DEFAULT FALSE,
    auto_healing_attempts INTEGER DEFAULT 0,
    max_auto_healing_attempts INTEGER DEFAULT 3,
    last_auto_healing_at TIMESTAMP,
    tags TEXT,
    labels TEXT,
    custom_metrics TEXT,
    business_impact VARCHAR(50) CHECK (business_impact IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    priority VARCHAR(10) CHECK (priority IN ('P1', 'P2', 'P3', 'P4')),
    escalation_policy VARCHAR(255),
    runbook_url TEXT,
    dashboard_url TEXT,
    active BOOLEAN DEFAULT TRUE,
    archived BOOLEAN DEFAULT FALSE,
    archived_at TIMESTAMP,
    archived_by VARCHAR(255),
    metadata TEXT,
    external_id VARCHAR(255),
    external_system VARCHAR(255),
    integration_data TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_checked_at TIMESTAMP,
    last_success_at TIMESTAMP,
    last_failure_at TIMESTAMP,
    status_changed_at TIMESTAMP,
    previous_status VARCHAR(50)
);

-- Create indexes for alerts table
CREATE INDEX idx_alerts_alert_id ON alerts(alert_id);
CREATE INDEX idx_alerts_service ON alerts(service);
CREATE INDEX idx_alerts_status ON alerts(status);
CREATE INDEX idx_alerts_severity ON alerts(severity);
CREATE INDEX idx_alerts_type ON alerts(type);
CREATE INDEX idx_alerts_environment ON alerts(environment);
CREATE INDEX idx_alerts_active ON alerts(active);
CREATE INDEX idx_alerts_archived ON alerts(archived);
CREATE INDEX idx_alerts_created_at ON alerts(created_at);
CREATE INDEX idx_alerts_updated_at ON alerts(updated_at);
CREATE INDEX idx_alerts_resolved_at ON alerts(resolved_at);
CREATE INDEX idx_alerts_escalated_at ON alerts(escalated_at);
CREATE INDEX idx_alerts_assigned_to ON alerts(assigned_to);
CREATE INDEX idx_alerts_resolved_by ON alerts(resolved_by);
CREATE INDEX idx_alerts_correlation_id ON alerts(correlation_id);
CREATE INDEX idx_alerts_parent_alert_id ON alerts(parent_alert_id);
CREATE INDEX idx_alerts_sla_deadline ON alerts(sla_deadline);
CREATE INDEX idx_alerts_sla_breached ON alerts(sla_breached);
CREATE INDEX idx_alerts_auto_resolved ON alerts(auto_resolved);
CREATE INDEX idx_alerts_notification_sent ON alerts(notification_sent);
CREATE INDEX idx_alerts_service_status ON alerts(service, status);
CREATE INDEX idx_alerts_service_severity ON alerts(service, severity);
CREATE INDEX idx_alerts_active_status ON alerts(active, status);
CREATE INDEX idx_alerts_active_severity ON alerts(active, severity);
CREATE INDEX idx_alerts_escalation_level ON alerts(escalation_level);
CREATE INDEX idx_alerts_host ON alerts(host);
CREATE INDEX idx_alerts_metric_name ON alerts(metric_name);

-- Create indexes for metrics table
CREATE INDEX idx_metrics_metric_id ON metrics(metric_id);
CREATE INDEX idx_metrics_metric_name ON metrics(metric_name);
CREATE INDEX idx_metrics_service ON metrics(service);
CREATE INDEX idx_metrics_environment ON metrics(environment);
CREATE INDEX idx_metrics_category ON metrics(category);
CREATE INDEX idx_metrics_active ON metrics(active);
CREATE INDEX idx_metrics_archived ON metrics(archived);
CREATE INDEX idx_metrics_created_at ON metrics(created_at);
CREATE INDEX idx_metrics_updated_at ON metrics(updated_at);
CREATE INDEX idx_metrics_measured_at ON metrics(measured_at);
CREATE INDEX idx_metrics_processed_at ON metrics(processed_at);
CREATE INDEX idx_metrics_host ON metrics(host);
CREATE INDEX idx_metrics_instance ON metrics(instance);
CREATE INDEX idx_metrics_namespace ON metrics(namespace);
CREATE INDEX idx_metrics_source ON metrics(source);
CREATE INDEX idx_metrics_collector ON metrics(collector);
CREATE INDEX idx_metrics_metric_type ON metrics(metric_type);
CREATE INDEX idx_metrics_is_key_metric ON metrics(is_key_metric);
CREATE INDEX idx_metrics_is_sla_metric ON metrics(is_sla_metric);
CREATE INDEX idx_metrics_alert_enabled ON metrics(alert_enabled);
CREATE INDEX idx_metrics_business_impact ON metrics(business_impact);
CREATE INDEX idx_metrics_validation_status ON metrics(validation_status);
CREATE INDEX idx_metrics_service_metric_name ON metrics(service, metric_name);
CREATE INDEX idx_metrics_service_category ON metrics(service, category);
CREATE INDEX idx_metrics_active_service ON metrics(active, service);
CREATE INDEX idx_metrics_active_key_metric ON metrics(active, is_key_metric);
CREATE INDEX idx_metrics_active_sla_metric ON metrics(active, is_sla_metric);
CREATE INDEX idx_metrics_dashboard_id ON metrics(dashboard_id);
CREATE INDEX idx_metrics_alert_rule_id ON metrics(alert_rule_id);
CREATE INDEX idx_metrics_value ON metrics(value);
CREATE INDEX idx_metrics_threshold_warning ON metrics(threshold_warning);
CREATE INDEX idx_metrics_threshold_critical ON metrics(threshold_critical);

-- Create indexes for service_health table
CREATE INDEX idx_service_health_health_check_id ON service_health(health_check_id);
CREATE INDEX idx_service_health_service_name ON service_health(service_name);
CREATE INDEX idx_service_health_status ON service_health(status);
CREATE INDEX idx_service_health_environment ON service_health(environment);
CREATE INDEX idx_service_health_active ON service_health(active);
CREATE INDEX idx_service_health_archived ON service_health(archived);
CREATE INDEX idx_service_health_created_at ON service_health(created_at);
CREATE INDEX idx_service_health_updated_at ON service_health(updated_at);
CREATE INDEX idx_service_health_last_checked_at ON service_health(last_checked_at);
CREATE INDEX idx_service_health_last_success_at ON service_health(last_success_at);
CREATE INDEX idx_service_health_last_failure_at ON service_health(last_failure_at);
CREATE INDEX idx_service_health_host ON service_health(host);
CREATE INDEX idx_service_health_region ON service_health(region);
CREATE INDEX idx_service_health_instance_id ON service_health(instance_id);
CREATE INDEX idx_service_health_deployment_id ON service_health(deployment_id);
CREATE INDEX idx_service_health_version ON service_health(version);
CREATE INDEX idx_service_health_check_type ON service_health(check_type);
CREATE INDEX idx_service_health_health_score ON service_health(health_score);
CREATE INDEX idx_service_health_response_time ON service_health(response_time);
CREATE INDEX idx_service_health_error_rate ON service_health(error_rate);
CREATE INDEX idx_service_health_uptime_percentage ON service_health(uptime_percentage);
CREATE INDEX idx_service_health_consecutive_failures ON service_health(consecutive_failures);
CREATE INDEX idx_service_health_sla_compliance ON service_health(sla_compliance);
CREATE INDEX idx_service_health_maintenance_mode ON service_health(maintenance_mode);
CREATE INDEX idx_service_health_alert_enabled ON service_health(alert_enabled);
CREATE INDEX idx_service_health_auto_healing_enabled ON service_health(auto_healing_enabled);
CREATE INDEX idx_service_health_business_impact ON service_health(business_impact);
CREATE INDEX idx_service_health_priority ON service_health(priority);
CREATE INDEX idx_service_health_circuit_breaker_state ON service_health(circuit_breaker_state);
CREATE INDEX idx_service_health_service_status ON service_health(service_name, status);
CREATE INDEX idx_service_health_service_environment ON service_health(service_name, environment);
CREATE INDEX idx_service_health_active_service ON service_health(active, service_name);
CREATE INDEX idx_service_health_active_status ON service_health(active, status);

-- Create composite indexes for better query performance
CREATE INDEX idx_alerts_service_status_severity ON alerts(service, status, severity);
CREATE INDEX idx_alerts_active_status_severity ON alerts(active, status, severity);
CREATE INDEX idx_alerts_created_at_service ON alerts(created_at, service);
CREATE INDEX idx_alerts_escalated_at_service ON alerts(escalated_at, service);
CREATE INDEX idx_alerts_resolved_at_service ON alerts(resolved_at, service);

CREATE INDEX idx_metrics_service_metric_measured_at ON metrics(service, metric_name, measured_at);
CREATE INDEX idx_metrics_active_service_category ON metrics(active, service, category);
CREATE INDEX idx_metrics_measured_at_service ON metrics(measured_at, service);
CREATE INDEX idx_metrics_processed_at_service ON metrics(processed_at, service);

CREATE INDEX idx_service_health_service_env_status ON service_health(service_name, environment, status);
CREATE INDEX idx_service_health_active_service_env ON service_health(active, service_name, environment);
CREATE INDEX idx_service_health_last_checked_service ON service_health(last_checked_at, service_name);
CREATE INDEX idx_service_health_last_success_service ON service_health(last_success_at, service_name);

-- Create GIN indexes for text search
CREATE INDEX idx_alerts_tags_gin ON alerts USING gin(to_tsvector('english', tags));
CREATE INDEX idx_alerts_labels_gin ON alerts USING gin(to_tsvector('english', labels));
CREATE INDEX idx_alerts_description_gin ON alerts USING gin(to_tsvector('english', description));

CREATE INDEX idx_metrics_tags_gin ON metrics USING gin(to_tsvector('english', tags));
CREATE INDEX idx_metrics_labels_gin ON metrics USING gin(to_tsvector('english', labels));
CREATE INDEX idx_metrics_description_gin ON metrics USING gin(to_tsvector('english', description));

CREATE INDEX idx_service_health_tags_gin ON service_health USING gin(to_tsvector('english', tags));
CREATE INDEX idx_service_health_labels_gin ON service_health USING gin(to_tsvector('english', labels));

-- Create partial indexes for active records
CREATE INDEX idx_alerts_active_true ON alerts(service, status, severity) WHERE active = true;
CREATE INDEX idx_metrics_active_true ON metrics(service, metric_name, measured_at) WHERE active = true;
CREATE INDEX idx_service_health_active_true ON service_health(service_name, status, last_checked_at) WHERE active = true;

-- Create triggers for updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_alerts_updated_at BEFORE UPDATE ON alerts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_metrics_updated_at BEFORE UPDATE ON metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_health_updated_at BEFORE UPDATE ON service_health
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create views for commonly used queries
CREATE VIEW v_active_alerts AS
SELECT 
    alert_id,
    title,
    description,
    severity,
    service,
    type,
    status,
    priority,
    environment,
    assigned_to,
    created_at,
    updated_at,
    sla_deadline,
    sla_breached,
    escalation_level,
    correlation_id
FROM alerts 
WHERE active = true;

CREATE VIEW v_active_metrics AS
SELECT 
    metric_id,
    metric_name,
    metric_type,
    value,
    unit,
    service,
    environment,
    category,
    is_key_metric,
    is_sla_metric,
    alert_enabled,
    business_impact,
    measured_at,
    threshold_warning,
    threshold_critical
FROM metrics 
WHERE active = true;

CREATE VIEW v_active_service_health AS
SELECT 
    health_check_id,
    service_name,
    service_url,
    status,
    health_score,
    response_time,
    environment,
    uptime_percentage,
    error_rate,
    consecutive_failures,
    sla_compliance,
    business_impact,
    priority,
    last_checked_at,
    last_success_at,
    last_failure_at
FROM service_health 
WHERE active = true;

-- Create materialized views for performance
CREATE MATERIALIZED VIEW mv_alert_summary AS
SELECT 
    service,
    environment,
    severity,
    status,
    COUNT(*) as alert_count,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_count,
    COUNT(CASE WHEN status = 'RESOLVED' THEN 1 END) as resolved_count,
    COUNT(CASE WHEN sla_breached = true THEN 1 END) as sla_breached_count,
    AVG(CASE WHEN resolved_at IS NOT NULL THEN EXTRACT(EPOCH FROM (resolved_at - created_at)) END) as avg_resolution_time,
    MIN(created_at) as first_alert_time,
    MAX(created_at) as last_alert_time
FROM alerts 
WHERE active = true
GROUP BY service, environment, severity, status;

CREATE MATERIALIZED VIEW mv_metric_summary AS
SELECT 
    service,
    environment,
    category,
    metric_type,
    COUNT(*) as metric_count,
    COUNT(CASE WHEN is_key_metric = true THEN 1 END) as key_metric_count,
    COUNT(CASE WHEN is_sla_metric = true THEN 1 END) as sla_metric_count,
    COUNT(CASE WHEN alert_enabled = true THEN 1 END) as alert_enabled_count,
    AVG(data_quality_score) as avg_data_quality_score,
    MIN(measured_at) as first_measurement_time,
    MAX(measured_at) as last_measurement_time
FROM metrics 
WHERE active = true
GROUP BY service, environment, category, metric_type;

CREATE MATERIALIZED VIEW mv_service_health_summary AS
SELECT 
    service_name,
    environment,
    status,
    COUNT(*) as check_count,
    AVG(health_score) as avg_health_score,
    AVG(response_time) as avg_response_time,
    AVG(error_rate) as avg_error_rate,
    AVG(uptime_percentage) as avg_uptime_percentage,
    COUNT(CASE WHEN sla_compliance = true THEN 1 END) as sla_compliant_count,
    COUNT(CASE WHEN consecutive_failures > 0 THEN 1 END) as failure_count,
    MIN(last_checked_at) as first_check_time,
    MAX(last_checked_at) as last_check_time
FROM service_health 
WHERE active = true
GROUP BY service_name, environment, status;

-- Create indexes on materialized views
CREATE INDEX idx_mv_alert_summary_service ON mv_alert_summary(service);
CREATE INDEX idx_mv_metric_summary_service ON mv_metric_summary(service);
CREATE INDEX idx_mv_service_health_summary_service ON mv_service_health_summary(service_name);

-- Create function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_monitoring_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW mv_alert_summary;
    REFRESH MATERIALIZED VIEW mv_metric_summary;
    REFRESH MATERIALIZED VIEW mv_service_health_summary;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL PROCEDURES IN SCHEMA public TO postgres;

-- Insert sample data for testing
INSERT INTO alerts (alert_id, title, description, severity, service, type, status, environment, metric_name, metric_value, threshold, condition)
VALUES 
    ('ALERT-SAMPLE-001', 'High CPU Usage', 'CPU usage exceeded 80% threshold', 'HIGH', 'api-gateway', 'PERFORMANCE', 'ACTIVE', 'PROD', 'cpu_usage', 85.5, 80.0, 'GREATER_THAN'),
    ('ALERT-SAMPLE-002', 'Database Connection Pool Full', 'Database connection pool is at capacity', 'CRITICAL', 'storage-service', 'SYSTEM', 'ACTIVE', 'PROD', 'db_connections', 100.0, 90.0, 'GREATER_THAN'),
    ('ALERT-SAMPLE-003', 'High Memory Usage', 'Memory usage exceeded 85% threshold', 'MEDIUM', 'user-service', 'PERFORMANCE', 'RESOLVED', 'PROD', 'memory_usage', 87.2, 85.0, 'GREATER_THAN');

INSERT INTO metrics (metric_id, metric_name, metric_type, value, unit, service, environment, category, is_key_metric, alert_enabled, business_impact)
VALUES 
    ('METRIC-SAMPLE-001', 'cpu_usage', 'GAUGE', 75.5, 'percent', 'api-gateway', 'PROD', 'SYSTEM', true, true, 'HIGH'),
    ('METRIC-SAMPLE-002', 'memory_usage', 'GAUGE', 82.3, 'percent', 'api-gateway', 'PROD', 'SYSTEM', true, true, 'HIGH'),
    ('METRIC-SAMPLE-003', 'request_count', 'COUNTER', 12543.0, 'count', 'api-gateway', 'PROD', 'APPLICATION', true, false, 'MEDIUM'),
    ('METRIC-SAMPLE-004', 'response_time', 'HISTOGRAM', 125.7, 'milliseconds', 'api-gateway', 'PROD', 'APPLICATION', true, true, 'HIGH'),
    ('METRIC-SAMPLE-005', 'error_rate', 'GAUGE', 2.1, 'percent', 'api-gateway', 'PROD', 'APPLICATION', true, true, 'CRITICAL');

INSERT INTO service_health (health_check_id, service_name, service_url, status, health_score, response_time, environment, uptime_percentage, error_rate, business_impact, priority, check_type, check_method)
VALUES 
    ('HEALTH-SAMPLE-001', 'api-gateway', 'http://localhost:8080', 'UP', 0.95, 150, 'PROD', 99.8, 0.5, 'CRITICAL', 'P1', 'HTTP', 'GET'),
    ('HEALTH-SAMPLE-002', 'storage-service', 'http://localhost:8082', 'UP', 0.92, 200, 'PROD', 99.5, 1.2, 'HIGH', 'P2', 'HTTP', 'GET'),
    ('HEALTH-SAMPLE-003', 'user-service', 'http://localhost:8083', 'UP', 0.88, 180, 'PROD', 99.2, 2.1, 'MEDIUM', 'P3', 'HTTP', 'GET'),
    ('HEALTH-SAMPLE-004', 'security-service', 'http://localhost:8084', 'UP', 0.93, 120, 'PROD', 99.7, 0.8, 'CRITICAL', 'P1', 'HTTP', 'GET'),
    ('HEALTH-SAMPLE-005', 'monitoring-service', 'http://localhost:8085', 'UP', 0.96, 100, 'PROD', 99.9, 0.2, 'HIGH', 'P2', 'HTTP', 'GET');

-- Refresh materialized views
SELECT refresh_monitoring_views();

-- Create scheduled job to refresh materialized views (requires pg_cron extension)
-- SELECT cron.schedule('refresh-monitoring-views', '*/5 * * * *', 'SELECT refresh_monitoring_views();');
