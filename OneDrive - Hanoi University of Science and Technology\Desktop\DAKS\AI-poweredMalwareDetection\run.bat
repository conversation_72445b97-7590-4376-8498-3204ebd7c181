@echo off
echo AI-Powered Malware Detection System
echo ===================================

:menu
echo.
echo Choose an option:
echo 1. Setup environment
echo 2. Train models
echo 3. Analyze file
echo 4. Start monitoring
echo 5. Run demo
echo 6. Run tests
echo 7. Exit
echo.

set /p choice=Enter your choice (1-7): 

if "%choice%"=="1" goto setup
if "%choice%"=="2" goto train
if "%choice%"=="3" goto analyze
if "%choice%"=="4" goto monitor
if "%choice%"=="5" goto demo
if "%choice%"=="6" goto test
if "%choice%"=="7" goto exit

echo Invalid choice. Please try again.
goto menu

:setup
echo Setting up environment...
python setup.py
pause
goto menu

:train
echo Training models...
python main.py --train
pause
goto menu

:analyze
set /p filepath=Enter file path to analyze: 
python main.py --file "%filepath%"
pause
goto menu

:monitor
echo Starting real-time monitoring...
echo Press Ctrl+C to stop monitoring
python main.py --monitor
pause
goto menu

:demo
echo Running demo...
python demo.py
pause
goto menu

:test
echo Running tests...
python tests/test_malware_detection.py
pause
goto menu

:exit
echo Goodbye!
exit
