package com.securecloudstorage.security.service;

import com.securecloudstorage.security.dto.SecurityEventRequest;
import com.securecloudstorage.security.dto.SecurityEventResponse;
import com.securecloudstorage.security.dto.SecurityStatsResponse;
import com.securecloudstorage.security.entity.SecurityEvent;
import com.securecloudstorage.security.entity.SecurityAlert;
import com.securecloudstorage.security.repository.SecurityEventRepository;
import com.securecloudstorage.security.repository.SecurityAlertRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Security Audit Service
 * Dịch vụ xử lý audit logging và security analytics
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SecurityAuditService {

    private final SecurityEventRepository eventRepository;
    private final SecurityAlertRepository alertRepository;
    private final ThreatDetectionService threatDetectionService;
    private final NotificationService notificationService;
    private final SecurityTokenService tokenService;

    /**
     * Ghi lại security event
     */
    @Transactional
    public SecurityEventResponse recordEvent(SecurityEventRequest request) {
        try {
            // Validate request
            validateEventRequest(request);
            
            // Create security event
            SecurityEvent event = createSecurityEvent(request);
            
            // Enrich event với threat intelligence
            enrichEventWithThreatIntelligence(event);
            
            // Save event
            event = eventRepository.save(event);
            
            // Check if event requires alert
            checkForAlerts(event);
            
            // Check for threat patterns
            checkThreatPatterns(event);
            
            // Convert to response
            SecurityEventResponse response = convertToResponse(event);
            
            log.info("Security event recorded: {} - {} - {}", 
                event.getEventType(), event.getSeverity(), event.getDescription());
            
            return response;
            
        } catch (Exception e) {
            log.error("Error recording security event: ", e);
            throw new RuntimeException("Failed to record security event", e);
        }
    }

    /**
     * Lấy danh sách security events
     */
    public Page<SecurityEventResponse> getEvents(Pageable pageable, String severity, 
                                                String eventType, String userId) {
        try {
            Page<SecurityEvent> events;
            
            if (severity != null && eventType != null && userId != null) {
                events = eventRepository.findBySeverityAndEventTypeAndUserId(
                    severity, eventType, userId, pageable);
            } else if (severity != null && eventType != null) {
                events = eventRepository.findBySeverityAndEventType(
                    severity, eventType, pageable);
            } else if (severity != null) {
                events = eventRepository.findBySeverity(severity, pageable);
            } else if (eventType != null) {
                events = eventRepository.findByEventType(eventType, pageable);
            } else if (userId != null) {
                events = eventRepository.findByUserId(userId, pageable);
            } else {
                events = eventRepository.findAll(pageable);
            }
            
            return events.map(this::convertToResponse);
            
        } catch (Exception e) {
            log.error("Error getting security events: ", e);
            throw new RuntimeException("Failed to get security events", e);
        }
    }

    /**
     * Lấy thống kê security
     */
    public SecurityStatsResponse getSecurityStats() {
        try {
            SecurityStatsResponse stats = new SecurityStatsResponse();
            
            // Tổng quan
            stats.setTotalEvents(eventRepository.count());
            stats.setTotalUsers(eventRepository.countDistinctUsers());
            stats.setTotalThreats(eventRepository.countByThreatLevelNotNull());
            stats.setTotalBlockedIps(threatDetectionService.getBlockedIpCount());
            
            // Theo thời gian
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime todayStart = now.toLocalDate().atStartOfDay();
            LocalDateTime weekStart = now.minusDays(7);
            LocalDateTime monthStart = now.minusDays(30);
            
            stats.setEventsToday(eventRepository.countByTimestampAfter(todayStart));
            stats.setEventsThisWeek(eventRepository.countByTimestampAfter(weekStart));
            stats.setEventsThisMonth(eventRepository.countByTimestampAfter(monthStart));
            
            // Theo severity
            stats.setCriticalEvents(eventRepository.countBySeverity("CRITICAL"));
            stats.setHighSeverityEvents(eventRepository.countBySeverity("HIGH"));
            stats.setMediumSeverityEvents(eventRepository.countBySeverity("MEDIUM"));
            stats.setLowSeverityEvents(eventRepository.countBySeverity("LOW"));
            
            // Thống kê theo loại
            stats.setEventTypeStats(getEventTypeStats());
            stats.setSeverityStats(getSeverityStats());
            stats.setSourceStats(getSourceStats());
            
            // Top threats
            stats.setTopThreats(getTopThreats());
            stats.setTopUsers(getTopUsers());
            stats.setTopIps(getTopIps());
            
            // Alerts
            stats.setTotalAlerts(alertRepository.count());
            stats.setOpenAlerts(alertRepository.countByStatus("OPEN"));
            stats.setClosedAlerts(alertRepository.countByStatus("CLOSED"));
            
            // Health metrics
            stats.setSystemHealth("HEALTHY");
            stats.setCpuUsage(getCurrentCpuUsage());
            stats.setMemoryUsage(getCurrentMemoryUsage());
            
            return stats;
            
        } catch (Exception e) {
            log.error("Error getting security stats: ", e);
            throw new RuntimeException("Failed to get security stats", e);
        }
    }

    /**
     * Lấy active alerts
     */
    public List<Map<String, Object>> getActiveAlerts() {
        try {
            List<SecurityAlert> alerts = alertRepository.findByStatusOrderByCreatedAtDesc("OPEN");
            
            return alerts.stream()
                .map(alert -> {
                    Map<String, Object> alertMap = new HashMap<>();
                    alertMap.put("id", alert.getId());
                    alertMap.put("title", alert.getTitle());
                    alertMap.put("description", alert.getDescription());
                    alertMap.put("severity", alert.getSeverity());
                    alertMap.put("alertType", alert.getAlertType());
                    alertMap.put("createdAt", alert.getCreatedAt());
                    alertMap.put("priority", alert.getPriority());
                    alertMap.put("source", alert.getSource());
                    return alertMap;
                })
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("Error getting active alerts: ", e);
            throw new RuntimeException("Failed to get active alerts", e);
        }
    }

    /**
     * Lấy user security profile
     */
    public Map<String, Object> getUserSecurityProfile(String userId) {
        try {
            Map<String, Object> profile = new HashMap<>();
            
            // Basic stats
            profile.put("userId", userId);
            profile.put("totalEvents", eventRepository.countByUserId(userId));
            profile.put("riskScore", calculateUserRiskScore(userId));
            
            // Recent activity
            List<SecurityEvent> recentEvents = eventRepository
                .findTop10ByUserIdOrderByTimestampDesc(userId);
            profile.put("recentEvents", recentEvents.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList()));
            
            // Threat summary
            profile.put("threatEvents", eventRepository.countByUserIdAndThreatLevelNotNull(userId));
            profile.put("loginAttempts", eventRepository.countByUserIdAndEventType(userId, "LOGIN"));
            profile.put("failedLogins", eventRepository.countByUserIdAndEventTypeAndIsSuccessful(
                userId, "LOGIN", false));
            
            // Geolocation summary
            profile.put("locations", getUserLocations(userId));
            profile.put("devices", getUserDevices(userId));
            
            return profile;
            
        } catch (Exception e) {
            log.error("Error getting user security profile: ", e);
            throw new RuntimeException("Failed to get user security profile", e);
        }
    }

    /**
     * Validate security token
     */
    public Map<String, Object> validateSecurityToken(String token) {
        try {
            return tokenService.validateToken(token);
        } catch (Exception e) {
            log.error("Error validating security token: ", e);
            throw new RuntimeException("Failed to validate security token", e);
        }
    }

    // Private helper methods

    private void validateEventRequest(SecurityEventRequest request) {
        if (request.getEventType() == null || request.getEventType().isEmpty()) {
            throw new IllegalArgumentException("Event type is required");
        }
        if (request.getSeverity() == null || request.getSeverity().isEmpty()) {
            throw new IllegalArgumentException("Severity is required");
        }
        if (request.getTimestamp() == null) {
            request.setTimestamp(LocalDateTime.now());
        }
    }

    private SecurityEvent createSecurityEvent(SecurityEventRequest request) {
        SecurityEvent event = new SecurityEvent();
        
        event.setEventType(request.getEventType());
        event.setSeverity(request.getSeverity());
        event.setDescription(request.getDescription());
        event.setUserId(request.getUserId());
        event.setResourceId(request.getResourceId());
        event.setIpAddress(request.getIpAddress());
        event.setUserAgent(request.getUserAgent());
        event.setSessionId(request.getSessionId());
        event.setTimestamp(request.getTimestamp());
        event.setSource(request.getSource());
        event.setAction(request.getAction());
        event.setTargetEntity(request.getTargetEntity());
        event.setTargetEntityId(request.getTargetEntityId());
        event.setIsSuccessful(request.getIsSuccessful());
        event.setFailureReason(request.getFailureReason());
        event.setGeolocation(request.getGeolocation());
        event.setDeviceInfo(request.getDeviceInfo());
        event.setApplicationVersion(request.getApplicationVersion());
        event.setRiskScore(request.getRiskScore());
        event.setThreatLevel(request.getThreatLevel());
        event.setCorrelationId(request.getCorrelationId());
        event.setCustomData(request.getCustomData());
        event.setCreatedAt(LocalDateTime.now());
        event.setStatus("NEW");
        event.setIsProcessed(false);
        
        return event;
    }

    private void enrichEventWithThreatIntelligence(SecurityEvent event) {
        if (event.getIpAddress() != null) {
            Map<String, Object> threatIntel = threatDetectionService.analyzeIp(event.getIpAddress());
            if (threatIntel.containsKey("isThreat") && (Boolean) threatIntel.get("isThreat")) {
                event.setThreatLevel("HIGH");
                event.setRiskScore("85");
            }
        }
    }

    private void checkForAlerts(SecurityEvent event) {
        // Check if event meets alert criteria
        if (shouldCreateAlert(event)) {
            createAlert(event);
        }
    }

    private boolean shouldCreateAlert(SecurityEvent event) {
        return "CRITICAL".equals(event.getSeverity()) || 
               "HIGH".equals(event.getThreatLevel()) ||
               isAnomalousActivity(event);
    }

    private boolean isAnomalousActivity(SecurityEvent event) {
        // Check for anomalous patterns
        if (event.getUserId() != null) {
            long recentFailures = eventRepository.countByUserIdAndEventTypeAndIsSuccessfulAndTimestampAfter(
                event.getUserId(), event.getEventType(), false, 
                LocalDateTime.now().minusDays(1));
            return recentFailures > 5;
        }
        return false;
    }

    private void createAlert(SecurityEvent event) {
        SecurityAlert alert = new SecurityAlert();
        alert.setTitle("Security Alert: " + event.getEventType());
        alert.setDescription("Suspicious activity detected: " + event.getDescription());
        alert.setSeverity(event.getSeverity());
        alert.setAlertType("SECURITY_EVENT");
        alert.setEventId(event.getId());
        alert.setStatus("OPEN");
        alert.setCreatedAt(LocalDateTime.now());
        alert.setSource(event.getSource());
        alert.setPriority(getSeverityPriority(event.getSeverity()));
        
        alertRepository.save(alert);
        
        // Send notification
        notificationService.sendAlert(alert);
    }

    private Integer getSeverityPriority(String severity) {
        switch (severity) {
            case "CRITICAL": return 1;
            case "HIGH": return 2;
            case "MEDIUM": return 3;
            case "LOW": return 4;
            default: return 5;
        }
    }

    private void checkThreatPatterns(SecurityEvent event) {
        // Implement threat pattern detection
        threatDetectionService.analyzeThreatPatterns(event);
    }

    private SecurityEventResponse convertToResponse(SecurityEvent event) {
        SecurityEventResponse response = new SecurityEventResponse();
        
        response.setId(event.getId());
        response.setEventType(event.getEventType());
        response.setSeverity(event.getSeverity());
        response.setDescription(event.getDescription());
        response.setUserId(event.getUserId());
        response.setResourceId(event.getResourceId());
        response.setIpAddress(event.getIpAddress());
        response.setUserAgent(event.getUserAgent());
        response.setSessionId(event.getSessionId());
        response.setTimestamp(event.getTimestamp());
        response.setSource(event.getSource());
        response.setAction(event.getAction());
        response.setTargetEntity(event.getTargetEntity());
        response.setTargetEntityId(event.getTargetEntityId());
        response.setIsSuccessful(event.getIsSuccessful());
        response.setFailureReason(event.getFailureReason());
        response.setGeolocation(event.getGeolocation());
        response.setDeviceInfo(event.getDeviceInfo());
        response.setApplicationVersion(event.getApplicationVersion());
        response.setRiskScore(event.getRiskScore());
        response.setThreatLevel(event.getThreatLevel());
        response.setCorrelationId(event.getCorrelationId());
        response.setCustomData(event.getCustomData());
        response.setCreatedAt(event.getCreatedAt());
        response.setUpdatedAt(event.getUpdatedAt());
        response.setStatus(event.getStatus());
        response.setIsProcessed(event.getIsProcessed());
        response.setProcessedBy(event.getProcessedBy());
        response.setProcessedAt(event.getProcessedAt());
        
        return response;
    }

    private Map<String, Long> getEventTypeStats() {
        List<Object[]> results = eventRepository.getEventTypeStats();
        return results.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],
                row -> (Long) row[1]
            ));
    }

    private Map<String, Long> getSeverityStats() {
        List<Object[]> results = eventRepository.getSeverityStats();
        return results.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],
                row -> (Long) row[1]
            ));
    }

    private Map<String, Long> getSourceStats() {
        List<Object[]> results = eventRepository.getSourceStats();
        return results.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],
                row -> (Long) row[1]
            ));
    }

    private List<Map<String, Object>> getTopThreats() {
        List<SecurityEvent> threats = eventRepository.findTop10ByThreatLevelNotNullOrderByTimestampDesc();
        return threats.stream()
            .map(event -> {
                Map<String, Object> threat = new HashMap<>();
                threat.put("eventType", event.getEventType());
                threat.put("threatLevel", event.getThreatLevel());
                threat.put("ipAddress", event.getIpAddress());
                threat.put("timestamp", event.getTimestamp());
                threat.put("riskScore", event.getRiskScore());
                return threat;
            })
            .collect(Collectors.toList());
    }

    private List<Map<String, Object>> getTopUsers() {
        List<Object[]> results = eventRepository.getTopUsersByEventCount();
        return results.stream()
            .map(row -> {
                Map<String, Object> user = new HashMap<>();
                user.put("userId", row[0]);
                user.put("eventCount", row[1]);
                return user;
            })
            .collect(Collectors.toList());
    }

    private List<Map<String, Object>> getTopIps() {
        List<Object[]> results = eventRepository.getTopIpsByEventCount();
        return results.stream()
            .map(row -> {
                Map<String, Object> ip = new HashMap<>();
                ip.put("ipAddress", row[0]);
                ip.put("eventCount", row[1]);
                return ip;
            })
            .collect(Collectors.toList());
    }

    private Double getCurrentCpuUsage() {
        // Implement CPU usage monitoring
        return 65.0;
    }

    private Double getCurrentMemoryUsage() {
        // Implement memory usage monitoring
        return 78.0;
    }

    private String calculateUserRiskScore(String userId) {
        // Implement user risk scoring
        long threatEvents = eventRepository.countByUserIdAndThreatLevelNotNull(userId);
        long totalEvents = eventRepository.countByUserId(userId);
        
        if (totalEvents == 0) return "0";
        
        double riskScore = (double) threatEvents / totalEvents * 100;
        return String.valueOf(Math.min(100, (int) riskScore));
    }

    private List<String> getUserLocations(String userId) {
        return eventRepository.findDistinctGeolocationsByUserId(userId);
    }

    private List<String> getUserDevices(String userId) {
        return eventRepository.findDistinctDeviceInfoByUserId(userId);
    }
}
