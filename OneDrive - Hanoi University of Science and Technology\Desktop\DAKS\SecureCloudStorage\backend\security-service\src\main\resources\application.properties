spring.application.name=security-service
server.port=8084

# Database Configuration
spring.datasource.url=********************************************
spring.datasource.username=postgres
spring.datasource.password=password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Logging Configuration
logging.level.com.securecloudstorage=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Security Configuration
security.jwt.secret=mySecretKeyForSecurityService2024
security.jwt.expiration=86400

# Eureka Configuration (if using service discovery)
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
eureka.instance.prefer-ip-address=true

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# Application specific configuration
app.security.threat-intelligence.cache-ttl=3600
app.security.threat-intelligence.max-cache-size=10000
app.security.ip-blocking.default-block-duration=86400
app.security.ip-blocking.max-failed-attempts=10
app.security.alert.notification.enabled=true
app.security.alert.notification.channels=email,slack

# Thread pool configuration
spring.task.execution.pool.core-size=10
spring.task.execution.pool.max-size=20
spring.task.execution.pool.queue-capacity=100
