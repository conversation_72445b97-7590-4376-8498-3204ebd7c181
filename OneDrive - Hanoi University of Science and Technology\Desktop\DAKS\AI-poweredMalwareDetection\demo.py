"""
Demo script for AI-Powered Malware Detection System
"""

import os
import sys
import tempfile
import time
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.analysis.static_analyzer import StaticAnalyzer
from src.models.ensemble_model import EnsembleDetector
from src.utils.config import load_config
from src.utils.logger import setup_logging
from src.data_preprocessing.preprocessor import generate_sample_data

def run_demo():
    """Run a complete demo of the malware detection system"""
    
    print("=" * 60)
    print("AI-POWERED MALWARE DETECTION SYSTEM - DEMO")
    print("=" * 60)
    
    # Setup logging
    setup_logging(verbose=True)
    
    # Load configuration
    config = load_config('config/config.yaml')
    
    print("\n1. SYSTEM INITIALIZATION")
    print("-" * 30)
    
    # Initialize components
    static_analyzer = StaticAnalyzer(config)
    detector = EnsembleDetector(config)
    
    print("✓ Static analyzer initialized")
    print("✓ Ensemble detector initialized")
    
    print("\n2. GENERATING SAMPLE DATA")
    print("-" * 30)
    
    # Generate sample training data
    if not os.path.exists('data/training_data.csv'):
        generate_sample_data('data/training_data.csv', num_samples=1000)
        print("✓ Sample training data generated")
    else:
        print("✓ Training data already exists")
    
    print("\n3. TRAINING MODELS")
    print("-" * 30)
    
    # Train models
    try:
        detector.train('data/training_data.csv')
        print("✓ Models trained successfully")
    except Exception as e:
        print(f"✗ Training failed: {str(e)}")
        print("Note: Some dependencies might be missing for full training")
    
    print("\n4. CREATING TEST FILES")
    print("-" * 30)
    
    # Create test files
    test_files = create_test_files()
    
    print("\n5. ANALYZING TEST FILES")
    print("-" * 30)
    
    # Analyze each test file
    for file_path, file_type in test_files:
        print(f"\nAnalyzing {file_type} file: {file_path}")
        
        try:
            # Static analysis
            static_features = static_analyzer.analyze(file_path)
            print(f"  Static features extracted: {len(static_features)} features")
            
            # Display key features
            key_features = ['file_size', 'file_entropy', 'string_count', 'yara_matches']
            for feature in key_features:
                if feature in static_features:
                    print(f"    {feature}: {static_features[feature]}")
            
            # Prediction (if models are trained)
            if detector.is_trained:
                result = detector.predict(file_path, static_features)
                if result:
                    print(f"  Prediction: {result['prediction']}")
                    print(f"  Confidence: {result['confidence']:.2f}")
                    
                    if result.get('threats'):
                        print(f"  Threats: {', '.join(result['threats'])}")
            else:
                print("  Prediction: Skipped (models not trained)")
                
        except Exception as e:
            print(f"  Error: {str(e)}")
    
    print("\n6. PERFORMANCE METRICS")
    print("-" * 30)
    
    # Display system information
    display_system_info()
    
    print("\n7. DEMONSTRATION COMPLETE")
    print("-" * 30)
    print("Demo completed successfully!")
    print("\nNext steps:")
    print("- Add real malware samples to data/samples/")
    print("- Update YARA rules in data/yara_rules/")
    print("- Train with larger datasets")
    print("- Configure real-time monitoring")
    
    # Cleanup
    cleanup_test_files(test_files)

def create_test_files():
    """Create test files for demonstration"""
    
    test_files = []
    
    # Create benign file
    benign_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt')
    benign_content = b"""
This is a benign test file.
It contains normal text content.
No suspicious strings or patterns.
"""
    benign_file.write(benign_content)
    benign_file.close()
    test_files.append((benign_file.name, 'benign'))
    
    # Create suspicious file
    suspicious_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt')
    suspicious_content = b"""
This file contains suspicious content.
CreateRemoteThread
WriteProcessMemory
VirtualAlloc
GetProcAddress
RegSetValueEx
http://malicious-site.com
InternetOpen
CreateMutex
"""
    suspicious_file.write(suspicious_content)
    suspicious_file.close()
    test_files.append((suspicious_file.name, 'suspicious'))
    
    # Create high-entropy file
    entropy_file = tempfile.NamedTemporaryFile(delete=False, suffix='.bin')
    entropy_content = os.urandom(1024)  # Random bytes (high entropy)
    entropy_file.write(entropy_content)
    entropy_file.close()
    test_files.append((entropy_file.name, 'high-entropy'))
    
    print(f"✓ Created {len(test_files)} test files")
    return test_files

def display_system_info():
    """Display system information"""
    
    import platform
    import psutil
    
    print(f"OS: {platform.system()} {platform.release()}")
    print(f"Python: {platform.python_version()}")
    print(f"CPU: {psutil.cpu_count()} cores")
    print(f"Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB")
    
    # Check installed packages
    try:
        import tensorflow as tf
        print(f"TensorFlow: {tf.__version__}")
    except ImportError:
        print("TensorFlow: Not installed")
    
    try:
        import sklearn
        print(f"scikit-learn: {sklearn.__version__}")
    except ImportError:
        print("scikit-learn: Not installed")

def cleanup_test_files(test_files):
    """Cleanup test files"""
    
    for file_path, _ in test_files:
        try:
            os.unlink(file_path)
        except OSError:
            pass

if __name__ == "__main__":
    run_demo()
