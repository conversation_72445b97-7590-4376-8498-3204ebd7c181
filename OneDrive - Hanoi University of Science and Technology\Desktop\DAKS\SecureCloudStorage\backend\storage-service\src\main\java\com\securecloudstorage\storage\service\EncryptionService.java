package com.securecloudstorage.storage.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * Encryption Service for file encryption/decryption
 */
@Service
@Slf4j
public class EncryptionService {

    @Value("${encryption.algorithm:AES}")
    private String algorithm;

    @Value("${encryption.key-size:256}")
    private int keySize;

    @Value("${encryption.secret-key:mySecretKey123456789012345678901234567890}")
    private String secretKey;

    private SecretKey getSecretKey() {
        // Use the first 32 bytes of the secret key for AES-256
        byte[] keyBytes = secretKey.getBytes();
        byte[] key = new byte[32]; // 256 bits
        System.arraycopy(keyBytes, 0, key, 0, Math.min(keyBytes.length, key.length));
        return new SecretKeySpec(key, algorithm);
    }

    /**
     * Encrypt byte array
     */
    public byte[] encrypt(byte[] data) {
        try {
            Cipher cipher = Cipher.getInstance(algorithm);
            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey());
            return cipher.doFinal(data);
        } catch (Exception e) {
            log.error("Error encrypting data: ", e);
            throw new RuntimeException("Encryption failed", e);
        }
    }

    /**
     * Decrypt byte array
     */
    public byte[] decrypt(byte[] encryptedData) {
        try {
            Cipher cipher = Cipher.getInstance(algorithm);
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey());
            return cipher.doFinal(encryptedData);
        } catch (Exception e) {
            log.error("Error decrypting data: ", e);
            throw new RuntimeException("Decryption failed", e);
        }
    }

    /**
     * Encrypt string
     */
    public String encrypt(String data) {
        byte[] encryptedData = encrypt(data.getBytes());
        return Base64.getEncoder().encodeToString(encryptedData);
    }

    /**
     * Decrypt string
     */
    public String decrypt(String encryptedData) {
        byte[] decodedData = Base64.getDecoder().decode(encryptedData);
        byte[] decryptedData = decrypt(decodedData);
        return new String(decryptedData);
    }

    /**
     * Generate random encryption key
     */
    public SecretKey generateKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(algorithm);
            keyGenerator.init(keySize);
            return keyGenerator.generateKey();
        } catch (NoSuchAlgorithmException e) {
            log.error("Error generating encryption key: ", e);
            throw new RuntimeException("Key generation failed", e);
        }
    }

    /**
     * Generate random encryption key as string
     */
    public String generateKeyString() {
        SecretKey key = generateKey();
        return Base64.getEncoder().encodeToString(key.getEncoded());
    }

    /**
     * Calculate checksum for data integrity
     */
    public String calculateChecksum(byte[] data) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(data);
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            log.error("Error calculating checksum: ", e);
            throw new RuntimeException("Checksum calculation failed", e);
        }
    }

    /**
     * Verify checksum
     */
    public boolean verifyChecksum(byte[] data, String expectedChecksum) {
        String actualChecksum = calculateChecksum(data);
        return actualChecksum.equals(expectedChecksum);
    }

    /**
     * Generate secure random token
     */
    public String generateSecureToken() {
        byte[] token = new byte[32];
        new SecureRandom().nextBytes(token);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(token);
    }
}
