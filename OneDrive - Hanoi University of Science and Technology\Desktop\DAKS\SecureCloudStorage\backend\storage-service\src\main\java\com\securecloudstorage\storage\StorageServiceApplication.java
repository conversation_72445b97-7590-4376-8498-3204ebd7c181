package com.securecloudstorage.storage;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Storage Service Application
 * Secure Cloud Storage with AI-Malware Detection
 */
@SpringBootApplication
@EnableAsync
@EnableTransactionManagement
@EnableConfigurationProperties
public class StorageServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(StorageServiceApplication.class, args);
    }
}
