version: '3.8'

services:
  # Redis for rate limiting
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  # MariaDB database
  mariadb:
    image: mariadb:10.11
    environment:
      MARIADB_ROOT_PASSWORD: root
      MARIADB_DATABASE: storagedb
      MARIADB_USER: storage_user
      MARIADB_PASSWORD: storage_password
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./mariadb/init:/docker-entrypoint-initdb.d
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    restart: unless-stopped

  # PostgreSQL database (for other services)
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: securecloudstorage
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - redis
      - storage-service
      - user-service
      - security-service
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./logs/gateway:/app/logs
    restart: unless-stopped

  # Storage Service
  storage-service:
    build:
      context: ./storage-service
      dockerfile: Dockerfile
    ports:
      - "8082:8082"
    depends_on:
      - mariadb
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=*************************************
      - SPRING_DATASOURCE_USERNAME=storage_user
      - SPRING_DATASOURCE_PASSWORD=storage_password
    volumes:
      - ./uploads:/app/uploads
      - ./quarantine:/app/quarantine
      - ./logs/storage:/app/logs
    restart: unless-stopped

  # User Service
  user-service:
    build:
      context: ./user-service
      dockerfile: Dockerfile
    ports:
      - "8083:8083"
    depends_on:
      - postgres
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=postgres
    volumes:
      - ./logs/user:/app/logs
    restart: unless-stopped

  # Security Service
  security-service:
    build:
      context: ./security-service
      dockerfile: Dockerfile
    ports:
      - "8084:8084"
    depends_on:
      - postgres
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=postgres
    volumes:
      - ./logs/security:/app/logs
    restart: unless-stopped

  # Monitoring Service
  monitoring-service:
    build:
      context: ./monitoring-service
      dockerfile: Dockerfile
    ports:
      - "8085:8085"
    depends_on:
      - postgres
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**************************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=postgres
    volumes:
      - ./logs/monitoring:/app/logs
    restart: unless-stopped

  # AI-Malware Detection Service (Python)
  ai-malware-service:
    build:
      context: ../AI-poweredMalwareDetection
      dockerfile: Dockerfile
    ports:
      - "8086:8086"
    volumes:
      - ../AI-poweredMalwareDetection:/app
      - ./logs/ai-malware:/app/logs
    environment:
      - FLASK_ENV=production
      - FLASK_PORT=8086
    restart: unless-stopped

  # Network IDS Service (Python)
  network-ids-service:
    build:
      context: ../NetworkIDS
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    volumes:
      - ../NetworkIDS:/app
      - ./logs/network-ids:/app/logs
    environment:
      - FLASK_ENV=production
      - FLASK_PORT=5000
    restart: unless-stopped
    privileged: true
    network_mode: host

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    restart: unless-stopped

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api-gateway
    restart: unless-stopped

volumes:
  redis_data:
  mariadb_data:
  postgres_data:
  prometheus_data:
  grafana_data:
