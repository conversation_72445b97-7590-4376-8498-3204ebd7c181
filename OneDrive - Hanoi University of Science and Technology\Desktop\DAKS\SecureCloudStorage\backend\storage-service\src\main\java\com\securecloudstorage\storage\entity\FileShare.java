package com.securecloudstorage.storage.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * File Share Entity for MariaDB
 */
@Entity
@Table(name = "file_share", indexes = {
    @Index(name = "idx_file_share_file_id", columnList = "file_id"),
    @Index(name = "idx_file_share_target_user", columnList = "target_user_id"),
    @Index(name = "idx_file_share_token", columnList = "share_token", unique = true)
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileShare {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "file_id", nullable = false, length = 255)
    private String fileId;
    
    @Column(name = "owner_user_id", nullable = false, length = 255)
    private String ownerUserId;
    
    @Column(name = "target_user_id", nullable = false, length = 255)
    private String targetUserId;
    
    @Column(name = "permission", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private Permission permission = Permission.READ;
    
    @Column(name = "share_token", nullable = false, unique = true, length = 255)
    private String shareToken;
    
    @CreationTimestamp
    @Column(name = "shared_date", nullable = false)
    private LocalDateTime sharedDate;
    
    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;
    
    @Column(name = "is_active", nullable = false)
    private boolean isActive = true;
    
    @Column(name = "access_count", nullable = false)
    private Long accessCount = 0L;
    
    @Column(name = "last_accessed")
    private LocalDateTime lastAccessed;
    
    @Column(name = "share_message", columnDefinition = "TEXT")
    private String shareMessage;
    
    @Column(name = "download_limit")
    private Integer downloadLimit;
    
    @Column(name = "downloads_count", nullable = false)
    private Integer downloadsCount = 0;
    
    public enum Permission {
        READ,
        WRITE,
        DELETE,
        SHARE
    }
}
