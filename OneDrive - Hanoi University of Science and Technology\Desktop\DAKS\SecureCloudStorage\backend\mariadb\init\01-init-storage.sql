-- MariaDB initialization script for Storage Service
-- This script will be executed when the MariaDB container starts for the first time

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS storagedb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user and grant privileges
CREATE USER IF NOT EXISTS 'storage_user'@'%' IDENTIFIED BY 'storage_password';
GRANT ALL PRIVILEGES ON storagedb.* TO 'storage_user'@'%';

-- Switch to storagedb database
USE storagedb;

-- Create tables for storage service
-- These tables will be created by Hibernate, but we can create indexes for better performance

-- File entities table will be created by Hibernate
-- We can add custom indexes after table creation
-- CREATE INDEX IF NOT EXISTS idx_file_user_id ON file_entity(user_id);
-- CREATE INDEX IF NOT EXISTS idx_file_upload_date ON file_entity(upload_date);
-- CREATE INDEX IF NOT EXISTS idx_file_quarantined ON file_entity(quarantined);

-- File access log table indexes
-- CREATE INDEX IF NOT EXISTS idx_access_log_file_id ON file_access_log(file_id);
-- CREATE INDEX IF NOT EXISTS idx_access_log_user_id ON file_access_log(user_id);
-- CREATE INDEX IF NOT EXISTS idx_access_log_date ON file_access_log(access_date);

-- File share table indexes
-- CREATE INDEX IF NOT EXISTS idx_file_share_file_id ON file_share(file_id);
-- CREATE INDEX IF NOT EXISTS idx_file_share_target_user ON file_share(target_user_id);
-- CREATE INDEX IF NOT EXISTS idx_file_share_token ON file_share(share_token);

-- Set timezone
SET time_zone = '+00:00';

-- Configure MariaDB settings for better performance
SET GLOBAL innodb_buffer_pool_size = 128M;
SET GLOBAL max_connections = 200;
SET GLOBAL wait_timeout = 600;
SET GLOBAL interactive_timeout = 600;

-- Create configuration for file storage
-- This can be used for application-level settings
CREATE TABLE IF NOT EXISTS storage_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(255) NOT NULL UNIQUE,
    config_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default configuration
INSERT INTO storage_config (config_key, config_value, description) VALUES
('max_file_size', '104857600', 'Maximum file size in bytes (100MB)'),
('default_encryption', 'AES-256', 'Default encryption algorithm'),
('scan_timeout', '30', 'AI-Malware scan timeout in seconds'),
('quarantine_retention', '30', 'Quarantine retention period in days'),
('max_files_per_user', '1000', 'Maximum files per user'),
('allowed_extensions', 'jpg,jpeg,png,gif,pdf,docx,xlsx,pptx,txt,zip,rar', 'Allowed file extensions')
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value);

-- Create audit table for security events
CREATE TABLE IF NOT EXISTS security_audit (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    user_id VARCHAR(255),
    file_id VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    event_details JSON,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'LOW',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_audit_event_type (event_type),
    INDEX idx_audit_user_id (user_id),
    INDEX idx_audit_file_id (file_id),
    INDEX idx_audit_created_at (created_at),
    INDEX idx_audit_severity (severity)
);

-- Create system stats table for monitoring
CREATE TABLE IF NOT EXISTS system_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(20,2),
    metric_unit VARCHAR(20),
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_stats_metric (metric_name),
    INDEX idx_stats_recorded_at (recorded_at)
);

-- Insert initial system stats
INSERT INTO system_stats (metric_name, metric_value, metric_unit) VALUES
('total_files', 0, 'count'),
('total_storage_used', 0, 'bytes'),
('total_users', 0, 'count'),
('quarantined_files', 0, 'count'),
('failed_uploads', 0, 'count'),
('successful_scans', 0, 'count'),
('threats_detected', 0, 'count')
ON DUPLICATE KEY UPDATE metric_value = VALUES(metric_value);

-- Create file metadata table for additional file information
CREATE TABLE IF NOT EXISTS file_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    file_id VARCHAR(255) NOT NULL,
    metadata_key VARCHAR(100) NOT NULL,
    metadata_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_file_metadata (file_id, metadata_key),
    INDEX idx_metadata_file_id (file_id),
    INDEX idx_metadata_key (metadata_key)
);

-- Create scan results table for AI-Malware detection results
CREATE TABLE IF NOT EXISTS scan_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    file_id VARCHAR(255) NOT NULL,
    scan_type VARCHAR(50) NOT NULL,
    scan_result ENUM('SAFE', 'THREAT', 'SUSPICIOUS', 'ERROR') NOT NULL,
    confidence_score DECIMAL(5,4),
    threat_type VARCHAR(100),
    scan_details JSON,
    scan_duration INT, -- in milliseconds
    scanned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_scan_file_id (file_id),
    INDEX idx_scan_result (scan_result),
    INDEX idx_scan_type (scan_type),
    INDEX idx_scan_date (scanned_at)
);

-- Create table for tracking file versions (for future use)
CREATE TABLE IF NOT EXISTS file_versions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    file_id VARCHAR(255) NOT NULL,
    version_number INT NOT NULL,
    file_path VARCHAR(500),
    file_size BIGINT,
    checksum VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_version_file_id (file_id),
    INDEX idx_version_number (version_number)
);

-- Create session table for user sessions
CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    user_id VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_session_user_id (user_id),
    INDEX idx_session_created_at (created_at),
    INDEX idx_session_expires_at (expires_at)
);

-- Flush privileges to ensure all changes take effect
FLUSH PRIVILEGES;

-- Show databases and tables for verification
SHOW DATABASES;
SHOW TABLES;

-- Display configuration
SELECT 'MariaDB Storage Service Database initialized successfully' AS status;
