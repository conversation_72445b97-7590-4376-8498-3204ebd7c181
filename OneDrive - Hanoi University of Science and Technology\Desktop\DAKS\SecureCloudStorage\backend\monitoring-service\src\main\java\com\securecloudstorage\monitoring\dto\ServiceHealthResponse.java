package com.securecloudstorage.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Service Health Response DTO
 * Response chứa thông tin health check của service
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceHealthResponse {

    private String serviceName;                    // Tên service
    private String status;                         // UP, DOWN, UNKNOWN
    private String health;                         // HEALTHY, WARNING, CRITICAL
    private LocalDateTime lastCheck;               // Thời gian check cuối
    private Long responseTime;                     // Response time (ms)
    private String version;                        // Version của service
    private String host;                           // Host/IP của service
    private Integer port;                          // Port của service
    private String url;                            // Health check URL
    private String description;                    // Mô tả trạng thái
    private Map<String, Object> details;           // Chi tiết health check
    private List<HealthCheck> checks;              // Các health check con
    private Map<String, String> metadata;          // Metadata bổ sung

    // Metrics liên quan đến service
    private Double cpuUsage;                       // CPU usage của service
    private Long memoryUsage;                      // Memory usage
    private Integer activeConnections;             // Số connections hiện tại
    private Long uptime;                           // Uptime của service
    private Double errorRate;                      // Tỷ lệ lỗi
    private Long requestCount;                     // Số requests
    private Double averageResponseTime;            // Thời gian phản hồi trung bình

    // Dependencies health
    private List<DependencyHealth> dependencies;   // Trạng thái dependencies
    private Boolean isDependencyHealthy;           // Tất cả dependencies OK?

    // Alerts và warnings
    private List<String> warnings;                 // Warnings hiện tại
    private List<String> errors;                   // Errors hiện tại
    private Integer alertCount;                    // Số alerts active

    // Configuration
    private Map<String, Object> configuration;     // Cấu hình service
    private String environment;                    // Environment (dev, prod, etc.)
    private String profile;                        // Spring profile

    // Nested classes
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HealthCheck {
        private String name;                       // Tên health check
        private String status;                     // UP, DOWN, UNKNOWN
        private String description;                // Mô tả
        private Map<String, Object> details;       // Chi tiết
        private Long responseTime;                 // Response time
        private LocalDateTime timestamp;           // Thời gian check
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DependencyHealth {
        private String name;                       // Tên dependency
        private String type;                       // DATABASE, SERVICE, EXTERNAL
        private String status;                     // UP, DOWN, UNKNOWN
        private String host;                       // Host/IP
        private Integer port;                      // Port
        private Long responseTime;                 // Response time
        private String version;                    // Version
        private LocalDateTime lastCheck;           // Thời gian check cuối
        private String errorMessage;               // Error message (nếu có)
        private Map<String, Object> details;       // Chi tiết
    }
}
