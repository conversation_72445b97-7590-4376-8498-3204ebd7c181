"""
Alert Management System for Network IDS
======================================

Module quản lý cảnh báo và thông báo
"""

import time
import json
import smtplib
import threading
import queue
from datetime import datetime
from typing import Dict, Any, List, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from pathlib import Path
import logging
import requests

class AlertThrottler:
    """Lớp quản lý throttling alerts"""
    
    def __init__(self):
        self.alert_history = {}
        self.lock = threading.Lock()
    
    def should_send_alert(self, alert_type: str, throttle_time: int) -> bool:
        """
        Kiểm tra có nên gửi alert không
        
        Args:
            alert_type: Loại alert
            throttle_time: Thời gian throttle (seconds)
            
        Returns:
            bool: True nếu nên gửi
        """
        current_time = time.time()
        
        with self.lock:
            if alert_type not in self.alert_history:
                self.alert_history[alert_type] = current_time
                return True
            
            last_sent = self.alert_history[alert_type]
            if current_time - last_sent >= throttle_time:
                self.alert_history[alert_type] = current_time
                return True
        
        return False
    
    def clear_history(self):
        """Xóa lịch sử alerts"""
        with self.lock:
            self.alert_history.clear()

class EmailNotifier:
    """Lớp gửi email notifications"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Khởi tạo email notifier
        
        Args:
            config: Cấu hình email
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.EmailNotifier")
        
        self.smtp_server = config.get('smtp_server', 'smtp.gmail.com')
        self.smtp_port = config.get('smtp_port', 587)
        self.username = config.get('username', '')
        self.password = config.get('password', '')
        self.recipients = config.get('recipients', [])
        
        self.enabled = config.get('enabled', False) and self.username and self.password
        
        if self.enabled:
            self.logger.info("Email notifier initialized")
        else:
            self.logger.warning("Email notifier disabled or not configured")
    
    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """
        Gửi email alert
        
        Args:
            alert_data: Dữ liệu alert
            
        Returns:
            bool: True nếu thành công
        """
        if not self.enabled:
            return False
        
        try:
            # Tạo email content
            subject = f"[NetworkIDS] {alert_data.get('severity', 'UNKNOWN')} - {alert_data.get('title', 'Security Alert')}"
            body = self._create_email_body(alert_data)
            
            # Tạo message
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['To'] = ', '.join(self.recipients)
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'html'))
            
            # Gửi email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            self.logger.info(f"Email alert sent: {subject}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending email alert: {str(e)}")
            return False
    
    def _create_email_body(self, alert_data: Dict[str, Any]) -> str:
        """
        Tạo nội dung email
        
        Args:
            alert_data: Dữ liệu alert
            
        Returns:
            str: HTML body
        """
        timestamp = datetime.fromtimestamp(alert_data.get('timestamp', time.time()))
        
        html_body = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f44336; color: white; padding: 10px; text-align: center; }}
                .content {{ padding: 20px; }}
                .section {{ margin: 15px 0; }}
                .label {{ font-weight: bold; color: #333; }}
                .value {{ margin-left: 10px; }}
                .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🚨 Network IDS Security Alert</h2>
            </div>
            
            <div class="content">
                <div class="section">
                    <span class="label">Alert Type:</span>
                    <span class="value">{alert_data.get('alert_type', 'Unknown')}</span>
                </div>
                
                <div class="section">
                    <span class="label">Severity:</span>
                    <span class="value">{alert_data.get('severity', 'Unknown')}</span>
                </div>
                
                <div class="section">
                    <span class="label">Timestamp:</span>
                    <span class="value">{timestamp.strftime('%Y-%m-%d %H:%M:%S')}</span>
                </div>
                
                <div class="section">
                    <span class="label">Description:</span>
                    <span class="value">{alert_data.get('description', 'No description available')}</span>
                </div>
        """
        
        # Thêm thông tin packet nếu có
        if 'packet_info' in alert_data:
            packet_info = alert_data['packet_info']
            html_body += f"""
                <div class="section">
                    <h3>Packet Information</h3>
                    <div class="value">
                        <strong>Source IP:</strong> {packet_info.get('src_ip', 'Unknown')}<br>
                        <strong>Destination IP:</strong> {packet_info.get('dst_ip', 'Unknown')}<br>
                        <strong>Protocol:</strong> {packet_info.get('protocol', 'Unknown')}<br>
                        <strong>Source Port:</strong> {packet_info.get('src_port', 'N/A')}<br>
                        <strong>Destination Port:</strong> {packet_info.get('dst_port', 'N/A')}<br>
                        <strong>Packet Size:</strong> {packet_info.get('size', 'Unknown')} bytes
                    </div>
                </div>
            """
        
        # Thêm thông tin ML results
        if 'ml_result' in alert_data:
            ml_result = alert_data['ml_result']
            html_body += f"""
                <div class="section">
                    <h3>Machine Learning Analysis</h3>
                    <div class="value">
                        <strong>Anomaly Detected:</strong> {ml_result.get('is_anomaly', False)}<br>
                        <strong>Confidence:</strong> {ml_result.get('confidence', 0):.2f}
                    </div>
                </div>
            """
        
        # Thêm thông tin signature results
        if 'signature_result' in alert_data:
            sig_result = alert_data['signature_result']
            html_body += f"""
                <div class="section">
                    <h3>Signature Analysis</h3>
                    <div class="value">
                        <strong>Attack Detected:</strong> {sig_result.get('is_attack', False)}<br>
                        <strong>Rules Matched:</strong> {len(sig_result.get('rules_matched', []))}
                    </div>
                </div>
            """
        
        html_body += """
            </div>
            
            <div class="footer">
                <p>This alert was generated by Network IDS system.</p>
                <p>Please investigate immediately if this is a HIGH or CRITICAL severity alert.</p>
            </div>
        </body>
        </html>
        """
        
        return html_body

class SlackNotifier:
    """Lớp gửi Slack notifications"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Khởi tạo Slack notifier
        
        Args:
            config: Cấu hình Slack
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.SlackNotifier")
        
        self.webhook_url = config.get('webhook_url', '')
        self.channel = config.get('channel', '#security-alerts')
        self.enabled = config.get('enabled', False) and self.webhook_url
        
        if self.enabled:
            self.logger.info("Slack notifier initialized")
        else:
            self.logger.warning("Slack notifier disabled or not configured")
    
    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """
        Gửi Slack alert
        
        Args:
            alert_data: Dữ liệu alert
            
        Returns:
            bool: True nếu thành công
        """
        if not self.enabled:
            return False
        
        try:
            # Tạo Slack message
            severity = alert_data.get('severity', 'UNKNOWN')
            emoji = self._get_severity_emoji(severity)
            color = self._get_severity_color(severity)
            
            message = {
                "channel": self.channel,
                "username": "Network IDS",
                "icon_emoji": ":shield:",
                "attachments": [
                    {
                        "color": color,
                        "title": f"{emoji} Security Alert - {severity}",
                        "text": alert_data.get('description', 'No description available'),
                        "fields": [
                            {
                                "title": "Alert Type",
                                "value": alert_data.get('alert_type', 'Unknown'),
                                "short": True
                            },
                            {
                                "title": "Timestamp",
                                "value": datetime.fromtimestamp(alert_data.get('timestamp', time.time())).strftime('%Y-%m-%d %H:%M:%S'),
                                "short": True
                            }
                        ],
                        "ts": int(time.time())
                    }
                ]
            }
            
            # Thêm packet info nếu có
            if 'packet_info' in alert_data:
                packet_info = alert_data['packet_info']
                message["attachments"][0]["fields"].extend([
                    {
                        "title": "Source IP",
                        "value": packet_info.get('src_ip', 'Unknown'),
                        "short": True
                    },
                    {
                        "title": "Destination IP",
                        "value": packet_info.get('dst_ip', 'Unknown'),
                        "short": True
                    }
                ])
            
            # Gửi message
            response = requests.post(self.webhook_url, json=message, timeout=10)
            response.raise_for_status()
            
            self.logger.info(f"Slack alert sent: {alert_data.get('alert_type', 'Unknown')}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending Slack alert: {str(e)}")
            return False
    
    def _get_severity_emoji(self, severity: str) -> str:
        """Lấy emoji cho severity"""
        emoji_map = {
            'LOW': '🟡',
            'MEDIUM': '🟠',
            'HIGH': '🔴',
            'CRITICAL': '🚨'
        }
        return emoji_map.get(severity, '⚪')
    
    def _get_severity_color(self, severity: str) -> str:
        """Lấy color cho severity"""
        color_map = {
            'LOW': '#ffeb3b',
            'MEDIUM': '#ff9800',
            'HIGH': '#f44336',
            'CRITICAL': '#9c27b0'
        }
        return color_map.get(severity, '#9e9e9e')

class WebhookNotifier:
    """Lớp gửi webhook notifications"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Khởi tạo webhook notifier
        
        Args:
            config: Cấu hình webhook
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.WebhookNotifier")
        
        self.url = config.get('url', '')
        self.headers = config.get('headers', {})
        self.enabled = config.get('enabled', False) and self.url
        
        if self.enabled:
            self.logger.info("Webhook notifier initialized")
        else:
            self.logger.warning("Webhook notifier disabled or not configured")
    
    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """
        Gửi webhook alert
        
        Args:
            alert_data: Dữ liệu alert
            
        Returns:
            bool: True nếu thành công
        """
        if not self.enabled:
            return False
        
        try:
            # Chuẩn bị payload
            payload = {
                'timestamp': alert_data.get('timestamp', time.time()),
                'alert_type': alert_data.get('alert_type', 'unknown'),
                'severity': alert_data.get('severity', 'UNKNOWN'),
                'description': alert_data.get('description', ''),
                'data': alert_data
            }
            
            # Gửi request
            response = requests.post(
                self.url,
                json=payload,
                headers=self.headers,
                timeout=10
            )
            response.raise_for_status()
            
            self.logger.info(f"Webhook alert sent: {alert_data.get('alert_type', 'Unknown')}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending webhook alert: {str(e)}")
            return False

class AlertManager:
    """Lớp quản lý alerts chính"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Khởi tạo alert manager
        
        Args:
            config: Cấu hình alerts
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.AlertManager")
        
        self.enabled = config.get('enabled', True)
        self.severity_levels = config.get('severity_levels', {})
        
        # Alert queue
        self.alert_queue = queue.Queue(maxsize=1000)
        
        # Throttler
        self.throttler = AlertThrottler()
        
        # Notifiers
        self.notifiers = {}
        self._initialize_notifiers()
        
        # Worker thread
        self.worker_thread = None
        self.is_running = False
        
        # Statistics
        self.stats = {
            'total_alerts': 0,
            'alerts_sent': 0,
            'alerts_throttled': 0,
            'alerts_failed': 0,
            'by_severity': {
                'LOW': 0,
                'MEDIUM': 0,
                'HIGH': 0,
                'CRITICAL': 0
            }
        }
        
        if self.enabled:
            self.start()
        
        self.logger.info("AlertManager initialized")
    
    def _initialize_notifiers(self):
        """Khởi tạo các notifiers"""
        channels = self.config.get('channels', {})
        
        # Email notifier
        if 'email' in channels:
            self.notifiers['email'] = EmailNotifier(channels['email'])
        
        # Slack notifier
        if 'slack' in channels:
            self.notifiers['slack'] = SlackNotifier(channels['slack'])
        
        # Webhook notifier
        if 'webhook' in channels:
            self.notifiers['webhook'] = WebhookNotifier(channels['webhook'])
    
    def start(self):
        """Bắt đầu alert processing"""
        if self.is_running:
            return
        
        self.is_running = True
        self.worker_thread = threading.Thread(target=self._alert_worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
        
        self.logger.info("Alert manager started")
    
    def stop(self):
        """Dừng alert processing"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        
        self.logger.info("Alert manager stopped")
    
    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """
        Gửi alert
        
        Args:
            alert_data: Dữ liệu alert
            
        Returns:
            bool: True nếu thành công
        """
        if not self.enabled:
            return False
        
        try:
            # Thêm timestamp nếu chưa có
            if 'timestamp' not in alert_data:
                alert_data['timestamp'] = time.time()
            
            # Thêm vào queue
            if not self.alert_queue.full():
                self.alert_queue.put(alert_data)
                self.stats['total_alerts'] += 1
                return True
            else:
                self.logger.warning("Alert queue full, dropping alert")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending alert: {str(e)}")
            return False
    
    def _alert_worker(self):
        """Worker thread xử lý alerts"""
        while self.is_running:
            try:
                # Lấy alert từ queue
                alert_data = self.alert_queue.get(timeout=1)
                
                # Xử lý alert
                self._process_alert(alert_data)
                
                # Mark task done
                self.alert_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Error in alert worker: {str(e)}")
    
    def _process_alert(self, alert_data: Dict[str, Any]):
        """
        Xử lý alert
        
        Args:
            alert_data: Dữ liệu alert
        """
        try:
            severity = alert_data.get('severity', 'LOW')
            alert_type = alert_data.get('alert_type', 'unknown')
            
            # Update statistics
            if severity in self.stats['by_severity']:
                self.stats['by_severity'][severity] += 1
            
            # Lấy config cho severity level
            severity_config = self.severity_levels.get(severity, {})
            throttle_time = severity_config.get('throttle', 0)
            channels = severity_config.get('channels', [])
            
            # Kiểm tra throttling
            if throttle_time > 0:
                throttle_key = f"{alert_type}_{severity}"
                if not self.throttler.should_send_alert(throttle_key, throttle_time):
                    self.stats['alerts_throttled'] += 1
                    self.logger.debug(f"Alert throttled: {throttle_key}")
                    return
            
            # Gửi alert qua các channels
            sent_successfully = False
            for channel in channels:
                if channel in self.notifiers:
                    try:
                        if self.notifiers[channel].send_alert(alert_data):
                            sent_successfully = True
                    except Exception as e:
                        self.logger.error(f"Error sending alert via {channel}: {str(e)}")
            
            # Update statistics
            if sent_successfully:
                self.stats['alerts_sent'] += 1
            else:
                self.stats['alerts_failed'] += 1
            
            # Log alert
            self._log_alert(alert_data)
            
        except Exception as e:
            self.logger.error(f"Error processing alert: {str(e)}")
            self.stats['alerts_failed'] += 1
    
    def _log_alert(self, alert_data: Dict[str, Any]):
        """
        Log alert
        
        Args:
            alert_data: Dữ liệu alert
        """
        # Tạo log entry
        log_entry = {
            'timestamp': alert_data.get('timestamp', time.time()),
            'alert_type': alert_data.get('alert_type', 'unknown'),
            'severity': alert_data.get('severity', 'UNKNOWN'),
            'description': alert_data.get('description', ''),
            'data': alert_data
        }
        
        # Lưu vào file
        log_file = Path('logs/alerts.log')
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry) + '\n')
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Lấy thống kê alerts
        
        Returns:
            Dict: Thống kê
        """
        stats = self.stats.copy()
        
        # Tính toán thêm
        if stats['total_alerts'] > 0:
            stats['success_rate'] = (stats['alerts_sent'] / stats['total_alerts']) * 100
            stats['throttle_rate'] = (stats['alerts_throttled'] / stats['total_alerts']) * 100
            stats['failure_rate'] = (stats['alerts_failed'] / stats['total_alerts']) * 100
        
        return stats
    
    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """
        Lấy alerts gần đây
        
        Args:
            hours: Số giờ gần đây
            
        Returns:
            List: Danh sách alerts
        """
        cutoff_time = time.time() - (hours * 3600)
        recent_alerts = []
        
        log_file = Path('logs/alerts.log')
        if not log_file.exists():
            return recent_alerts
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        alert = json.loads(line.strip())
                        if alert.get('timestamp', 0) >= cutoff_time:
                            recent_alerts.append(alert)
                    except json.JSONDecodeError:
                        continue
        except Exception as e:
            self.logger.error(f"Error reading alert log: {str(e)}")
        
        return recent_alerts
    
    def clear_throttle_history(self):
        """Xóa lịch sử throttling"""
        self.throttler.clear_history()
        self.logger.info("Throttle history cleared")
    
    def test_notifiers(self) -> Dict[str, bool]:
        """
        Test các notifiers
        
        Returns:
            Dict: Kết quả test
        """
        results = {}
        
        test_alert = {
            'alert_type': 'test',
            'severity': 'LOW',
            'description': 'This is a test alert from Network IDS',
            'timestamp': time.time()
        }
        
        for name, notifier in self.notifiers.items():
            try:
                results[name] = notifier.send_alert(test_alert)
            except Exception as e:
                self.logger.error(f"Error testing {name} notifier: {str(e)}")
                results[name] = False
        
        return results
