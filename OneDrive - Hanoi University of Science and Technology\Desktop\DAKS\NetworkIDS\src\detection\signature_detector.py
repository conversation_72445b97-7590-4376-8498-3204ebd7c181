"""
Signature-based Detection Module for Network IDS
===============================================

Module ph<PERSON><PERSON> hiện attacks dựa trên signatures và rules
"""

import os
import re
import json
import time
import threading
from typing import Dict, Any, List, Optional, Tuple, Pattern
import logging
from pathlib import Path
from collections import defaultdict

try:
    from scapy.all import *
    from scapy.layers.inet import IP, TCP, UDP, ICMP
    from scapy.layers.l2 import Ether, ARP
    from scapy.layers.dns import DNS
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False

class SignatureRule:
    """Lớp định nghĩa signature rule"""
    
    def __init__(self, rule_id: str, name: str, description: str, 
                 pattern: str, severity: str, category: str):
        """
        Khởi tạo signature rule
        
        Args:
            rule_id: ID của rule
            name: Tên rule
            description: <PERSON><PERSON> tả rule
            pattern: Pattern để match
            severity: <PERSON>ứ<PERSON> độ nghiêm trọng
            category: Loại attack
        """
        self.rule_id = rule_id
        self.name = name
        self.description = description
        self.pattern = pattern
        self.severity = severity
        self.category = category
        self.regex = None
        self.match_count = 0
        self.last_match = None
        
        # Compile regex pattern
        try:
            self.regex = re.compile(pattern, re.IGNORECASE)
        except re.error as e:
            logging.error(f"Invalid regex pattern in rule {rule_id}: {e}")
    
    def match(self, data: str) -> bool:
        """
        Kiểm tra rule có match với data không
        
        Args:
            data: Dữ liệu để kiểm tra
            
        Returns:
            bool: True nếu match
        """
        if not self.regex:
            return False
        
        try:
            match = self.regex.search(data)
            if match:
                self.match_count += 1
                self.last_match = time.time()
                return True
        except Exception as e:
            logging.error(f"Error matching rule {self.rule_id}: {e}")
        
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Chuyển rule thành dictionary
        
        Returns:
            Dict: Rule information
        """
        return {
            'rule_id': self.rule_id,
            'name': self.name,
            'description': self.description,
            'pattern': self.pattern,
            'severity': self.severity,
            'category': self.category,
            'match_count': self.match_count,
            'last_match': self.last_match
        }

class AttackDetector:
    """Lớp phát hiện các loại attacks cụ thể"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.AttackDetector")
        
        # Connection tracking
        self.connection_counts = defaultdict(int)
        self.port_scan_tracker = defaultdict(set)
        self.brute_force_tracker = defaultdict(list)
        self.dns_queries = defaultdict(list)
        
        # Thresholds
        self.port_scan_threshold = 20
        self.brute_force_threshold = 5
        self.brute_force_window = 60  # seconds
        self.ddos_threshold = 1000
        self.ddos_window = 60  # seconds
        
        # Cleanup interval
        self.cleanup_interval = 300  # seconds
        self.last_cleanup = time.time()
    
    def detect_port_scan(self, packet) -> Optional[Dict[str, Any]]:
        """
        Phát hiện port scanning
        
        Args:
            packet: Gói tin
            
        Returns:
            Dict: Thông tin về port scan (nếu phát hiện)
        """
        if not packet.haslayer(TCP) or not packet.haslayer(IP):
            return None
        
        src_ip = packet[IP].src
        dst_ip = packet[IP].dst
        dst_port = packet[TCP].dport
        
        # Track ports accessed by source IP
        self.port_scan_tracker[src_ip].add(dst_port)
        
        # Check if threshold exceeded
        if len(self.port_scan_tracker[src_ip]) >= self.port_scan_threshold:
            return {
                'attack_type': 'port_scan',
                'src_ip': src_ip,
                'dst_ip': dst_ip,
                'ports_scanned': len(self.port_scan_tracker[src_ip]),
                'ports': list(self.port_scan_tracker[src_ip]),
                'severity': 'MEDIUM',
                'description': f'Port scan detected from {src_ip}'
            }
        
        return None
    
    def detect_ddos(self, packet) -> Optional[Dict[str, Any]]:
        """
        Phát hiện DDoS attacks
        
        Args:
            packet: Gói tin
            
        Returns:
            Dict: Thông tin về DDoS (nếu phát hiện)
        """
        if not packet.haslayer(IP):
            return None
        
        dst_ip = packet[IP].dst
        current_time = time.time()
        
        # Count connections per destination
        key = f"ddos_{dst_ip}"
        self.connection_counts[key] += 1
        
        # Check if threshold exceeded
        if self.connection_counts[key] >= self.ddos_threshold:
            return {
                'attack_type': 'ddos',
                'target_ip': dst_ip,
                'connection_count': self.connection_counts[key],
                'severity': 'HIGH',
                'description': f'DDoS attack detected against {dst_ip}'
            }
        
        return None
    
    def detect_brute_force(self, packet) -> Optional[Dict[str, Any]]:
        """
        Phát hiện brute force attacks
        
        Args:
            packet: Gói tin
            
        Returns:
            Dict: Thông tin về brute force (nếu phát hiện)
        """
        if not packet.haslayer(TCP) or not packet.haslayer(IP):
            return None
        
        src_ip = packet[IP].src
        dst_ip = packet[IP].dst
        dst_port = packet[TCP].dport
        current_time = time.time()
        
        # Only check common authentication ports
        auth_ports = {22, 23, 21, 25, 110, 143, 993, 995, 3389}
        if dst_port not in auth_ports:
            return None
        
        # Track failed connection attempts
        key = f"{src_ip}_{dst_ip}_{dst_port}"
        self.brute_force_tracker[key].append(current_time)
        
        # Remove old entries
        cutoff_time = current_time - self.brute_force_window
        self.brute_force_tracker[key] = [
            t for t in self.brute_force_tracker[key] if t > cutoff_time
        ]
        
        # Check if threshold exceeded
        if len(self.brute_force_tracker[key]) >= self.brute_force_threshold:
            return {
                'attack_type': 'brute_force',
                'src_ip': src_ip,
                'dst_ip': dst_ip,
                'dst_port': dst_port,
                'attempt_count': len(self.brute_force_tracker[key]),
                'severity': 'HIGH',
                'description': f'Brute force attack detected from {src_ip} to {dst_ip}:{dst_port}'
            }
        
        return None
    
    def detect_arp_spoofing(self, packet) -> Optional[Dict[str, Any]]:
        """
        Phát hiện ARP spoofing
        
        Args:
            packet: Gói tin
            
        Returns:
            Dict: Thông tin về ARP spoofing (nếu phát hiện)
        """
        if not packet.haslayer(ARP):
            return None
        
        arp = packet[ARP]
        
        # Check for ARP response
        if arp.op == 2:  # ARP response
            # Simple detection: check for duplicate IP responses
            # In a real implementation, you'd maintain an ARP table
            return {
                'attack_type': 'arp_spoofing',
                'src_ip': arp.psrc,
                'src_mac': arp.hwsrc,
                'dst_ip': arp.pdst,
                'severity': 'MEDIUM',
                'description': f'Potential ARP spoofing detected from {arp.psrc}'
            }
        
        return None
    
    def detect_dns_poisoning(self, packet) -> Optional[Dict[str, Any]]:
        """
        Phát hiện DNS poisoning
        
        Args:
            packet: Gói tin
            
        Returns:
            Dict: Thông tin về DNS poisoning (nếu phát hiện)
        """
        if not packet.haslayer(DNS):
            return None
        
        dns = packet[DNS]
        
        # Check for DNS responses
        if dns.qr == 1:  # DNS response
            # Track responses to detect inconsistencies
            query_name = dns.qd.qname.decode() if dns.qd else None
            
            if query_name:
                current_time = time.time()
                self.dns_queries[query_name].append({
                    'timestamp': current_time,
                    'response': dns.an.rdata if dns.an else None
                })
                
                # Simple detection: check for multiple different responses
                if len(self.dns_queries[query_name]) > 1:
                    responses = [q['response'] for q in self.dns_queries[query_name]]
                    unique_responses = set(responses)
                    
                    if len(unique_responses) > 1:
                        return {
                            'attack_type': 'dns_poisoning',
                            'query_name': query_name,
                            'responses': list(unique_responses),
                            'severity': 'HIGH',
                            'description': f'DNS poisoning detected for {query_name}'
                        }
        
        return None
    
    def detect_sql_injection(self, packet) -> Optional[Dict[str, Any]]:
        """
        Phát hiện SQL injection attempts
        
        Args:
            packet: Gói tin
            
        Returns:
            Dict: Thông tin về SQL injection (nếu phát hiện)
        """
        if not packet.haslayer(Raw):
            return None
        
        try:
            payload = packet[Raw].load.decode('utf-8', errors='ignore')
            
            # Common SQL injection patterns
            sql_patterns = [
                r"(\bor\b|\band\b)\s+\d+\s*=\s*\d+",
                r"union\s+select",
                r"select\s+.+\s+from",
                r"insert\s+into",
                r"update\s+.+\s+set",
                r"delete\s+from",
                r"drop\s+table",
                r"exec\s*\(",
                r"script\s*>",
                r"<\s*script",
                r"javascript\s*:",
                r"vbscript\s*:",
                r"onload\s*=",
                r"onerror\s*="
            ]
            
            for pattern in sql_patterns:
                if re.search(pattern, payload, re.IGNORECASE):
                    return {
                        'attack_type': 'sql_injection',
                        'pattern_matched': pattern,
                        'payload_sample': payload[:200],
                        'severity': 'HIGH',
                        'description': 'SQL injection attempt detected'
                    }
        
        except Exception as e:
            self.logger.error(f"Error detecting SQL injection: {e}")
        
        return None
    
    def cleanup_old_data(self):
        """Dọn dẹp dữ liệu cũ"""
        current_time = time.time()
        
        if current_time - self.last_cleanup < self.cleanup_interval:
            return
        
        # Cleanup brute force tracker
        cutoff_time = current_time - self.brute_force_window
        for key in list(self.brute_force_tracker.keys()):
            self.brute_force_tracker[key] = [
                t for t in self.brute_force_tracker[key] if t > cutoff_time
            ]
            if not self.brute_force_tracker[key]:
                del self.brute_force_tracker[key]
        
        # Cleanup DNS queries
        dns_cutoff = current_time - 3600  # 1 hour
        for key in list(self.dns_queries.keys()):
            self.dns_queries[key] = [
                q for q in self.dns_queries[key] if q['timestamp'] > dns_cutoff
            ]
            if not self.dns_queries[key]:
                del self.dns_queries[key]
        
        # Reset connection counts periodically
        if current_time - self.last_cleanup > self.ddos_window:
            self.connection_counts.clear()
        
        self.last_cleanup = current_time

class SignatureDetector:
    """Lớp chính cho signature-based detection"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Khởi tạo signature detector
        
        Args:
            config: Cấu hình signature detection
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.SignatureDetector")
        
        # Signature rules
        self.rules = {}
        self.rules_enabled = config.get('enabled', True)
        self.rules_path = config.get('rules_path', 'rules/')
        
        # Attack detector
        self.attack_detector = AttackDetector()
        
        # Statistics
        self.stats = {
            'total_rules': 0,
            'rules_matched': 0,
            'attacks_detected': 0,
            'false_positives': 0,
            'last_update': None
        }
        
        # Load rules
        if self.rules_enabled:
            self.load_rules()
        
        self.logger.info(f"SignatureDetector initialized with {len(self.rules)} rules")
    
    def load_rules(self) -> bool:
        """
        Tải signature rules
        
        Returns:
            bool: True nếu thành công
        """
        try:
            rules_path = Path(self.rules_path)
            rules_path.mkdir(parents=True, exist_ok=True)
            
            # Load built-in rules
            self._load_builtin_rules()
            
            # Load custom rules from files
            for rule_file in rules_path.glob("*.json"):
                self._load_rule_file(rule_file)
            
            self.stats['total_rules'] = len(self.rules)
            self.stats['last_update'] = time.time()
            
            self.logger.info(f"Loaded {len(self.rules)} signature rules")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading rules: {str(e)}")
            return False
    
    def _load_builtin_rules(self):
        """Tải các rules built-in"""
        builtin_rules = [
            {
                'rule_id': 'DDOS_001',
                'name': 'DDoS Detection',
                'description': 'Detect distributed denial of service attacks',
                'pattern': r'ddos|flood|overwhelm',
                'severity': 'HIGH',
                'category': 'ddos'
            },
            {
                'rule_id': 'SCAN_001',
                'name': 'Port Scan Detection',
                'description': 'Detect port scanning activities',
                'pattern': r'nmap|port.*scan|stealth.*scan',
                'severity': 'MEDIUM',
                'category': 'reconnaissance'
            },
            {
                'rule_id': 'SQLI_001',
                'name': 'SQL Injection',
                'description': 'Detect SQL injection attempts',
                'pattern': r'(union.*select|select.*from|insert.*into|drop.*table)',
                'severity': 'HIGH',
                'category': 'injection'
            },
            {
                'rule_id': 'XSS_001',
                'name': 'Cross-Site Scripting',
                'description': 'Detect XSS attempts',
                'pattern': r'(<script|javascript:|vbscript:|onload=|onerror=)',
                'severity': 'MEDIUM',
                'category': 'injection'
            },
            {
                'rule_id': 'BRUTE_001',
                'name': 'Brute Force Attack',
                'description': 'Detect brute force authentication attempts',
                'pattern': r'(login.*failed|authentication.*failed|invalid.*password)',
                'severity': 'HIGH',
                'category': 'brute_force'
            },
            {
                'rule_id': 'MALWARE_001',
                'name': 'Malware Communication',
                'description': 'Detect malware communication patterns',
                'pattern': r'(botnet|c2|command.*control|backdoor)',
                'severity': 'CRITICAL',
                'category': 'malware'
            }
        ]
        
        for rule_data in builtin_rules:
            rule = SignatureRule(**rule_data)
            self.rules[rule.rule_id] = rule
    
    def _load_rule_file(self, file_path: Path):
        """
        Tải rules từ file
        
        Args:
            file_path: Đường dẫn file
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                rules_data = json.load(f)
            
            for rule_data in rules_data:
                rule = SignatureRule(**rule_data)
                self.rules[rule.rule_id] = rule
            
            self.logger.info(f"Loaded rules from {file_path}")
            
        except Exception as e:
            self.logger.error(f"Error loading rule file {file_path}: {str(e)}")
    
    def detect_attack(self, packet) -> Dict[str, Any]:
        """
        Phát hiện attacks trong packet
        
        Args:
            packet: Gói tin
            
        Returns:
            Dict: Kết quả detection
        """
        results = {
            'is_attack': False,
            'attacks_detected': [],
            'rules_matched': [],
            'timestamp': time.time()
        }
        
        if not self.rules_enabled:
            return results
        
        try:
            # Signature-based detection
            signature_results = self._check_signatures(packet)
            results['rules_matched'] = signature_results
            
            # Behavioral attack detection
            behavioral_results = self._check_behavioral_attacks(packet)
            results['attacks_detected'] = behavioral_results
            
            # Determine if attack detected
            results['is_attack'] = len(signature_results) > 0 or len(behavioral_results) > 0
            
            # Update statistics
            if results['is_attack']:
                self.stats['attacks_detected'] += 1
            
            if signature_results:
                self.stats['rules_matched'] += len(signature_results)
            
            # Cleanup old data periodically
            self.attack_detector.cleanup_old_data()
            
        except Exception as e:
            self.logger.error(f"Error detecting attack: {str(e)}")
        
        return results
    
    def _check_signatures(self, packet) -> List[Dict[str, Any]]:
        """
        Kiểm tra signature rules
        
        Args:
            packet: Gói tin
            
        Returns:
            List: Danh sách rules matched
        """
        matched_rules = []
        
        try:
            # Extract text data from packet
            text_data = self._extract_text_data(packet)
            
            if not text_data:
                return matched_rules
            
            # Check each rule
            for rule_id, rule in self.rules.items():
                if rule.match(text_data):
                    matched_rules.append({
                        'rule_id': rule_id,
                        'name': rule.name,
                        'description': rule.description,
                        'severity': rule.severity,
                        'category': rule.category,
                        'match_count': rule.match_count
                    })
        
        except Exception as e:
            self.logger.error(f"Error checking signatures: {str(e)}")
        
        return matched_rules
    
    def _extract_text_data(self, packet) -> str:
        """
        Trích xuất text data từ packet
        
        Args:
            packet: Gói tin
            
        Returns:
            str: Text data
        """
        text_data = []
        
        try:
            # HTTP payload
            if packet.haslayer(Raw):
                payload = packet[Raw].load
                try:
                    text_data.append(payload.decode('utf-8', errors='ignore'))
                except:
                    text_data.append(str(payload))
            
            # DNS queries
            if packet.haslayer(DNS):
                if packet[DNS].qd:
                    text_data.append(packet[DNS].qd.qname.decode())
            
            # Add packet summary
            text_data.append(str(packet.summary()))
            
        except Exception as e:
            self.logger.error(f"Error extracting text data: {str(e)}")
        
        return ' '.join(text_data)
    
    def _check_behavioral_attacks(self, packet) -> List[Dict[str, Any]]:
        """
        Kiểm tra behavioral attacks
        
        Args:
            packet: Gói tin
            
        Returns:
            List: Danh sách attacks detected
        """
        attacks = []
        
        try:
            # Port scan detection
            port_scan = self.attack_detector.detect_port_scan(packet)
            if port_scan:
                attacks.append(port_scan)
            
            # DDoS detection
            ddos = self.attack_detector.detect_ddos(packet)
            if ddos:
                attacks.append(ddos)
            
            # Brute force detection
            brute_force = self.attack_detector.detect_brute_force(packet)
            if brute_force:
                attacks.append(brute_force)
            
            # ARP spoofing detection
            arp_spoofing = self.attack_detector.detect_arp_spoofing(packet)
            if arp_spoofing:
                attacks.append(arp_spoofing)
            
            # DNS poisoning detection
            dns_poisoning = self.attack_detector.detect_dns_poisoning(packet)
            if dns_poisoning:
                attacks.append(dns_poisoning)
            
            # SQL injection detection
            sql_injection = self.attack_detector.detect_sql_injection(packet)
            if sql_injection:
                attacks.append(sql_injection)
        
        except Exception as e:
            self.logger.error(f"Error checking behavioral attacks: {str(e)}")
        
        return attacks
    
    def add_rule(self, rule_data: Dict[str, Any]) -> bool:
        """
        Thêm rule mới
        
        Args:
            rule_data: Dữ liệu rule
            
        Returns:
            bool: True nếu thành công
        """
        try:
            rule = SignatureRule(**rule_data)
            self.rules[rule.rule_id] = rule
            self.stats['total_rules'] = len(self.rules)
            
            self.logger.info(f"Added new rule: {rule.rule_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding rule: {str(e)}")
            return False
    
    def remove_rule(self, rule_id: str) -> bool:
        """
        Xóa rule
        
        Args:
            rule_id: ID của rule
            
        Returns:
            bool: True nếu thành công
        """
        if rule_id in self.rules:
            del self.rules[rule_id]
            self.stats['total_rules'] = len(self.rules)
            
            self.logger.info(f"Removed rule: {rule_id}")
            return True
        
        return False
    
    def get_rule_statistics(self) -> Dict[str, Any]:
        """
        Lấy thống kê rules
        
        Returns:
            Dict: Thống kê rules
        """
        rule_stats = []
        
        for rule_id, rule in self.rules.items():
            rule_stats.append(rule.to_dict())
        
        return {
            'total_rules': len(self.rules),
            'rules': rule_stats,
            'global_stats': self.stats
        }
    
    def export_rules(self, file_path: str) -> bool:
        """
        Xuất rules ra file
        
        Args:
            file_path: Đường dẫn file
            
        Returns:
            bool: True nếu thành công
        """
        try:
            rules_data = []
            
            for rule in self.rules.values():
                rules_data.append(rule.to_dict())
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(rules_data, f, indent=2)
            
            self.logger.info(f"Exported {len(rules_data)} rules to {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error exporting rules: {str(e)}")
            return False
