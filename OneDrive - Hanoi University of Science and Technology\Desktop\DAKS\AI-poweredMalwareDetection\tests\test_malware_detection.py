"""
Test cases for malware detection system
"""

import unittest
import os
import tempfile
import shutil
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.analysis.static_analyzer import StaticAnalyzer
from src.models.ensemble_model import EnsembleDetector
from src.utils.config import load_config
from src.data_preprocessing.preprocessor import DataPreprocessor

class TestStaticAnalyzer(unittest.TestCase):
    """Test cases for static analyzer"""
    
    def setUp(self):
        """Setup test environment"""
        self.config = {
            'features': {
                'pe_features': {'enabled': True},
                'static_features': {'enabled': True}
            }
        }
        self.analyzer = StaticAnalyzer(self.config)
        
        # Create a temporary test file
        self.test_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt')
        self.test_file.write(b'This is a test file with some content')
        self.test_file.close()
    
    def tearDown(self):
        """Cleanup test environment"""
        os.unlink(self.test_file.name)
    
    def test_basic_features(self):
        """Test basic feature extraction"""
        features = self.analyzer._extract_basic_features(self.test_file.name)
        
        self.assertIn('file_size', features)
        self.assertIn('file_type', features)
        self.assertIn('md5', features)
        self.assertIn('sha1', features)
        self.assertIn('sha256', features)
        
        self.assertGreater(features['file_size'], 0)
    
    def test_entropy_calculation(self):
        """Test entropy calculation"""
        test_data = b'A' * 100  # Low entropy
        entropy = self.analyzer._calculate_entropy(test_data)
        self.assertLess(entropy, 2.0)
        
        test_data = os.urandom(100)  # High entropy
        entropy = self.analyzer._calculate_entropy(test_data)
        self.assertGreater(entropy, 6.0)
    
    def test_string_extraction(self):
        """Test string extraction"""
        test_data = b'Hello World!\x00\x00Test String\x00\x00'
        strings = self.analyzer._extract_strings(test_data)
        
        self.assertIn('Hello World!', strings)
        self.assertIn('Test String', strings)

class TestEnsembleDetector(unittest.TestCase):
    """Test cases for ensemble detector"""
    
    def setUp(self):
        """Setup test environment"""
        self.config = {
            'models': {
                'random_forest': {'enabled': True, 'n_estimators': 10},
                'svm': {'enabled': True},
                'neural_network': {'enabled': True, 'epochs': 5}
            }
        }
        self.detector = EnsembleDetector(self.config)
    
    def test_model_initialization(self):
        """Test model initialization"""
        self.assertIn('random_forest', self.detector.models)
        self.assertIn('svm', self.detector.models)
        self.assertIn('neural_network', self.detector.models)
    
    def test_feature_combination(self):
        """Test feature combination"""
        static_features = {'file_size': 1000, 'entropy': 6.5}
        dynamic_features = {'network_connections': 2}
        
        combined = self.detector._combine_features(static_features, dynamic_features)
        
        self.assertIn('file_size', combined)
        self.assertIn('entropy', combined)
        self.assertIn('network_connections', combined)

class TestDataPreprocessor(unittest.TestCase):
    """Test cases for data preprocessor"""
    
    def setUp(self):
        """Setup test environment"""
        self.config = {}
        self.preprocessor = DataPreprocessor(self.config)
    
    def test_missing_value_handling(self):
        """Test missing value handling"""
        import pandas as pd
        import numpy as np
        
        df = pd.DataFrame({
            'feature1': [1, 2, np.nan, 4],
            'feature2': [5, np.nan, 7, 8],
            'label': [0, 1, 0, 1]
        })
        
        df_clean = self.preprocessor._handle_missing_values(df)
        
        self.assertFalse(df_clean['feature1'].isna().any())
        self.assertFalse(df_clean['feature2'].isna().any())
    
    def test_feature_engineering(self):
        """Test feature engineering"""
        import pandas as pd
        
        df = pd.DataFrame({
            'file_size': [1000, 2000, 3000],
            'file_entropy': [5.0, 6.0, 7.0],
            'pe_sections': [3, 4, 5],
            'pe_import_functions': [30, 40, 50]
        })
        
        df_engineered = self.preprocessor._feature_engineering(df)
        
        self.assertIn('file_size_log', df_engineered.columns)
        self.assertIn('functions_per_section', df_engineered.columns)

class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def setUp(self):
        """Setup test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.test_dir, 'test_config.yaml')
        
        # Create test config
        config_content = """
logging:
  level: INFO
models:
  random_forest:
    enabled: true
    n_estimators: 10
features:
  pe_features:
    enabled: true
  static_features:
    enabled: true
"""
        with open(self.config_path, 'w') as f:
            f.write(config_content)
    
    def tearDown(self):
        """Cleanup test environment"""
        shutil.rmtree(self.test_dir)
    
    def test_config_loading(self):
        """Test configuration loading"""
        from src.utils.config import load_config
        
        config = load_config(self.config_path)
        
        self.assertIn('logging', config)
        self.assertIn('models', config)
        self.assertIn('features', config)
        
        self.assertEqual(config['logging']['level'], 'INFO')
        self.assertTrue(config['models']['random_forest']['enabled'])

def run_tests():
    """Run all tests"""
    
    print("Running tests for AI-Powered Malware Detection System...")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestStaticAnalyzer))
    suite.addTests(loader.loadTestsFromTestCase(TestEnsembleDetector))
    suite.addTests(loader.loadTestsFromTestCase(TestDataPreprocessor))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
