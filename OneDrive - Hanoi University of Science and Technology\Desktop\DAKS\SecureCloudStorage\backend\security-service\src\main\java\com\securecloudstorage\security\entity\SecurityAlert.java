package com.securecloudstorage.security.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Security Alert Entity
 * Entity lưu trữ các security alerts
 */
@Entity
@Table(name = "security_alerts")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecurityAlert {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "title", nullable = false)
    private String title;

    @Column(name = "description", length = 2000)
    private String description;

    @Column(name = "severity", nullable = false)
    private String severity;

    @Column(name = "alert_type", nullable = false)
    private String alertType;

    @Column(name = "event_id")
    private Long eventId;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "priority")
    private Integer priority;

    @Column(name = "source")
    private String source;

    @Column(name = "assigned_to")
    private String assignedTo;

    @Column(name = "acknowledged_by")
    private String acknowledgedBy;

    @Column(name = "acknowledged_at")
    private LocalDateTime acknowledgedAt;

    @Column(name = "resolved_by")
    private String resolvedBy;

    @Column(name = "resolved_at")
    private LocalDateTime resolvedAt;

    @Column(name = "resolution", length = 2000)
    private String resolution;

    @Column(name = "notes", length = 2000)
    private String notes;

    @Column(name = "tags")
    private String tags;

    @Column(name = "escalated_to")
    private String escalatedTo;

    @Column(name = "escalated_at")
    private LocalDateTime escalatedAt;

    @Column(name = "sla_deadline")
    private LocalDateTime slaDeadline;

    @Column(name = "custom_data", length = 5000)
    private String customData;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        if (status == null) {
            status = "OPEN";
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
