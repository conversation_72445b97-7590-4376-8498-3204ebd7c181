package com.securecloudstorage.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Alert Response DTO
 * Response chứa thông tin alert
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlertResponse {

    private Long id;                               // ID của alert
    private String alertId;                        // Unique alert ID
    private String title;                          // Tiêu đề alert
    private String description;                    // Mô tả chi tiết
    private String severity;                       // LOW, MEDIUM, HIGH, CRITICAL
    private String status;                         // OPEN, ACKNOWLEDGED, RESOLVED, CLOSED
    private String type;                           // SYSTEM, APPLICATION, SECURITY, PERFORMANCE
    private String category;                       // Danh mục alert
    private String source;                         // Nguồn gốc alert
    private String service;                        // Service liên quan
    private String component;                      // Component liên quan
    private String host;                           // Host/server liên quan
    private String environment;                    // Environment (dev, staging, prod)
    
    // Timing information
    private LocalDateTime createdAt;               // Thời gian tạo
    private LocalDateTime updatedAt;               // Thời gian update cuối
    private LocalDateTime acknowledgedAt;          // Thời gian acknowledge
    private LocalDateTime resolvedAt;              // Thời gian resolve
    private LocalDateTime closedAt;                // Thời gian đóng
    private LocalDateTime expiresAt;               // Thời gian hết hạn
    private Long duration;                         // Thời gian kéo dài (seconds)
    
    // Assignment and ownership
    private String assignedTo;                     // Người được assign
    private String acknowledgedBy;                 // Người acknowledge
    private String resolvedBy;                     // Người resolve
    private String team;                           // Team chịu trách nhiệm
    private String escalationLevel;                // Mức độ escalation
    
    // Priority and impact
    private Integer priority;                      // Độ ưu tiên (1-5)
    private String impact;                         // LOW, MEDIUM, HIGH, CRITICAL
    private String urgency;                        // LOW, MEDIUM, HIGH, CRITICAL
    private String businessImpact;                 // Tác động đến business
    private String affectedUsers;                  // Số users bị ảnh hưởng
    
    // Technical details
    private String metricName;                     // Tên metric gây ra alert
    private String condition;                      // Điều kiện trigger alert
    private String threshold;                      // Ngưỡng trigger
    private String currentValue;                   // Giá trị hiện tại
    private String unit;                           // Đơn vị đo
    private Map<String, Object> metadata;          // Metadata bổ sung
    private Map<String, Object> context;           // Context thông tin
    private String query;                          // Query để check alert
    
    // Resolution information
    private String resolution;                     // Cách giải quyết
    private String rootCause;                      // Nguyên nhân gốc
    private String actions;                        // Hành động đã thực hiện
    private String notes;                          // Ghi chú
    private String workaround;                     // Workaround (nếu có)
    private String preventionSteps;                // Bước phòng ngừa
    
    // Notification information
    private Boolean isNotified;                    // Đã gửi notification?
    private String notificationChannels;           // Kênh notification
    private Integer notificationCount;             // Số lần gửi notification
    private LocalDateTime lastNotificationAt;      // Lần gửi cuối
    private String notificationRecipients;         // Người nhận notification
    
    // Recurrence information
    private Boolean isRecurring;                   // Alert có lặp lại?
    private Integer occurrenceCount;               // Số lần xảy ra
    private LocalDateTime firstOccurrence;         // Lần đầu xảy ra
    private LocalDateTime lastOccurrence;          // Lần cuối xảy ra
    private String recurrencePattern;              // Pattern lặp lại
    
    // Correlation and grouping
    private String correlationId;                  // ID để nhóm alerts
    private String parentAlertId;                  // Parent alert (nếu có)
    private String groupId;                        // Group ID
    private Boolean isGrouped;                     // Alert có được nhóm?
    private Integer relatedAlertCount;             // Số alerts liên quan
    
    // SLA and compliance
    private LocalDateTime slaDeadline;             // Deadline SLA
    private String slaStatus;                      // Trạng thái SLA
    private Boolean isSlaBreached;                 // SLA có bị vi phạm?
    private String complianceLevel;                // Mức độ compliance
    
    // Automation information
    private Boolean isAutoResolved;                // Tự động resolve?
    private String autoResolutionReason;           // Lý do auto resolve
    private String automationAction;               // Hành động automation
    private Boolean canAutoResolve;                // Có thể auto resolve?
    
    // External integration
    private String ticketId;                       // Ticket ID (JIRA, ServiceNow, etc.)
    private String externalId;                     // External system ID
    private String externalUrl;                    // URL external system
    private Map<String, String> externalLinks;     // Links to external systems
    
    // Tags and labels
    private String tags;                           // Tags (comma-separated)
    private String labels;                         // Labels (comma-separated)
    private String customFields;                   // Custom fields (JSON)
    
    // Statistics
    private Long viewCount;                        // Số lần xem
    private Long commentCount;                     // Số comments
    private Long attachmentCount;                  // Số attachments
    private Double avgResolutionTime;              // Thời gian resolve trung bình
    
    // Health check
    private String healthCheckUrl;                 // URL health check
    private String healthCheckStatus;              // Trạng thái health check
    private LocalDateTime lastHealthCheck;         // Lần check cuối
    
    // Forecast and prediction
    private String predictedResolution;            // Dự đoán thời gian resolve
    private String trendAnalysis;                  // Phân tích xu hướng
    private String riskAssessment;                 // Đánh giá rủi ro
    private String recommendedActions;             // Hành động khuyến nghị
}
