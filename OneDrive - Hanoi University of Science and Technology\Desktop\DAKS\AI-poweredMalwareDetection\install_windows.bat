@echo off
echo Installing AI Malware Detection System Dependencies
echo ==================================================

echo.
echo Installing basic requirements...
pip install -r requirements.txt

echo.
echo Checking optional dependencies...

echo.
echo Checking YARA...
pip install yara-python 2>nul
if %errorlevel% neq 0 (
    echo YARA installation failed. This is optional but recommended.
    echo You can install it manually later with: pip install yara-python
)

echo.
echo Checking python-magic...
pip install python-magic 2>nul
if %errorlevel% neq 0 (
    echo python-magic installation failed. This is optional.
    echo Alternative file type detection will be used.
)

echo.
echo Installing python-magic-bin (Windows alternative)...
pip install python-magic-bin 2>nul
if %errorlevel% neq 0 (
    echo python-magic-bin installation failed. This is optional.
)

echo.
echo Setup complete!
echo You can now run: python main.py --train
pause
