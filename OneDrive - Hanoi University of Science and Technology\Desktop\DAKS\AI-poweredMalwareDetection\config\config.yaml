# Configuration settings
logging:
  level: INFO
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  file: 'logs/malware_detection.log'

# Model settings
models:
  random_forest:
    enabled: true
    n_estimators: 100
    max_depth: 20
    random_state: 42
  
  svm:
    enabled: true
    kernel: 'rbf'
    C: 1.0
    gamma: 'scale'
  
  neural_network:
    enabled: true
    hidden_layers: [128, 64, 32]
    activation: 'relu'
    optimizer: 'adam'
    epochs: 50
    batch_size: 32
  
  xgboost:
    enabled: true
    n_estimators: 100
    max_depth: 6
    learning_rate: 0.1

# Feature extraction settings
features:
  pe_features:
    enabled: true
    import_table: true
    export_table: true
    sections: true
    headers: true
    strings: true
    entropy: true
  
  static_features:
    enabled: true
    file_size: true
    file_type: true
    hash_values: true
    yara_rules: true
  
  dynamic_features:
    enabled: false
    api_calls: true
    network_activity: true
    file_operations: true
    registry_changes: true
    timeout: 60  # seconds

# Analysis settings
analysis:
  static:
    enabled: true
    timeout: 30
  
  dynamic:
    enabled: false
    sandbox_path: 'sandbox/'
    timeout: 300
    vm_snapshot: 'clean_snapshot'

# Monitoring settings
monitoring:
  enabled: true
  watch_directories:
    - 'C:\\Users\\<USER>\\Downloads'
    - 'C:\\Temp'
  
  quarantine_directory: 'quarantine/'
  scan_interval: 5  # seconds
  
  real_time:
    enabled: true
    file_extensions:
      - '.exe'
      - '.dll'
      - '.scr'
      - '.bat'
      - '.ps1'
      - '.jar'
      - '.py'

# Database settings
database:
  type: 'sqlite'
  path: 'data/malware_detection.db'
  
# API settings
api:
  virustotal:
    enabled: false
    api_key: ''
    rate_limit: 4  # requests per minute
  
  hybrid_analysis:
    enabled: false
    api_key: ''
    rate_limit: 10

# Threat intelligence
threat_intelligence:
  enabled: true
  update_interval: 3600  # seconds
  sources:
    - 'https://rules.emergingthreats.net/open/snort-2.9.0/rules/'
    - 'https://github.com/Yara-Rules/rules'

# Performance settings
performance:
  max_file_size: 104857600  # 100MB
  max_concurrent_scans: 4
  cache_enabled: true
  cache_size: 1000
