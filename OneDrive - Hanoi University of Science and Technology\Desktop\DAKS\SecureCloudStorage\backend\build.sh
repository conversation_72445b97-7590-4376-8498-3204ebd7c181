#!/bin/bash

# Build script for Secure Cloud Storage Backend
# Builds all microservices and starts the system

set -e

echo "======================================"
echo "Building Secure Cloud Storage Backend"
echo "======================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "Error: Docker Compose is not installed"
    exit 1
fi

# Create necessary directories
echo "Creating directories..."
mkdir -p uploads quarantine logs/{gateway,storage,user,security,monitoring,ai-malware,network-ids}

# Build API Gateway
echo "Building API Gateway..."
cd api-gateway
./mvnw clean package -DskipTests
cd ..

# Build Storage Service
echo "Building Storage Service..."
cd storage-service
./mvnw clean package -DskipTests
cd ..

# Build User Service
echo "Building User Service..."
cd user-service
./mvnw clean package -DskipTests
cd ..

# Build Security Service (if exists)
if [ -d "security-service" ]; then
    echo "Building Security Service..."
    cd security-service
    ./mvnw clean package -DskipTests
    cd ..
fi

# Build Monitoring Service (if exists)
if [ -d "monitoring-service" ]; then
    echo "Building Monitoring Service..."
    cd monitoring-service
    ./mvnw clean package -DskipTests
    cd ..
fi

# Create Docker network
echo "Creating Docker network..."
docker network create secure-cloud-network 2>/dev/null || true

# Build and start services
echo "Starting services with Docker Compose..."
echo "Starting MariaDB database..."
docker-compose up -d mariadb

echo "Waiting for MariaDB to be ready..."
sleep 30

echo "Starting other services..."
docker-compose up --build -d

echo "======================================"
echo "Services starting up..."
echo "======================================"

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 30

# Check service health
echo "Checking service health..."
services=("api-gateway:8080" "storage-service:8082" "user-service:8083")

for service in "${services[@]}"; do
    name=$(echo $service | cut -d':' -f1)
    port=$(echo $service | cut -d':' -f2)
    
    echo "Checking $name on port $port..."
    
    # Wait up to 60 seconds for service to be ready
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s -f http://localhost:$port/actuator/health > /dev/null 2>&1; then
            echo "✓ $name is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        echo "⚠ $name is not ready (timeout)"
    fi
done

# Check Python services
echo "Checking Python services..."
if curl -s -f http://localhost:5000/api/stats > /dev/null 2>&1; then
    echo "✓ Network IDS is ready"
else
    echo "⚠ Network IDS is not ready"
fi

if curl -s -f http://localhost:8086/api/health > /dev/null 2>&1; then
    echo "✓ AI-Malware Detection is ready"
else
    echo "⚠ AI-Malware Detection is not ready"
fi

echo "======================================"
echo "Build and deployment completed!"
echo "======================================"
echo ""
echo "Services running:"
echo "- API Gateway: http://localhost:8080"
echo "- Storage Service: http://localhost:8082"
echo "- User Service: http://localhost:8083"
echo "- Security Service: http://localhost:8084"
echo "- Monitoring Service: http://localhost:8085"
echo "- AI-Malware Detection: http://localhost:8086"
echo "- Network IDS: http://localhost:5000"
echo "- Prometheus: http://localhost:9090"
echo "- Grafana: http://localhost:3000 (admin/admin)"
echo ""
echo "To check logs: docker-compose logs -f [service-name]"
echo "To stop: docker-compose down"
echo "To rebuild: docker-compose down && docker-compose up --build -d"
