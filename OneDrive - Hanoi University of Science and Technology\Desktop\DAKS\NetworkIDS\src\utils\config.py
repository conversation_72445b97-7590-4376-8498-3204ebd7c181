"""
Configuration Manager for Network IDS
====================================

Module quản lý cấu hình hệ thống
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional

class Config:
    """Lớp quản lý cấu hình hệ thống"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        Khởi tạo configuration manager
        
        Args:
            config_path (str): Đường dẫn đến file cấu hình
        """
        self.config_path = Path(config_path)
        self.config_data = {}
        self.load_config()
    
    def load_config(self) -> None:
        """Tải cấu hình từ file YAML"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config_data = yaml.safe_load(f)
            else:
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        except Exception as e:
            raise Exception(f"Error loading configuration: {str(e)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Lấy giá trị cấu hình
        
        Args:
            key (str): Key cấu hình (hỗ trợ dot notation)
            default: Giá trị mặc định
            
        Returns:
            Giá trị cấu hình
        """
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """
        Cài đặt giá trị cấu hình
        
        Args:
            key (str): Key cấu hình
            value: Giá trị cần cài đặt
        """
        keys = key.split('.')
        config = self.config_data
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self) -> None:
        """Lưu cấu hình vào file"""
        try:
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, indent=2)
        except Exception as e:
            raise Exception(f"Error saving configuration: {str(e)}")
    
    def get_log_config(self) -> Dict[str, Any]:
        """Lấy cấu hình logging"""
        return self.get('logging', {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file': 'logs/network_ids.log',
            'max_size': '100MB',
            'backup_count': 5,
            'console': True
        })
    
    def get_capture_config(self) -> Dict[str, Any]:
        """Lấy cấu hình packet capture"""
        return self.get('capture', {
            'interface': 'auto',
            'promiscuous': True,
            'buffer_size': 65536,
            'timeout': 1000,
            'filter': '',
            'max_packets': 0,
            'protocols': ['tcp', 'udp', 'icmp', 'arp']
        })
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """Lấy cấu hình traffic analysis"""
        return self.get('analysis', {
            'window_size': 60,
            'features': [
                'packet_size', 'inter_arrival_time', 'protocol_distribution',
                'port_distribution', 'connection_duration', 'byte_frequency',
                'packet_count', 'flow_duration'
            ],
            'aggregation_interval': 10,
            'baseline_period': 300
        })
    
    def get_ml_config(self) -> Dict[str, Any]:
        """Lấy cấu hình machine learning"""
        return self.get('ml', {
            'models': {
                'isolation_forest': {
                    'enabled': True,
                    'contamination': 0.1,
                    'n_estimators': 100,
                    'max_samples': 256
                },
                'autoencoder': {
                    'enabled': True,
                    'encoding_dim': 32,
                    'epochs': 50,
                    'batch_size': 32,
                    'validation_split': 0.2
                },
                'lstm': {
                    'enabled': True,
                    'sequence_length': 10,
                    'hidden_units': 50,
                    'epochs': 30,
                    'batch_size': 32
                },
                'random_forest': {
                    'enabled': True,
                    'n_estimators': 100,
                    'max_depth': 10,
                    'min_samples_split': 2
                }
            },
            'training': {
                'data_path': 'data/training/',
                'model_path': 'models/',
                'retrain_interval': 86400,
                'min_samples': 1000
            },
            'detection': {
                'threshold': 0.7,
                'confidence_threshold': 0.8,
                'ensemble_voting': True
            }
        })
    
    def get_signature_config(self) -> Dict[str, Any]:
        """Lấy cấu hình signature detection"""
        return self.get('signature', {
            'enabled': True,
            'rules_path': 'rules/',
            'update_interval': 3600,
            'rules': [
                'ddos_detection', 'port_scan_detection', 'sql_injection',
                'xss_detection', 'malware_communication', 'brute_force_detection',
                'arp_spoofing', 'dns_poisoning'
            ]
        })
    
    def get_alert_config(self) -> Dict[str, Any]:
        """Lấy cấu hình alerts"""
        return self.get('alerts', {
            'enabled': True,
            'channels': {
                'email': {
                    'enabled': True,
                    'smtp_server': 'smtp.gmail.com',
                    'smtp_port': 587,
                    'username': '',
                    'password': '',
                    'recipients': []
                }
            },
            'severity_levels': {
                'LOW': {'throttle': 300, 'channels': ['email']},
                'MEDIUM': {'throttle': 60, 'channels': ['email']},
                'HIGH': {'throttle': 0, 'channels': ['email']},
                'CRITICAL': {'throttle': 0, 'channels': ['email']}
            }
        })
    
    def get_dashboard_config(self) -> Dict[str, Any]:
        """Lấy cấu hình dashboard"""
        return self.get('dashboard', {
            'enabled': True,
            'host': '0.0.0.0',
            'port': 8080,
            'debug': False,
            'secret_key': 'your_secret_key_here',
            'authentication': {
                'enabled': True,
                'users': {
                    'admin': {'password': 'admin123', 'role': 'admin'}
                }
            },
            'features': {
                'real_time_monitoring': True,
                'historical_analysis': True,
                'threat_intelligence': True,
                'system_health': True,
                'configuration_management': True
            },
            'refresh_rate': 5
        })
    
    def get_database_config(self) -> Dict[str, Any]:
        """Lấy cấu hình database"""
        return self.get('database', {
            'type': 'sqlite',
            'path': 'data/network_ids.db',
            'pool_size': 20,
            'max_overflow': 30,
            'pool_timeout': 30
        })
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Lấy cấu hình performance"""
        return self.get('performance', {
            'max_memory_usage': 2048,
            'max_cpu_usage': 80,
            'packet_queue_size': 10000,
            'analysis_queue_size': 1000,
            'worker_threads': 4,
            'cache_size': 1000,
            'health_check_interval': 30,
            'performance_log_interval': 300
        })
    
    def get_security_config(self) -> Dict[str, Any]:
        """Lấy cấu hình security"""
        return self.get('security', {
            'encryption': {
                'enabled': True,
                'algorithm': 'AES-256',
                'key_file': 'config/encryption.key'
            },
            'api_security': {
                'rate_limiting': True,
                'max_requests_per_minute': 100,
                'authentication_required': True
            },
            'data_retention': {
                'packets': 7,
                'alerts': 30,
                'logs': 90,
                'reports': 365
            }
        })
    
    def get_threat_intelligence_config(self) -> Dict[str, Any]:
        """Lấy cấu hình threat intelligence"""
        return self.get('threat_intelligence', {
            'enabled': True,
            'sources': [],
            'local_sources': []
        })
    
    def get_network_config(self) -> Dict[str, Any]:
        """Lấy cấu hình network"""
        return self.get('network', {
            'interfaces': [],
            'subnets': {
                'internal': ['192.168.1.0/24', '10.0.0.0/8', '172.16.0.0/12'],
                'dmz': ['192.168.100.0/24'],
                'external': ['0.0.0.0/0']
            },
            'ports': {
                'monitored': [22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 1433, 3306, 3389, 5432],
                'high_priority': [22, 443, 3389]
            }
        })
    
    def validate_config(self) -> bool:
        """
        Kiểm tra tính hợp lệ của cấu hình
        
        Returns:
            bool: True nếu cấu hình hợp lệ
        """
        required_sections = [
            'logging', 'capture', 'analysis', 'ml', 'signature', 
            'alerts', 'dashboard', 'database', 'performance'
        ]
        
        for section in required_sections:
            if section not in self.config_data:
                return False
        
        return True
    
    def __str__(self) -> str:
        """String representation"""
        return f"Config(path={self.config_path}, sections={list(self.config_data.keys())})"
