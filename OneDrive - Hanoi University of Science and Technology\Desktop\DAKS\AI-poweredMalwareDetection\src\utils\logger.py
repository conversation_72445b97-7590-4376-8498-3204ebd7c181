"""
Logging utility for malware detection system
"""

import logging
import os
from pathlib import Path
from colorama import init, Fore, Style

# Initialize colorama
init(autoreset=True)

def setup_logging(verbose=False, log_file=None):
    """Setup logging configuration"""
    
    # Create logs directory if it doesn't exist
    Path('logs').mkdir(exist_ok=True)
    
    # Set log level
    level = logging.DEBUG if verbose else logging.INFO
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Console handler with colors
    console_handler = ColoredConsoleHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler
    log_file = log_file or 'logs/malware_detection.log'
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    return root_logger

class ColoredConsoleHandler(logging.StreamHandler):
    """Console handler with colored output"""
    
    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.MAGENTA
    }
    
    def format(self, record):
        """Format log record with colors"""
        log_color = self.COLORS.get(record.levelname, '')
        record.levelname = f"{log_color}{record.levelname}{Style.RESET_ALL}"
        return super().format(record)

def get_logger(name):
    """Get logger with specified name"""
    return logging.getLogger(name)

def log_analysis_result(logger, file_path, prediction, confidence, threats=None):
    """Log analysis result in a standardized format"""
    
    if prediction == 'malware':
        logger.warning(f"MALWARE DETECTED: {file_path} (confidence: {confidence:.2f})")
        if threats:
            for threat in threats:
                logger.warning(f"  - Threat: {threat}")
    else:
        logger.info(f"CLEAN: {file_path} (confidence: {confidence:.2f})")

def log_performance_metrics(logger, metrics):
    """Log performance metrics"""
    
    logger.info("Performance Metrics:")
    for metric, value in metrics.items():
        logger.info(f"  {metric}: {value}")

def log_error_with_traceback(logger, error, context=""):
    """Log error with traceback information"""
    
    import traceback
    
    logger.error(f"Error {context}: {str(error)}")
    logger.debug(f"Traceback:\n{traceback.format_exc()}")

def log_system_info(logger):
    """Log system information"""
    
    import platform
    import psutil
    
    logger.info("System Information:")
    logger.info(f"  OS: {platform.system()} {platform.release()}")
    logger.info(f"  Python: {platform.python_version()}")
    logger.info(f"  CPU: {psutil.cpu_count()} cores")
    logger.info(f"  Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB")
    logger.info(f"  Disk: {psutil.disk_usage('/').total / (1024**3):.1f} GB")
