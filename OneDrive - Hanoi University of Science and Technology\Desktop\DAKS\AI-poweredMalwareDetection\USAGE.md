# AI-Powered Malware Detection System

## Hướng dẫn sử dụng

### 1. Cài đặt
```bash
# Cài đặt dependencies cơ bản
pip install -r requirements.txt

# Hoặc trên Windows, sử dụng batch file:
install_windows.bat

# Chạy setup script
python setup.py
```

**Lưu ý cho Windows:**
- Một số dependencies như `python-magic` và `yara-python` có thể khó cài đặt trên Windows
- <PERSON><PERSON> thống sẽ tự động sử dụng các alternatives nếu không có sẵn
- Chạy `install_windows.bat` để cài đặt tự động với error handling

### 2. Huấn luyện mô hình
```bash
# Tạo dữ liệu mẫu và huấn luyện
python main.py --train
```

### 3. <PERSON><PERSON> tích tệp tin
```bash
# Phân tích một tệp tin
python main.py --file C:\path\to\file.exe

# Phân tích thư mục
python main.py --directory C:\path\to\directory
```

### 4. Giám sát thời gian thực
```bash
# Bắt đầu giám sát
python main.py --monitor
```

### 5. Chạy demo
```bash
# Chạy demo hoàn chỉnh
python demo.py
```

### 6. Chạy tests
```bash
# Chạy unit tests
python -m pytest tests/

# Hoặc chạy trực tiếp
python tests/test_malware_detection.py
```

## Tính năng chính

### Static Analysis
- Phân tích tệp PE (Portable Executable)
- Trích xuất features từ headers, sections, imports/exports
- Phân tích entropy và strings
- YARA rule matching
- Hash calculation

### Dynamic Analysis (Tùy chọn)
- Sandbox execution
- API call monitoring
- Network activity tracking
- Registry changes monitoring
- Process behavior analysis

### Machine Learning Models
- Random Forest
- Support Vector Machine (SVM)
- Neural Network (TensorFlow)
- XGBoost
- Ensemble voting

### Real-time Monitoring
- File system monitoring
- Automatic scanning
- Quarantine system
- Alert notifications
- Performance metrics

## Cấu hình

Chỉnh sửa `config/config.yaml` để:
- Bật/tắt các mô hình ML
- Cấu hình thư mục giám sát
- Thiết lập dynamic analysis
- Cấu hình logging

## Cấu trúc dữ liệu

```
data/
├── samples/          # Mẫu malware và clean files
├── datasets/         # Datasets for training
├── training_data.csv # Generated training data
└── yara_rules/       # YARA rules
```

## Logs

```
logs/
├── malware_detection.log    # Main log file
├── dynamic_analysis/        # Dynamic analysis logs
└── detections/             # Malware detection logs
```

## Quarantine

Tệp tin malware được phát hiện sẽ được tự động chuyển vào thư mục `quarantine/`.

## Troubleshooting

### Lỗi thường gặp:
1. **ModuleNotFoundError**: Chạy `pip install -r requirements.txt` hoặc `install_windows.bat`
2. **ImportError: failed to find libmagic**: Trên Windows, cài đặt `python-magic-bin` hoặc để hệ thống dùng alternative
3. **YARA rules not found**: Kiểm tra thư mục `data/yara_rules/` hoặc để YARA bị disable
4. **Training data missing**: Chạy `python setup.py` để tạo dữ liệu mẫu
5. **Permission denied**: Chạy với quyền Administrator
6. **TensorFlow issues**: Cài đặt phiên bản compatible: `pip install tensorflow==2.10.0`

### Performance Tips:
- Giảm số lượng models trong config để tăng tốc độ
- Sử dụng SSD để cải thiện I/O
- Tăng RAM cho việc training

## Mở rộng

### Thêm model mới:
1. Tạo class trong `src/models/`
2. Cập nhật `ensemble_model.py`
3. Thêm config vào `config.yaml`

### Thêm features mới:
1. Cập nhật `static_analyzer.py` hoặc `dynamic_analyzer.py`
2. Thêm vào `_get_expected_features()` trong ensemble model
3. Retrain models

### Tích hợp API:
- VirusTotal API
- Hybrid Analysis API
- Custom threat intelligence feeds

## Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## Liên hệ

- Email: <EMAIL>
- GitHub: [repository-link]
- Documentation: [docs-link]
