package com.securecloudstorage.storage.repository;

import com.securecloudstorage.storage.entity.FileEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * File Repository for MariaDB
 */
@Repository
public interface FileRepository extends JpaRepository<FileEntity, Long> {
    
    Optional<FileEntity> findByFileId(String fileId);
    
    List<FileEntity> findByUserIdOrderByUploadDateDesc(String userId);
    
    Page<FileEntity> findByUserIdOrderByUploadDateDesc(String userId, Pageable pageable);
    
    List<FileEntity> findByUserIdAndQuarantined(String userId, boolean quarantined);
    
    List<FileEntity> findByQuarantinedTrue();
    
    List<FileEntity> findByScanStatus(FileEntity.ScanStatus scanStatus);
    
    @Query("SELECT f FROM FileEntity f WHERE f.userId = :userId AND f.fileName LIKE %:fileName%")
    List<FileEntity> findByUserIdAndFileNameContaining(@Param("userId") String userId, 
                                                      @Param("fileName") String fileName);
    
    @Query("SELECT f FROM FileEntity f WHERE f.uploadDate BETWEEN :startDate AND :endDate")
    List<FileEntity> findByUploadDateBetween(@Param("startDate") LocalDateTime startDate,
                                           @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(f) FROM FileEntity f WHERE f.userId = :userId")
    Long countByUserId(@Param("userId") String userId);
    
    @Query("SELECT SUM(f.fileSize) FROM FileEntity f WHERE f.userId = :userId")
    Long sumFileSizeByUserId(@Param("userId") String userId);
    
    @Query("SELECT f FROM FileEntity f WHERE f.expiryDate < :currentDate AND f.expiryDate IS NOT NULL")
    List<FileEntity> findExpiredFiles(@Param("currentDate") LocalDateTime currentDate);
    
    @Query("SELECT f FROM FileEntity f WHERE f.tags LIKE %:tag%")
    List<FileEntity> findByTagsContaining(@Param("tag") String tag);
    
    @Query("SELECT f FROM FileEntity f WHERE f.isPublic = true")
    List<FileEntity> findPublicFiles();
    
    @Query("SELECT f FROM FileEntity f WHERE f.lastAccessed < :date")
    List<FileEntity> findInactiveFiles(@Param("date") LocalDateTime date);
    
    boolean existsByFileId(String fileId);
    
    void deleteByFileId(String fileId);
    
    @Query("SELECT f.contentType, COUNT(f) FROM FileEntity f GROUP BY f.contentType")
    List<Object[]> getFileTypeStatistics();
    
    @Query("SELECT DATE(f.uploadDate), COUNT(f) FROM FileEntity f GROUP BY DATE(f.uploadDate) ORDER BY DATE(f.uploadDate)")
    List<Object[]> getUploadStatistics();
}
