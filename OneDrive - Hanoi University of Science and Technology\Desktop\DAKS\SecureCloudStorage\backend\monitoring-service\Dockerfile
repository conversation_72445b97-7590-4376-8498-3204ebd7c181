FROM openjdk:17-jdk-slim

LABEL maintainer="SecureCloudStorage Team"
LABEL description="Monitoring Service for SecureCloudStorage Platform"
LABEL version="1.0.0"

# Set working directory
WORKDIR /app

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install required packages
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    telnet \
    dnsutils \
    procps \
    && rm -rf /var/lib/apt/lists/*

# Create directories
RUN mkdir -p /app/logs && \
    mkdir -p /app/config && \
    mkdir -p /app/tmp && \
    chown -R appuser:appuser /app

# Copy application JAR
COPY target/monitoring-service-1.0.0.jar /app/monitoring-service.jar

# Copy configuration files
COPY src/main/resources/application.yml /app/config/
COPY src/main/resources/application-prod.yml /app/config/

# Set permissions
RUN chown -R appuser:appuser /app && \
    chmod +x /app/monitoring-service.jar

# Switch to app user
USER appuser

# Expose port
EXPOSE 8085

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8085/monitoring/actuator/health || exit 1

# JVM settings
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication -XX:+OptimizeStringConcat -Djava.security.egd=file:/dev/./urandom"

# Application settings
ENV SPRING_PROFILES_ACTIVE=prod
ENV SPRING_CONFIG_LOCATION=/app/config/
ENV LOGGING_FILE_PATH=/app/logs/

# Run the application
CMD ["sh", "-c", "java $JAVA_OPTS -jar /app/monitoring-service.jar"]
