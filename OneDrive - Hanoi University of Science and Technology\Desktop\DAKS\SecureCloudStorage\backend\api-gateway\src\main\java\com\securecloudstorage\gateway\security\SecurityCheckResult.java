package com.securecloudstorage.gateway.security;

import lombok.Builder;
import lombok.Data;

/**
 * Security Check Result
 */
@Data
@Builder
public class SecurityCheckResult {
    private boolean allowed;
    private String reason;
    private double score;
    private String threatType;
    private long timestamp;
    
    public static SecurityCheckResult allow() {
        return SecurityCheckResult.builder()
            .allowed(true)
            .timestamp(System.currentTimeMillis())
            .build();
    }
    
    public static SecurityCheckResult deny(String reason) {
        return SecurityCheckResult.builder()
            .allowed(false)
            .reason(reason)
            .timestamp(System.currentTimeMillis())
            .build();
    }
}
