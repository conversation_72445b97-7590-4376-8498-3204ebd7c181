package com.securecloudstorage.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Dashboard Response DTO
 * Response chứa dữ liệu tổng quan cho dashboard
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DashboardResponse {

    // System Overview
    private String systemStatus;                   // HEALTHY, WARNING, CRITICAL
    private Double overallHealth;                  // 0-100
    private LocalDateTime lastUpdated;             // Thời gian update cuối
    private String environment;                    // Environment hiện tại
    private String version;                        // Version hệ thống
    private Long totalUptime;                      // Tổng uptime (seconds)
    
    // Service Status Summary
    private Integer totalServices;                 // Tổng số services
    private Integer healthyServices;               // Số services healthy
    private Integer warningServices;               // Số services warning
    private Integer criticalServices;              // Số services critical
    private Integer downServices;                  // Số services down
    private List<ServiceStatusSummary> services;   // Trạng thái từng service
    
    // Alerts Summary
    private Integer totalAlerts;                   // Tổng số alerts
    private Integer criticalAlerts;                // Số alerts critical
    private Integer highAlerts;                    // Số alerts high
    private Integer mediumAlerts;                  // Số alerts medium
    private Integer lowAlerts;                     // Số alerts low
    private Integer openAlerts;                    // Số alerts đang mở
    private Integer acknowledgedAlerts;            // Số alerts đã acknowledge
    private List<AlertSummary> recentAlerts;       // Alerts gần đây
    
    // Performance Metrics
    private Double avgResponseTime;                // Thời gian phản hồi trung bình
    private Long totalRequests;                    // Tổng số requests
    private Long requestsPerSecond;                // Requests per second
    private Double errorRate;                      // Tỷ lệ lỗi
    private Double throughput;                     // Throughput
    private Double availability;                   // Availability percentage
    
    // Resource Usage
    private Double cpuUsage;                       // CPU usage tổng
    private Double memoryUsage;                    // Memory usage tổng
    private Double diskUsage;                      // Disk usage tổng
    private Double networkUsage;                   // Network usage
    private List<ResourceMetric> resourceMetrics;  // Metrics chi tiết
    
    // Database Status
    private Integer totalDatabases;                // Tổng số databases
    private Integer healthyDatabases;              // Số databases healthy
    private Integer warningDatabases;              // Số databases warning
    private Integer criticalDatabases;             // Số databases critical
    private List<DatabaseStatus> databaseStatus;   // Trạng thái databases
    
    // Security Summary
    private Integer securityAlerts;                // Số security alerts
    private Integer threatsDetected;               // Số threats detected
    private Integer blockedIPs;                    // Số IPs bị block
    private Integer securityEvents;                // Số security events
    private Double securityScore;                  // Điểm security (0-100)
    private List<SecurityMetric> securityMetrics;  // Metrics security
    
    // Network Status
    private Integer totalNetworkDevices;           // Tổng số network devices
    private Integer healthyNetworkDevices;         // Số devices healthy
    private Integer warningNetworkDevices;         // Số devices warning
    private Integer criticalNetworkDevices;        // Số devices critical
    private Double networkLatency;                 // Network latency
    private Double networkThroughput;              // Network throughput
    
    // Application Metrics
    private Integer totalApplications;             // Tổng số applications
    private Integer runningApplications;           // Số apps đang chạy
    private Integer stoppedApplications;           // Số apps đã dừng
    private Integer failedApplications;            // Số apps lỗi
    private List<ApplicationMetric> applicationMetrics; // Metrics applications
    
    // Capacity Planning
    private Map<String, Object> capacityData;      // Dữ liệu capacity planning
    private List<CapacityForecast> forecasts;      // Dự báo capacity
    private List<String> recommendations;          // Khuyến nghị
    
    // Trends and Analytics
    private List<TrendData> performanceTrends;     // Xu hướng performance
    private List<TrendData> usageTrends;           // Xu hướng usage
    private List<TrendData> errorTrends;           // Xu hướng errors
    private Map<String, Object> analytics;         // Phân tích dữ liệu
    
    // SLA and Compliance
    private Double slaCompliance;                  // Tuân thủ SLA (%)
    private Integer slaViolations;                 // Số vi phạm SLA
    private List<SLAMetric> slaMetrics;            // Metrics SLA
    private String complianceStatus;               // Trạng thái compliance
    
    // Recent Activities
    private List<ActivityLog> recentActivities;    // Hoạt động gần đây
    private List<SystemEvent> recentEvents;        // Events gần đây
    private List<String> recentChanges;            // Thay đổi gần đây
    
    // Configuration
    private Map<String, Object> configuration;     // Cấu hình hệ thống
    private Map<String, String> environmentVars;   // Environment variables
    private List<String> enabledFeatures;          // Features đã enable
    
    // Nested classes
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceStatusSummary {
        private String serviceName;
        private String status;
        private String health;
        private Double responseTime;
        private String version;
        private LocalDateTime lastCheck;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlertSummary {
        private String alertId;
        private String title;
        private String severity;
        private String service;
        private LocalDateTime createdAt;
        private String status;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResourceMetric {
        private String resourceType;
        private Double usage;
        private Double capacity;
        private String unit;
        private String status;
        private LocalDateTime timestamp;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DatabaseStatus {
        private String databaseName;
        private String status;
        private Integer connections;
        private Double responseTime;
        private String version;
        private LocalDateTime lastCheck;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SecurityMetric {
        private String metricName;
        private Object value;
        private String unit;
        private String severity;
        private LocalDateTime timestamp;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplicationMetric {
        private String applicationName;
        private String status;
        private Double cpuUsage;
        private Long memoryUsage;
        private Integer threadCount;
        private LocalDateTime lastCheck;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CapacityForecast {
        private String resourceType;
        private Double currentUsage;
        private Double predictedUsage;
        private LocalDateTime forecastDate;
        private String recommendation;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendData {
        private String metricName;
        private List<DataPoint> dataPoints;
        private String trend;
        private String analysis;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataPoint {
        private LocalDateTime timestamp;
        private Double value;
        private String label;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SLAMetric {
        private String slaName;
        private Double target;
        private Double current;
        private String status;
        private LocalDateTime lastCalculated;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityLog {
        private String activity;
        private String user;
        private String service;
        private LocalDateTime timestamp;
        private String status;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemEvent {
        private String eventType;
        private String description;
        private String severity;
        private String source;
        private LocalDateTime timestamp;
    }
}
