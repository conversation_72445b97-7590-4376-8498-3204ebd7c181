package com.securecloudstorage.security.service;

import com.securecloudstorage.security.entity.SecurityEvent;
import com.securecloudstorage.security.entity.BlockedIp;
import com.securecloudstorage.security.entity.ThreatIntelligence;
import com.securecloudstorage.security.repository.BlockedIpRepository;
import com.securecloudstorage.security.repository.ThreatIntelligenceRepository;
import com.securecloudstorage.security.repository.SecurityEventRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * Threat Detection Service
 * Dịch vụ phát hiện và phân tích threats
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ThreatDetectionService {

    private final BlockedIpRepository blockedIpRepository;
    private final ThreatIntelligenceRepository threatIntelligenceRepository;
    private final SecurityEventRepository eventRepository;
    private final RestTemplate restTemplate;
    private final NotificationService notificationService;

    // Cache for threat intelligence
    private final Map<String, Map<String, Object>> threatCache = new ConcurrentHashMap<>();
    
    // IP pattern for validation
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");

    /**
     * Phân tích IP address
     */
    public Map<String, Object> analyzeIp(String ipAddress) {
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            // Validate IP format
            if (!isValidIp(ipAddress)) {
                analysis.put("isValid", false);
                analysis.put("message", "Invalid IP address format");
                return analysis;
            }
            
            analysis.put("isValid", true);
            analysis.put("ipAddress", ipAddress);
            
            // Check if IP is blocked
            boolean isBlocked = blockedIpRepository.existsByIpAddress(ipAddress);
            analysis.put("isBlocked", isBlocked);
            
            // Check threat intelligence
            Map<String, Object> threatIntel = getThreatIntelligence(ipAddress);
            analysis.putAll(threatIntel);
            
            // Analyze IP patterns
            Map<String, Object> patterns = analyzeIpPatterns(ipAddress);
            analysis.putAll(patterns);
            
            // Check geolocation
            Map<String, Object> geolocation = getGeolocation(ipAddress);
            analysis.put("geolocation", geolocation);
            
            // Calculate risk score
            int riskScore = calculateRiskScore(analysis);
            analysis.put("riskScore", riskScore);
            analysis.put("threatLevel", getThreatLevel(riskScore));
            
            // Check against known threat feeds
            boolean isThreat = checkThreatFeeds(ipAddress);
            analysis.put("isThreat", isThreat);
            
            log.info("IP analysis completed for {}: risk={}, threat={}", 
                ipAddress, riskScore, isThreat);
            
            return analysis;
            
        } catch (Exception e) {
            log.error("Error analyzing IP {}: ", ipAddress, e);
            analysis.put("error", "Failed to analyze IP");
            return analysis;
        }
    }

    /**
     * Lấy danh sách IPs bị block
     */
    public List<String> getBlockedIps() {
        try {
            List<BlockedIp> blockedIps = blockedIpRepository.findByIsActiveTrue();
            return blockedIps.stream()
                .map(BlockedIp::getIpAddress)
                .toList();
        } catch (Exception e) {
            log.error("Error getting blocked IPs: ", e);
            return new ArrayList<>();
        }
    }

    /**
     * Block IP address
     */
    @Transactional
    public void blockIp(String ipAddress, String reason) {
        try {
            if (!isValidIp(ipAddress)) {
                throw new IllegalArgumentException("Invalid IP address format");
            }
            
            // Check if already blocked
            if (blockedIpRepository.existsByIpAddress(ipAddress)) {
                log.warn("IP {} is already blocked", ipAddress);
                return;
            }
            
            // Create blocked IP record
            BlockedIp blockedIp = new BlockedIp();
            blockedIp.setIpAddress(ipAddress);
            blockedIp.setReason(reason);
            blockedIp.setBlockedAt(LocalDateTime.now());
            blockedIp.setIsActive(true);
            blockedIp.setBlockedBy("SYSTEM");
            
            blockedIpRepository.save(blockedIp);
            
            // Send notification
            notificationService.sendIpBlockedNotification(ipAddress, reason);
            
            log.info("IP {} blocked successfully: {}", ipAddress, reason);
            
        } catch (Exception e) {
            log.error("Error blocking IP {}: ", ipAddress, e);
            throw new RuntimeException("Failed to block IP", e);
        }
    }

    /**
     * Unblock IP address
     */
    @Transactional
    public void unblockIp(String ipAddress) {
        try {
            BlockedIp blockedIp = blockedIpRepository.findByIpAddress(ipAddress);
            if (blockedIp != null) {
                blockedIp.setIsActive(false);
                blockedIp.setUnblockedAt(LocalDateTime.now());
                blockedIp.setUnblockedBy("SYSTEM");
                
                blockedIpRepository.save(blockedIp);
                
                log.info("IP {} unblocked successfully", ipAddress);
            } else {
                log.warn("IP {} not found in blocked list", ipAddress);
            }
            
        } catch (Exception e) {
            log.error("Error unblocking IP {}: ", ipAddress, e);
            throw new RuntimeException("Failed to unblock IP", e);
        }
    }

    /**
     * Lấy số lượng IPs bị block
     */
    public Long getBlockedIpCount() {
        try {
            return blockedIpRepository.countByIsActiveTrue();
        } catch (Exception e) {
            log.error("Error getting blocked IP count: ", e);
            return 0L;
        }
    }

    /**
     * Phân tích threat patterns
     */
    public void analyzeThreatPatterns(SecurityEvent event) {
        try {
            // Analyze brute force patterns
            analyzeBruteForcePatterns(event);
            
            // Analyze suspicious login patterns
            analyzeSuspiciousLoginPatterns(event);
            
            // Analyze file access patterns
            analyzeFileAccessPatterns(event);
            
            // Analyze API abuse patterns
            analyzeApiAbusePatterns(event);
            
            // Analyze geolocation anomalies
            analyzeGeolocationAnomalies(event);
            
        } catch (Exception e) {
            log.error("Error analyzing threat patterns: ", e);
        }
    }

    /**
     * Lấy threat intelligence
     */
    public Map<String, Object> getThreatIntelligence() {
        Map<String, Object> intelligence = new HashMap<>();
        
        try {
            // Get active threats
            List<ThreatIntelligence> activeThreats = threatIntelligenceRepository
                .findByIsActiveTrue();
            
            intelligence.put("totalThreats", activeThreats.size());
            intelligence.put("activeThreats", activeThreats);
            
            // Get blocked IPs summary
            long blockedIpCount = blockedIpRepository.countByIsActiveTrue();
            intelligence.put("blockedIpCount", blockedIpCount);
            
            // Get recent threat events
            List<SecurityEvent> recentThreats = eventRepository
                .findTop20ByThreatLevelNotNullOrderByTimestampDesc();
            intelligence.put("recentThreats", recentThreats);
            
            // Get threat statistics
            Map<String, Long> threatStats = getThreatStatistics();
            intelligence.put("threatStats", threatStats);
            
            // Get threat trends
            List<Map<String, Object>> threatTrends = getThreatTrends();
            intelligence.put("threatTrends", threatTrends);
            
            return intelligence;
            
        } catch (Exception e) {
            log.error("Error getting threat intelligence: ", e);
            return intelligence;
        }
    }

    // Private helper methods

    private boolean isValidIp(String ip) {
        return ip != null && IP_PATTERN.matcher(ip).matches();
    }

    private Map<String, Object> getThreatIntelligence(String ipAddress) {
        Map<String, Object> intel = new HashMap<>();
        
        try {
            // Check cache first
            if (threatCache.containsKey(ipAddress)) {
                return threatCache.get(ipAddress);
            }
            
            // Check database
            ThreatIntelligence threatIntel = threatIntelligenceRepository
                .findByIpAddress(ipAddress);
            
            if (threatIntel != null) {
                intel.put("threatType", threatIntel.getThreatType());
                intel.put("severity", threatIntel.getSeverity());
                intel.put("confidence", threatIntel.getConfidence());
                intel.put("source", threatIntel.getSource());
                intel.put("lastSeen", threatIntel.getLastSeen());
                intel.put("description", threatIntel.getDescription());
            } else {
                intel.put("threatType", "UNKNOWN");
                intel.put("severity", "LOW");
                intel.put("confidence", 0);
            }
            
            // Cache result
            threatCache.put(ipAddress, intel);
            
            return intel;
            
        } catch (Exception e) {
            log.error("Error getting threat intelligence for IP {}: ", ipAddress, e);
            intel.put("error", "Failed to get threat intelligence");
            return intel;
        }
    }

    private Map<String, Object> analyzeIpPatterns(String ipAddress) {
        Map<String, Object> patterns = new HashMap<>();
        
        try {
            // Check for private IP
            patterns.put("isPrivate", isPrivateIp(ipAddress));
            
            // Check for localhost
            patterns.put("isLocalhost", isLocalhost(ipAddress));
            
            // Check recent activity
            long recentEvents = eventRepository.countByIpAddressAndTimestampAfter(
                ipAddress, LocalDateTime.now().minusHours(1));
            patterns.put("recentActivity", recentEvents);
            
            // Check failed attempts
            long failedAttempts = eventRepository.countByIpAddressAndIsSuccessfulAndTimestampAfter(
                ipAddress, false, LocalDateTime.now().minusHours(24));
            patterns.put("failedAttempts", failedAttempts);
            
            // Check for suspicious patterns
            boolean isSuspicious = recentEvents > 100 || failedAttempts > 20;
            patterns.put("isSuspicious", isSuspicious);
            
            return patterns;
            
        } catch (Exception e) {
            log.error("Error analyzing IP patterns for {}: ", ipAddress, e);
            return patterns;
        }
    }

    private Map<String, Object> getGeolocation(String ipAddress) {
        Map<String, Object> geolocation = new HashMap<>();
        
        try {
            // Mock geolocation - in real implementation, use IP geolocation service
            if (isPrivateIp(ipAddress)) {
                geolocation.put("country", "Private Network");
                geolocation.put("city", "Unknown");
                geolocation.put("region", "Unknown");
            } else {
                geolocation.put("country", "Unknown");
                geolocation.put("city", "Unknown");
                geolocation.put("region", "Unknown");
                geolocation.put("isp", "Unknown");
            }
            
            return geolocation;
            
        } catch (Exception e) {
            log.error("Error getting geolocation for IP {}: ", ipAddress, e);
            return geolocation;
        }
    }

    private int calculateRiskScore(Map<String, Object> analysis) {
        int score = 0;
        
        try {
            // Base score
            score += 10;
            
            // Check if blocked
            if ((Boolean) analysis.getOrDefault("isBlocked", false)) {
                score += 50;
            }
            
            // Check if threat
            if ((Boolean) analysis.getOrDefault("isThreat", false)) {
                score += 40;
            }
            
            // Check suspicious activity
            if ((Boolean) analysis.getOrDefault("isSuspicious", false)) {
                score += 30;
            }
            
            // Check failed attempts
            Long failedAttempts = (Long) analysis.getOrDefault("failedAttempts", 0L);
            if (failedAttempts > 10) {
                score += 20;
            }
            
            // Check recent activity
            Long recentActivity = (Long) analysis.getOrDefault("recentActivity", 0L);
            if (recentActivity > 50) {
                score += 15;
            }
            
            return Math.min(100, score);
            
        } catch (Exception e) {
            log.error("Error calculating risk score: ", e);
            return 50; // Default moderate risk
        }
    }

    private String getThreatLevel(int riskScore) {
        if (riskScore >= 80) return "CRITICAL";
        if (riskScore >= 60) return "HIGH";
        if (riskScore >= 40) return "MEDIUM";
        return "LOW";
    }

    private boolean checkThreatFeeds(String ipAddress) {
        try {
            // Check against known threat feeds
            // This would integrate with external threat intelligence services
            return threatIntelligenceRepository.existsByIpAddressAndIsActiveTrue(ipAddress);
            
        } catch (Exception e) {
            log.error("Error checking threat feeds for IP {}: ", ipAddress, e);
            return false;
        }
    }

    private boolean isPrivateIp(String ip) {
        return ip.startsWith("192.168.") || 
               ip.startsWith("10.") || 
               ip.startsWith("172.16.") ||
               ip.startsWith("172.17.") ||
               ip.startsWith("172.18.") ||
               ip.startsWith("172.19.") ||
               ip.startsWith("172.20.") ||
               ip.startsWith("172.21.") ||
               ip.startsWith("172.22.") ||
               ip.startsWith("172.23.") ||
               ip.startsWith("172.24.") ||
               ip.startsWith("172.25.") ||
               ip.startsWith("172.26.") ||
               ip.startsWith("172.27.") ||
               ip.startsWith("172.28.") ||
               ip.startsWith("172.29.") ||
               ip.startsWith("172.30.") ||
               ip.startsWith("172.31.");
    }

    private boolean isLocalhost(String ip) {
        return "127.0.0.1".equals(ip) || "::1".equals(ip);
    }

    private void analyzeBruteForcePatterns(SecurityEvent event) {
        if ("LOGIN".equals(event.getEventType()) && Boolean.FALSE.equals(event.getIsSuccessful())) {
            // Check for brute force patterns
            long recentFailures = eventRepository.countByIpAddressAndEventTypeAndIsSuccessfulAndTimestampAfter(
                event.getIpAddress(), "LOGIN", false, LocalDateTime.now().minusMinutes(10));
            
            if (recentFailures > 5) {
                // Potential brute force attack
                blockIp(event.getIpAddress(), "Brute force attack detected");
            }
        }
    }

    private void analyzeSuspiciousLoginPatterns(SecurityEvent event) {
        if ("LOGIN".equals(event.getEventType()) && Boolean.TRUE.equals(event.getIsSuccessful())) {
            // Check for suspicious login patterns
            // e.g., login from unusual location, time, device
            
            String userId = event.getUserId();
            if (userId != null) {
                // Check for unusual geolocation
                List<String> userLocations = eventRepository.findDistinctGeolocationsByUserId(userId);
                if (userLocations.size() > 10) {
                    log.warn("User {} has many different geolocations", userId);
                }
            }
        }
    }

    private void analyzeFileAccessPatterns(SecurityEvent event) {
        if ("FILE_ACCESS".equals(event.getEventType())) {
            // Check for suspicious file access patterns
            long recentAccess = eventRepository.countByUserIdAndEventTypeAndTimestampAfter(
                event.getUserId(), "FILE_ACCESS", LocalDateTime.now().minusHours(1));
            
            if (recentAccess > 100) {
                log.warn("User {} has excessive file access: {}", event.getUserId(), recentAccess);
            }
        }
    }

    private void analyzeApiAbusePatterns(SecurityEvent event) {
        if (event.getIpAddress() != null) {
            // Check for API abuse patterns
            long recentRequests = eventRepository.countByIpAddressAndTimestampAfter(
                event.getIpAddress(), LocalDateTime.now().minusMinutes(5));
            
            if (recentRequests > 200) {
                log.warn("IP {} has excessive API requests: {}", event.getIpAddress(), recentRequests);
            }
        }
    }

    private void analyzeGeolocationAnomalies(SecurityEvent event) {
        if (event.getUserId() != null && event.getGeolocation() != null) {
            // Check for geolocation anomalies
            List<SecurityEvent> recentEvents = eventRepository
                .findTop10ByUserIdAndGeolocationNotNullOrderByTimestampDesc(event.getUserId());
            
            if (recentEvents.size() > 1) {
                String currentLocation = event.getGeolocation();
                String previousLocation = recentEvents.get(0).getGeolocation();
                
                if (!currentLocation.equals(previousLocation)) {
                    log.info("User {} changed location from {} to {}", 
                        event.getUserId(), previousLocation, currentLocation);
                }
            }
        }
    }

    private Map<String, Long> getThreatStatistics() {
        Map<String, Long> stats = new HashMap<>();
        
        try {
            stats.put("totalThreats", threatIntelligenceRepository.count());
            stats.put("activeThreats", threatIntelligenceRepository.countByIsActiveTrue());
            stats.put("blockedIps", blockedIpRepository.countByIsActiveTrue());
            stats.put("malwareThreats", threatIntelligenceRepository.countByThreatType("MALWARE"));
            stats.put("botnetThreats", threatIntelligenceRepository.countByThreatType("BOTNET"));
            stats.put("phishingThreats", threatIntelligenceRepository.countByThreatType("PHISHING"));
            
            return stats;
            
        } catch (Exception e) {
            log.error("Error getting threat statistics: ", e);
            return stats;
        }
    }

    private List<Map<String, Object>> getThreatTrends() {
        List<Map<String, Object>> trends = new ArrayList<>();
        
        try {
            // Get daily threat counts for the last 7 days
            for (int i = 0; i < 7; i++) {
                LocalDateTime date = LocalDateTime.now().minusDays(i);
                LocalDateTime dayStart = date.toLocalDate().atStartOfDay();
                LocalDateTime dayEnd = dayStart.plusDays(1);
                
                long threatsCount = eventRepository.countByThreatLevelNotNullAndTimestampBetween(
                    dayStart, dayEnd);
                
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", dayStart.toLocalDate());
                trend.put("threatCount", threatsCount);
                trends.add(trend);
            }
            
            return trends;
            
        } catch (Exception e) {
            log.error("Error getting threat trends: ", e);
            return trends;
        }
    }
}
