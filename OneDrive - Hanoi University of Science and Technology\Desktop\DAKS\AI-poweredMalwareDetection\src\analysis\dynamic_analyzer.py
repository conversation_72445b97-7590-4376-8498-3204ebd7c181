"""
Dynamic analysis module for malware detection
"""

import os
import time
import json
import logging
import subprocess
import threading
from pathlib import Path
import psutil
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# Windows-specific imports
try:
    import winreg
    HAS_WINREG = True
except ImportError:
    HAS_WINREG = False

class DynamicAnalyzer:
    """Dynamic analyzer for malware detection"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.monitoring_data = {
            'api_calls': [],
            'file_operations': [],
            'network_activity': [],
            'registry_changes': [],
            'process_activity': []
        }
        
    def analyze(self, file_path, timeout=300):
        """Perform dynamic analysis on file"""
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        features = {}
        
        try:
            # Setup monitoring
            self._setup_monitoring()
            
            # Execute file in sandbox
            process = self._execute_file(file_path)
            
            # Monitor for specified time
            self._monitor_execution(process, timeout)
            
            # Extract features from monitoring data
            features = self._extract_dynamic_features()
            
            # Cleanup
            self._cleanup_monitoring()
            
            self.logger.info(f"Dynamic analysis completed for: {file_path}")
            
        except Exception as e:
            self.logger.error(f"Error in dynamic analysis: {str(e)}")
            raise
        
        return features
    
    def _setup_monitoring(self):
        """Setup monitoring systems"""
        
        # Reset monitoring data
        self.monitoring_data = {
            'api_calls': [],
            'file_operations': [],
            'network_activity': [],
            'registry_changes': [],
            'process_activity': []
        }
        
        # Setup file system monitoring
        self._setup_file_monitoring()
        
        # Setup network monitoring
        self._setup_network_monitoring()
        
        # Setup registry monitoring
        self._setup_registry_monitoring()
        
    def _setup_file_monitoring(self):
        """Setup file system monitoring"""
        
        class FileEventHandler(FileSystemEventHandler):
            def __init__(self, analyzer):
                self.analyzer = analyzer
            
            def on_created(self, event):
                if not event.is_directory:
                    self.analyzer.monitoring_data['file_operations'].append({
                        'type': 'create',
                        'path': event.src_path,
                        'timestamp': time.time()
                    })
            
            def on_modified(self, event):
                if not event.is_directory:
                    self.analyzer.monitoring_data['file_operations'].append({
                        'type': 'modify',
                        'path': event.src_path,
                        'timestamp': time.time()
                    })
            
            def on_deleted(self, event):
                if not event.is_directory:
                    self.analyzer.monitoring_data['file_operations'].append({
                        'type': 'delete',
                        'path': event.src_path,
                        'timestamp': time.time()
                    })
        
        # Monitor system directories
        self.file_observer = Observer()
        handler = FileEventHandler(self)
        
        # Monitor common target directories
        watch_dirs = [
            os.path.expanduser('~\\Documents'),
            os.path.expanduser('~\\Desktop'),
            'C:\\Windows\\System32',
            'C:\\Windows\\Temp',
            'C:\\Temp'
        ]
        
        for watch_dir in watch_dirs:
            if os.path.exists(watch_dir):
                self.file_observer.schedule(handler, watch_dir, recursive=True)
        
        self.file_observer.start()
        
    def _setup_network_monitoring(self):
        """Setup network monitoring"""
        
        # Start network monitoring thread
        self.network_monitoring = True
        self.network_thread = threading.Thread(target=self._monitor_network)
        self.network_thread.daemon = True
        self.network_thread.start()
        
    def _setup_registry_monitoring(self):
        """Setup registry monitoring"""
        
        # Monitor key registry locations
        self.registry_monitoring = True
        self.registry_thread = threading.Thread(target=self._monitor_registry)
        self.registry_thread.daemon = True
        self.registry_thread.start()
        
    def _execute_file(self, file_path):
        """Execute file in controlled environment"""
        
        try:
            # Create sandbox directory
            sandbox_dir = Path('sandbox')
            sandbox_dir.mkdir(exist_ok=True)
            
            # Copy file to sandbox
            sandbox_file = sandbox_dir / Path(file_path).name
            import shutil
            shutil.copy2(file_path, sandbox_file)
            
            # Execute file
            process = subprocess.Popen(
                str(sandbox_file),
                cwd=str(sandbox_dir),
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
            
            self.logger.info(f"Executed file in sandbox: {sandbox_file}")
            return process
            
        except Exception as e:
            self.logger.error(f"Error executing file: {str(e)}")
            raise
    
    def _monitor_execution(self, process, timeout):
        """Monitor execution for specified time"""
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # Check if process is still running
            if process.poll() is not None:
                break
            
            # Monitor process activity
            try:
                proc = psutil.Process(process.pid)
                
                # CPU and memory usage
                cpu_percent = proc.cpu_percent()
                memory_info = proc.memory_info()
                
                self.monitoring_data['process_activity'].append({
                    'pid': process.pid,
                    'cpu_percent': cpu_percent,
                    'memory_rss': memory_info.rss,
                    'memory_vms': memory_info.vms,
                    'timestamp': time.time()
                })
                
                # Child processes
                for child in proc.children(recursive=True):
                    self.monitoring_data['process_activity'].append({
                        'pid': child.pid,
                        'parent_pid': process.pid,
                        'name': child.name(),
                        'timestamp': time.time()
                    })
                
            except psutil.NoSuchProcess:
                break
            except Exception as e:
                self.logger.warning(f"Error monitoring process: {str(e)}")
            
            time.sleep(1)
        
        # Terminate process if still running
        try:
            if process.poll() is None:
                process.terminate()
                process.wait(timeout=10)
        except:
            try:
                process.kill()
            except:
                pass
    
    def _monitor_network(self):
        """Monitor network activity"""
        
        initial_connections = set()
        
        # Get initial connections
        try:
            for conn in psutil.net_connections():
                if conn.status == 'ESTABLISHED':
                    initial_connections.add((conn.laddr, conn.raddr))
        except:
            pass
        
        while self.network_monitoring:
            try:
                current_connections = set()
                
                for conn in psutil.net_connections():
                    if conn.status == 'ESTABLISHED':
                        current_connections.add((conn.laddr, conn.raddr))
                
                # Find new connections
                new_connections = current_connections - initial_connections
                
                for laddr, raddr in new_connections:
                    self.monitoring_data['network_activity'].append({
                        'type': 'connection',
                        'local_addr': laddr,
                        'remote_addr': raddr,
                        'timestamp': time.time()
                    })
                
                initial_connections = current_connections
                
            except Exception as e:
                self.logger.warning(f"Error monitoring network: {str(e)}")
            
            time.sleep(2)
    
    def _monitor_registry(self):
        """Monitor registry changes"""
        
        if not HAS_WINREG:
            self.logger.warning("Registry monitoring not available on this platform")
            return
        
        # Monitor key registry locations
        registry_keys = [
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
            (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
            (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services"),
        ]
        
        # Get initial registry state
        initial_state = {}
        
        for hkey, subkey in registry_keys:
            try:
                with winreg.OpenKey(hkey, subkey) as key:
                    values = {}
                    i = 0
                    while True:
                        try:
                            name, value, _ = winreg.EnumValue(key, i)
                            values[name] = value
                            i += 1
                        except WindowsError:
                            break
                    initial_state[subkey] = values
            except:
                pass
        
        while self.registry_monitoring:
            try:
                current_state = {}
                
                for hkey, subkey in registry_keys:
                    try:
                        with winreg.OpenKey(hkey, subkey) as key:
                            values = {}
                            i = 0
                            while True:
                                try:
                                    name, value, _ = winreg.EnumValue(key, i)
                                    values[name] = value
                                    i += 1
                                except WindowsError:
                                    break
                            current_state[subkey] = values
                    except:
                        pass
                
                # Compare with initial state
                for subkey in current_state:
                    if subkey in initial_state:
                        current_values = current_state[subkey]
                        initial_values = initial_state[subkey]
                        
                        # Check for new values
                        for name, value in current_values.items():
                            if name not in initial_values or initial_values[name] != value:
                                self.monitoring_data['registry_changes'].append({
                                    'type': 'modify',
                                    'key': subkey,
                                    'name': name,
                                    'value': value,
                                    'timestamp': time.time()
                                })
                
                initial_state = current_state
                
            except Exception as e:
                self.logger.warning(f"Error monitoring registry: {str(e)}")
            
            time.sleep(5)
    
    def _extract_dynamic_features(self):
        """Extract features from monitoring data"""
        
        features = {}
        
        # File operation features
        file_ops = self.monitoring_data['file_operations']
        features['file_creates'] = len([op for op in file_ops if op['type'] == 'create'])
        features['file_modifies'] = len([op for op in file_ops if op['type'] == 'modify'])
        features['file_deletes'] = len([op for op in file_ops if op['type'] == 'delete'])
        
        # Network activity features
        network_activity = self.monitoring_data['network_activity']
        features['network_connections'] = len(network_activity)
        
        # Extract unique remote IPs
        remote_ips = set()
        for activity in network_activity:
            if 'remote_addr' in activity:
                remote_ips.add(activity['remote_addr'][0])
        features['unique_remote_ips'] = len(remote_ips)
        
        # Registry change features
        registry_changes = self.monitoring_data['registry_changes']
        features['registry_changes'] = len(registry_changes)
        
        # Process activity features
        process_activity = self.monitoring_data['process_activity']
        features['process_events'] = len(process_activity)
        
        # Calculate resource usage
        if process_activity:
            cpu_usage = [p['cpu_percent'] for p in process_activity if 'cpu_percent' in p]
            memory_usage = [p['memory_rss'] for p in process_activity if 'memory_rss' in p]
            
            if cpu_usage:
                features['avg_cpu_usage'] = sum(cpu_usage) / len(cpu_usage)
                features['max_cpu_usage'] = max(cpu_usage)
            
            if memory_usage:
                features['avg_memory_usage'] = sum(memory_usage) / len(memory_usage)
                features['max_memory_usage'] = max(memory_usage)
        
        return features
    
    def _cleanup_monitoring(self):
        """Cleanup monitoring systems"""
        
        # Stop file monitoring
        if hasattr(self, 'file_observer'):
            self.file_observer.stop()
            self.file_observer.join()
        
        # Stop network monitoring
        self.network_monitoring = False
        if hasattr(self, 'network_thread'):
            self.network_thread.join(timeout=5)
        
        # Stop registry monitoring
        self.registry_monitoring = False
        if hasattr(self, 'registry_thread'):
            self.registry_thread.join(timeout=5)
        
        # Save monitoring data
        self._save_monitoring_data()
    
    def _save_monitoring_data(self):
        """Save monitoring data to file"""
        
        try:
            output_dir = Path('logs/dynamic_analysis')
            output_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = int(time.time())
            output_file = output_dir / f"monitoring_data_{timestamp}.json"
            
            with open(output_file, 'w') as f:
                json.dump(self.monitoring_data, f, indent=2)
            
            self.logger.info(f"Monitoring data saved to: {output_file}")
            
        except Exception as e:
            self.logger.warning(f"Error saving monitoring data: {str(e)}")
    
    def get_monitoring_data(self):
        """Get current monitoring data"""
        return self.monitoring_data.copy()
