"""
Advanced Logger for Network IDS
==============================

Module logging nâng cao với nhiều tính năng
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import colorlog

class NetworkIDSLogger:
    """Lớp logger nâng cao cho Network IDS"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        Khởi tạo logger
        
        Args:
            name (str): Tên logger
            config (Dict): <PERSON><PERSON>u hình logger
        """
        self.name = name
        self.config = config
        self.logger = logging.getLogger(name)
        self.setup_logger()
    
    def setup_logger(self) -> None:
        """Thiết lập logger"""
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Set level
        level = getattr(logging, self.config.get('level', 'INFO').upper())
        self.logger.setLevel(level)
        
        # Create formatters
        file_formatter = logging.Formatter(
            self.config.get('format', 
                           '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        
        # Console formatter with colors
        console_formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        
        # File handler
        if self.config.get('file'):
            self.setup_file_handler(file_formatter)
        
        # Console handler
        if self.config.get('console', True):
            self.setup_console_handler(console_formatter)
        
        # System handler (Windows Event Log / Linux Syslog)
        if self.config.get('system_log', False):
            self.setup_system_handler(file_formatter)
    
    def setup_file_handler(self, formatter: logging.Formatter) -> None:
        """Thiết lập file handler"""
        log_file = Path(self.config['file'])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Rotating file handler
        max_size = self._parse_size(self.config.get('max_size', '100MB'))
        backup_count = self.config.get('backup_count', 5)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def setup_console_handler(self, formatter: logging.Formatter) -> None:
        """Thiết lập console handler"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def setup_system_handler(self, formatter: logging.Formatter) -> None:
        """Thiết lập system handler"""
        if sys.platform == 'win32':
            # Windows Event Log
            try:
                import logging.handlers
                sys_handler = logging.handlers.NTEventLogHandler(
                    appname='NetworkIDS',
                    logtype='Application'
                )
                sys_handler.setFormatter(formatter)
                self.logger.addHandler(sys_handler)
            except ImportError:
                pass
        else:
            # Linux Syslog
            sys_handler = logging.handlers.SysLogHandler(
                address='/dev/log',
                facility=logging.handlers.SysLogHandler.LOG_DAEMON
            )
            sys_handler.setFormatter(formatter)
            self.logger.addHandler(sys_handler)
    
    def _parse_size(self, size_str: str) -> int:
        """
        Parse size string to bytes
        
        Args:
            size_str (str): Size string like "100MB"
            
        Returns:
            int: Size in bytes
        """
        size_str = size_str.upper()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_logger(self) -> logging.Logger:
        """Trả về logger instance"""
        return self.logger

class SecurityLogger(NetworkIDSLogger):
    """Logger đặc biệt cho security events"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__('SecurityLogger', config)
        self.setup_security_features()
    
    def setup_security_features(self) -> None:
        """Thiết lập các tính năng bảo mật"""
        # Tạo security log file riêng
        security_config = self.config.copy()
        security_config['file'] = 'logs/security_events.log'
        
        log_file = Path(security_config['file'])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Security file handler với mã hóa
        security_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=self._parse_size(security_config.get('max_size', '100MB')),
            backupCount=security_config.get('backup_count', 10),
            encoding='utf-8'
        )
        
        # Security formatter
        security_formatter = logging.Formatter(
            '%(asctime)s - SECURITY - %(levelname)s - %(message)s'
        )
        
        security_handler.setFormatter(security_formatter)
        self.logger.addHandler(security_handler)
    
    def log_threat_detection(self, threat_type: str, details: Dict[str, Any]) -> None:
        """
        Log threat detection event
        
        Args:
            threat_type (str): Loại threat
            details (Dict): Chi tiết về threat
        """
        message = f"THREAT_DETECTED: {threat_type} - {details}"
        self.logger.critical(message)
    
    def log_security_event(self, event_type: str, details: Dict[str, Any]) -> None:
        """
        Log security event
        
        Args:
            event_type (str): Loại event
            details (Dict): Chi tiết về event
        """
        message = f"SECURITY_EVENT: {event_type} - {details}"
        self.logger.warning(message)
    
    def log_system_compromise(self, details: Dict[str, Any]) -> None:
        """
        Log system compromise event
        
        Args:
            details (Dict): Chi tiết về compromise
        """
        message = f"SYSTEM_COMPROMISE: {details}"
        self.logger.critical(message)

class PerformanceLogger(NetworkIDSLogger):
    """Logger cho performance monitoring"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__('PerformanceLogger', config)
        self.setup_performance_features()
    
    def setup_performance_features(self) -> None:
        """Thiết lập performance logging"""
        # Performance log file
        perf_config = self.config.copy()
        perf_config['file'] = 'logs/performance.log'
        
        log_file = Path(perf_config['file'])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Performance handler
        perf_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=self._parse_size(perf_config.get('max_size', '50MB')),
            backupCount=perf_config.get('backup_count', 5),
            encoding='utf-8'
        )
        
        # Performance formatter
        perf_formatter = logging.Formatter(
            '%(asctime)s - PERFORMANCE - %(message)s'
        )
        
        perf_handler.setFormatter(perf_formatter)
        self.logger.addHandler(perf_handler)
    
    def log_metrics(self, metrics: Dict[str, Any]) -> None:
        """
        Log performance metrics
        
        Args:
            metrics (Dict): Performance metrics
        """
        message = f"METRICS: {metrics}"
        self.logger.info(message)
    
    def log_bottleneck(self, component: str, details: Dict[str, Any]) -> None:
        """
        Log performance bottleneck
        
        Args:
            component (str): Component có bottleneck
            details (Dict): Chi tiết về bottleneck
        """
        message = f"BOTTLENECK: {component} - {details}"
        self.logger.warning(message)

def setup_logger(name: str, config: Dict[str, Any]) -> logging.Logger:
    """
    Thiết lập logger cho component
    
    Args:
        name (str): Tên component
        config (Dict): Cấu hình logger
        
    Returns:
        logging.Logger: Logger instance
    """
    if name == 'SecurityLogger':
        logger_instance = SecurityLogger(config)
    elif name == 'PerformanceLogger':
        logger_instance = PerformanceLogger(config)
    else:
        logger_instance = NetworkIDSLogger(name, config)
    
    return logger_instance.get_logger()

def setup_audit_logger(config: Dict[str, Any]) -> logging.Logger:
    """
    Thiết lập audit logger
    
    Args:
        config (Dict): Cấu hình logger
        
    Returns:
        logging.Logger: Audit logger
    """
    audit_config = config.copy()
    audit_config['file'] = 'logs/audit.log'
    
    return setup_logger('AuditLogger', audit_config)

class LogAnalyzer:
    """Lớp phân tích log"""
    
    def __init__(self, log_path: str):
        """
        Khởi tạo log analyzer
        
        Args:
            log_path (str): Đường dẫn đến log file
        """
        self.log_path = Path(log_path)
    
    def analyze_security_events(self) -> Dict[str, Any]:
        """
        Phân tích security events
        
        Returns:
            Dict: Kết quả phân tích
        """
        if not self.log_path.exists():
            return {}
        
        results = {
            'total_events': 0,
            'threat_detections': 0,
            'security_events': 0,
            'compromises': 0,
            'event_types': {}
        }
        
        try:
            with open(self.log_path, 'r', encoding='utf-8') as f:
                for line in f:
                    results['total_events'] += 1
                    
                    if 'THREAT_DETECTED' in line:
                        results['threat_detections'] += 1
                    elif 'SECURITY_EVENT' in line:
                        results['security_events'] += 1
                    elif 'SYSTEM_COMPROMISE' in line:
                        results['compromises'] += 1
                    
                    # Parse event type
                    if ' - ' in line:
                        parts = line.split(' - ')
                        if len(parts) >= 3:
                            event_type = parts[2].split(':')[0]
                            results['event_types'][event_type] = results['event_types'].get(event_type, 0) + 1
        
        except Exception as e:
            print(f"Error analyzing log: {str(e)}")
        
        return results
    
    def get_recent_events(self, hours: int = 24) -> list:
        """
        Lấy events gần đây
        
        Args:
            hours (int): Số giờ gần đây
            
        Returns:
            list: Danh sách events
        """
        import datetime
        
        if not self.log_path.exists():
            return []
        
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
        recent_events = []
        
        try:
            with open(self.log_path, 'r', encoding='utf-8') as f:
                for line in f:
                    # Parse timestamp
                    timestamp_str = line.split(' - ')[0]
                    try:
                        timestamp = datetime.datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                        if timestamp >= cutoff_time:
                            recent_events.append(line.strip())
                    except ValueError:
                        continue
        
        except Exception as e:
            print(f"Error getting recent events: {str(e)}")
        
        return recent_events
