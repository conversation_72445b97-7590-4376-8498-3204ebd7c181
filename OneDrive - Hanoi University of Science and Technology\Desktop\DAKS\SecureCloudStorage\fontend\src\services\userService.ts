import { apiService } from './apiService';
import {
    User,
    UpdateUserRequest,
    PaginatedResponse,
    ApiResponse,
    UserRole,
    UserStatus
} from '../types';

// User search and filter options
export interface UserSearchOptions {
    page?: number;
    size?: number;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
    search?: string;
    role?: UserRole;
    status?: UserStatus;
    createdAfter?: string;
    createdBefore?: string;
}

// User statistics
export interface UserStatistics {
    totalUsers: number;
    activeUsers: number;
    newUsersToday: number;
    newUsersThisWeek: number;
    newUsersThisMonth: number;
    usersByRole: Record<UserRole, number>;
    usersByStatus: Record<UserStatus, number>;
}

// User activity
export interface UserActivity {
    id: string;
    userId: string;
    action: string;
    description: string;
    timestamp: string;
    ipAddress: string;
    userAgent?: string;
    metadata?: Record<string, any>;
}

// User Service Class
class UserService {
    // User profile management
    async getCurrentUserProfile(): Promise<User> {
        try {
            const response = await apiService.get<User>('/api/users/me');

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch user profile');
            }
        } catch (error) {
            console.error('Get current user profile error:', error);
            throw error;
        }
    }

    async updateCurrentUserProfile(userData: UpdateUserRequest): Promise<User> {
        try {
            const response = await apiService.put<User>('/api/users/me', userData);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to update user profile');
            }
        } catch (error) {
            console.error('Update user profile error:', error);
            throw error;
        }
    }

    async uploadProfilePicture(file: File): Promise<{ profilePictureUrl: string }> {
        try {
            const response = await apiService.uploadFile<{ profilePictureUrl: string }>(
                '/api/users/me/profile-picture',
                file
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to upload profile picture');
            }
        } catch (error) {
            console.error('Upload profile picture error:', error);
            throw error;
        }
    }

    async deleteProfilePicture(): Promise<void> {
        try {
            const response = await apiService.delete('/api/users/me/profile-picture');

            if (!response.success) {
                throw new Error(response.message || 'Failed to delete profile picture');
            }
        } catch (error) {
            console.error('Delete profile picture error:', error);
            throw error;
        }
    }

    // User management (Admin functions)
    async getAllUsers(options: UserSearchOptions = {}): Promise<PaginatedResponse<User>> {
        try {
            const params = new URLSearchParams();

            if (options.page !== undefined) params.append('page', options.page.toString());
            if (options.size !== undefined) params.append('size', options.size.toString());
            if (options.sortBy) params.append('sortBy', options.sortBy);
            if (options.sortDirection) params.append('sortDirection', options.sortDirection);
            if (options.search) params.append('search', options.search);
            if (options.role) params.append('role', options.role);
            if (options.status) params.append('status', options.status);
            if (options.createdAfter) params.append('createdAfter', options.createdAfter);
            if (options.createdBefore) params.append('createdBefore', options.createdBefore);

            const response = await apiService.get<PaginatedResponse<User>>(
                `/api/users?${params.toString()}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch users');
            }
        } catch (error) {
            console.error('Get all users error:', error);
            throw error;
        }
    }

    async getUserById(userId: string): Promise<User> {
        try {
            const response = await apiService.get<User>(`/api/users/${userId}`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch user');
            }
        } catch (error) {
            console.error('Get user by ID error:', error);
            throw error;
        }
    }

    async updateUser(userId: string, userData: UpdateUserRequest): Promise<User> {
        try {
            const response = await apiService.put<User>(`/api/users/${userId}`, userData);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to update user');
            }
        } catch (error) {
            console.error('Update user error:', error);
            throw error;
        }
    }

    async deleteUser(userId: string): Promise<void> {
        try {
            const response = await apiService.delete(`/api/users/${userId}`);

            if (!response.success) {
                throw new Error(response.message || 'Failed to delete user');
            }
        } catch (error) {
            console.error('Delete user error:', error);
            throw error;
        }
    }

    async activateUser(userId: string): Promise<User> {
        try {
            const response = await apiService.post<User>(`/api/users/${userId}/activate`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to activate user');
            }
        } catch (error) {
            console.error('Activate user error:', error);
            throw error;
        }
    }

    async deactivateUser(userId: string): Promise<User> {
        try {
            const response = await apiService.post<User>(`/api/users/${userId}/deactivate`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to deactivate user');
            }
        } catch (error) {
            console.error('Deactivate user error:', error);
            throw error;
        }
    }

    async suspendUser(userId: string, reason?: string): Promise<User> {
        try {
            const response = await apiService.post<User>(`/api/users/${userId}/suspend`, {
                reason
            });

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to suspend user');
            }
        } catch (error) {
            console.error('Suspend user error:', error);
            throw error;
        }
    }

    async unsuspendUser(userId: string): Promise<User> {
        try {
            const response = await apiService.post<User>(`/api/users/${userId}/unsuspend`);

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to unsuspend user');
            }
        } catch (error) {
            console.error('Unsuspend user error:', error);
            throw error;
        }
    }

    // Role management
    async updateUserRole(userId: string, role: UserRole): Promise<User> {
        try {
            const response = await apiService.put<User>(`/api/users/${userId}/role`, { role });

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to update user role');
            }
        } catch (error) {
            console.error('Update user role error:', error);
            throw error;
        }
    }

    // User statistics and analytics
    async getUserStatistics(): Promise<UserStatistics> {
        try {
            const response = await apiService.get<UserStatistics>('/api/users/statistics');

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch user statistics');
            }
        } catch (error) {
            console.error('Get user statistics error:', error);
            throw error;
        }
    }

    // User activity tracking
    async getUserActivity(
        userId: string,
        page: number = 0,
        size: number = 20
    ): Promise<PaginatedResponse<UserActivity>> {
        try {
            const response = await apiService.get<PaginatedResponse<UserActivity>>(
                `/api/users/${userId}/activity?page=${page}&size=${size}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch user activity');
            }
        } catch (error) {
            console.error('Get user activity error:', error);
            throw error;
        }
    }

    async getCurrentUserActivity(
        page: number = 0,
        size: number = 20
    ): Promise<PaginatedResponse<UserActivity>> {
        try {
            const response = await apiService.get<PaginatedResponse<UserActivity>>(
                `/api/users/me/activity?page=${page}&size=${size}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to fetch current user activity');
            }
        } catch (error) {
            console.error('Get current user activity error:', error);
            throw error;
        }
    }

    // Search users
    async searchUsers(query: string, limit: number = 10): Promise<User[]> {
        try {
            const response = await apiService.get<User[]>(
                `/api/users/search?q=${encodeURIComponent(query)}&limit=${limit}`
            );

            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to search users');
            }
        } catch (error) {
            console.error('Search users error:', error);
            throw error;
        }
    }

    // Bulk operations
    async bulkUpdateUsers(userIds: string[], updates: Partial<User>): Promise<void> {
        try {
            const response = await apiService.put('/api/users/bulk', {
                userIds,
                updates
            });

            if (!response.success) {
                throw new Error(response.message || 'Failed to bulk update users');
            }
        } catch (error) {
            console.error('Bulk update users error:', error);
            throw error;
        }
    }

    async bulkDeleteUsers(userIds: string[]): Promise<void> {
        try {
            const response = await apiService.delete('/api/users/bulk', {
                data: { userIds }
            });

            if (!response.success) {
                throw new Error(response.message || 'Failed to bulk delete users');
            }
        } catch (error) {
            console.error('Bulk delete users error:', error);
            throw error;
        }
    }

    // Export users
    async exportUsers(format: 'csv' | 'xlsx' = 'csv', filters?: UserSearchOptions): Promise<void> {
        try {
            const params = new URLSearchParams();
            params.append('format', format);

            if (filters) {
                Object.entries(filters).forEach(([key, value]) => {
                    if (value !== undefined && value !== null) {
                        params.append(key, value.toString());
                    }
                });
            }

            await apiService.downloadFile(
                `/api/users/export?${params.toString()}`,
                `users_export_${new Date().toISOString().split('T')[0]}.${format}`
            );
        } catch (error) {
            console.error('Export users error:', error);
            throw error;
        }
    }
}

// Export singleton instance
export const userService = new UserService();
export default userService;