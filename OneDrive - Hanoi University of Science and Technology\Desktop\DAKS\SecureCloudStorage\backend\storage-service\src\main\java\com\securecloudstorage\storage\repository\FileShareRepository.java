package com.securecloudstorage.storage.repository;

import com.securecloudstorage.storage.entity.FileShare;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * File Share Repository for MariaDB
 */
@Repository
public interface FileShareRepository extends JpaRepository<FileShare, Long> {
    
    Optional<FileShare> findByShareToken(String shareToken);
    
    List<FileShare> findByFileId(String fileId);
    
    List<FileShare> findByOwnerUserId(String ownerUserId);
    
    List<FileShare> findByTargetUserId(String targetUserId);
    
    List<FileShare> findByFileIdAndTargetUserId(String fileId, String targetUserId);
    
    boolean existsByFileIdAndTargetUserId(String fileId, String targetUserId);
    
    @Query("SELECT s FROM FileShare s WHERE s.isActive = true AND (s.expiryDate IS NULL OR s.expiryDate > :currentDate)")
    List<FileShare> findActiveShares(@Param("currentDate") LocalDateTime currentDate);
    
    @Query("SELECT s FROM FileShare s WHERE s.expiryDate < :currentDate AND s.isActive = true")
    List<FileShare> findExpiredShares(@Param("currentDate") LocalDateTime currentDate);
    
    @Query("SELECT s FROM FileShare s WHERE s.fileId = :fileId AND s.isActive = true")
    List<FileShare> findActiveSharesByFileId(@Param("fileId") String fileId);
    
    @Query("SELECT s FROM FileShare s WHERE s.targetUserId = :userId AND s.isActive = true")
    List<FileShare> findActiveSharesByTargetUserId(@Param("userId") String userId);
    
    @Query("SELECT COUNT(s) FROM FileShare s WHERE s.fileId = :fileId AND s.isActive = true")
    Long countActiveSharesByFileId(@Param("fileId") String fileId);
    
    @Query("SELECT COUNT(s) FROM FileShare s WHERE s.ownerUserId = :userId")
    Long countByOwnerUserId(@Param("userId") String userId);
    
    @Query("SELECT COUNT(s) FROM FileShare s WHERE s.targetUserId = :userId")
    Long countByTargetUserId(@Param("userId") String userId);
    
    @Query("SELECT s.permission, COUNT(s) FROM FileShare s GROUP BY s.permission")
    List<Object[]> getPermissionStatistics();
    
    @Query("SELECT DATE(s.sharedDate), COUNT(s) FROM FileShare s GROUP BY DATE(s.sharedDate) ORDER BY DATE(s.sharedDate)")
    List<Object[]> getDailyShareStatistics();
    
    void deleteByFileId(String fileId);
    
    void deleteByShareToken(String shareToken);
    
    @Query("SELECT s FROM FileShare s WHERE s.downloadLimit IS NOT NULL AND s.downloadsCount >= s.downloadLimit")
    List<FileShare> findSharesExceedingDownloadLimit();
}
