#!/usr/bin/env python3
"""
Setup Script for Network IDS
===========================

Script thiết lập môi trường cho Network IDS
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Kiểm tra phiên bản Python"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    return True

def check_admin_privileges():
    """Kiểm tra quyền admin"""
    try:
        if platform.system() == "Windows":
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        else:
            is_admin = os.geteuid() == 0
        
        if is_admin:
            print("✅ Administrator privileges detected")
        else:
            print("⚠️  Administrator privileges recommended for packet capture")
        
        return is_admin
    except:
        return False

def create_directories():
    """Tạo c<PERSON><PERSON> thư mục cần thiết"""
    directories = [
        'logs',
        'data',
        'data/training',
        'models',
        'reports',
        'rules',
        'config'
    ]
    
    print("📁 Creating directories...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   Created: {directory}")

def install_dependencies():
    """Cài đặt dependencies"""
    print("📦 Installing Python dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Error installing dependencies")
        return False

def create_sample_rules():
    """Tạo sample rules"""
    print("📋 Creating sample signature rules...")
    
    sample_rules = [
        {
            "rule_id": "WEB_001",
            "name": "SQL Injection Detection",
            "description": "Detects SQL injection attempts in web traffic",
            "pattern": r"(union\s+select|select\s+.*\s+from|insert\s+into|drop\s+table)",
            "severity": "HIGH",
            "category": "web_attack"
        },
        {
            "rule_id": "WEB_002",
            "name": "XSS Detection",
            "description": "Detects cross-site scripting attempts",
            "pattern": r"(<script|javascript:|vbscript:|onload=|onerror=)",
            "severity": "MEDIUM",
            "category": "web_attack"
        },
        {
            "rule_id": "NETWORK_001",
            "name": "Port Scan Detection",
            "description": "Detects network port scanning",
            "pattern": r"(nmap|port.*scan|stealth.*scan)",
            "severity": "MEDIUM",
            "category": "reconnaissance"
        }
    ]
    
    import json
    with open('rules/sample_rules.json', 'w') as f:
        json.dump(sample_rules, f, indent=2)
    
    print("✅ Sample rules created")

def create_sample_config():
    """Tạo sample configuration nếu chưa có"""
    config_file = Path('config/config.yaml')
    
    if not config_file.exists():
        print("⚙️  Creating sample configuration...")
        
        # Config file đã được tạo rồi, chỉ cần copy
        print("✅ Configuration file ready")
    else:
        print("ℹ️  Configuration file already exists")

def setup_logging():
    """Thiết lập logging"""
    print("📝 Setting up logging...")
    
    # Tạo log file rỗng
    log_file = Path('logs/network_ids.log')
    log_file.touch(exist_ok=True)
    
    print("✅ Logging configured")

def check_optional_dependencies():
    """Kiểm tra optional dependencies"""
    print("🔍 Checking optional dependencies...")
    
    optional_deps = {
        'scapy': 'Packet capture and analysis',
        'tensorflow': 'Machine learning models',
        'sklearn': 'Machine learning algorithms',
        'flask': 'Web dashboard',
        'pandas': 'Data processing',
        'numpy': 'Numerical computing'
    }
    
    for dep, description in optional_deps.items():
        try:
            __import__(dep)
            print(f"   ✅ {dep} - {description}")
        except ImportError:
            print(f"   ❌ {dep} - {description} (install with: pip install {dep})")

def run_basic_tests():
    """Chạy các test cơ bản"""
    print("🧪 Running basic tests...")
    
    try:
        # Test import các modules chính
        sys.path.insert(0, 'src')
        
        from utils.config import Config
        print("   ✅ Configuration module")
        
        from utils.logger import setup_logger
        print("   ✅ Logger module")
        
        from capture.packet_capture import PacketCapture
        print("   ✅ Packet capture module")
        
        from analysis.traffic_analyzer import TrafficAnalyzer
        print("   ✅ Traffic analyzer module")
        
        from detection.ml_detector import MLDetector
        print("   ✅ ML detector module")
        
        from detection.signature_detector import SignatureDetector
        print("   ✅ Signature detector module")
        
        from alerts.alert_manager import AlertManager
        print("   ✅ Alert manager module")
        
        print("✅ All core modules loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error loading modules: {str(e)}")
        return False

def display_next_steps():
    """Hiển thị các bước tiếp theo"""
    print("\n" + "="*60)
    print("🎉 Network IDS Setup Complete!")
    print("="*60)
    
    print("\n📋 Next Steps:")
    print("1. Review configuration: config/config.yaml")
    print("2. Train ML models: python main.py --train")
    print("3. Start monitoring: python main.py --start")
    print("4. View dashboard: python main.py --dashboard")
    
    print("\n⚠️  Important Notes:")
    print("• Run as administrator for packet capture")
    print("• Configure email settings in config.yaml for alerts")
    print("• Review and customize signature rules in rules/")
    print("• Monitor logs in logs/ directory")
    
    print("\n🔧 Quick Commands:")
    print("• Training: python main.py --train")
    print("• Monitoring: python main.py --start")
    print("• Dashboard: python main.py --dashboard")
    print("• PCAP Analysis: python main.py --analyze-pcap <file>")
    print("• Help: python main.py --help")

def main():
    """Hàm main"""
    print("🚀 Network IDS Setup Script")
    print("=" * 40)
    
    # Kiểm tra Python version
    if not check_python_version():
        sys.exit(1)
    
    # Kiểm tra quyền admin
    check_admin_privileges()
    
    # Tạo directories
    create_directories()
    
    # Cài đặt dependencies
    if not install_dependencies():
        print("❌ Setup failed due to dependency installation error")
        sys.exit(1)
    
    # Tạo sample files
    create_sample_rules()
    create_sample_config()
    
    # Thiết lập logging
    setup_logging()
    
    # Kiểm tra optional dependencies
    check_optional_dependencies()
    
    # Chạy basic tests
    if not run_basic_tests():
        print("❌ Setup completed with errors")
        sys.exit(1)
    
    # Hiển thị next steps
    display_next_steps()
    
    print("\n✅ Setup completed successfully!")

if __name__ == "__main__":
    main()
