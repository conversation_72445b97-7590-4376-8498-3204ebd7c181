"""
Static analysis module for malware detection
"""

import os
import hashlib
import pefile
import logging
from pathlib import Path
import math
import string
import re
from collections import Counter
import mimetypes

# Try to import magic, fallback to mimetypes if not available
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False

# Try to import yara, fallback gracefully if not available
try:
    import yara
    HAS_YARA = True
except ImportError:
    HAS_YARA = False

class StaticAnalyzer:
    """Static analyzer for malware detection"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.yara_rules = self._load_yara_rules()
        
    def analyze(self, file_path):
        """Perform static analysis on file"""
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        features = {}
        
        try:
            # Basic file features
            features.update(self._extract_basic_features(file_path))
            
            # PE features (if PE file)
            if self._is_pe_file(file_path):
                features.update(self._extract_pe_features(file_path))
            
            # String features
            features.update(self._extract_string_features(file_path))
            
            # Entropy features
            features.update(self._extract_entropy_features(file_path))
            
            # YARA rule matching
            features.update(self._extract_yara_features(file_path))
            
            # Set defaults for missing PE features if not a PE file
            if not self._is_pe_file(file_path):
                pe_defaults = {
                    'pe_sections': 0,
                    'pe_import_dlls': 0,
                    'pe_import_functions': 0,
                    'pe_export_functions': 0,
                    'pe_executable_sections': 0,
                    'pe_writable_sections': 0,
                    'avg_section_entropy': 0,
                    'max_section_entropy': 0,
                    'suspicious_imports': 0
                }
                for key, value in pe_defaults.items():
                    if key not in features:
                        features[key] = value
            
            self.logger.info(f"Static analysis completed for: {file_path}")
            
        except Exception as e:
            self.logger.error(f"Error in static analysis: {str(e)}")
            raise
        
        return features
    
    def _extract_basic_features(self, file_path):
        """Extract basic file features"""
        
        features = {}
        
        # File size
        features['file_size'] = os.path.getsize(file_path)
        
        # File type
        try:
            if HAS_MAGIC:
                file_type = magic.from_file(file_path)
            else:
                # Fallback to mimetypes
                file_type, _ = mimetypes.guess_type(file_path)
                if file_type is None:
                    # Try to determine by extension
                    ext = Path(file_path).suffix.lower()
                    if ext in ['.exe', '.dll', '.sys']:
                        file_type = 'PE32 executable'
                    elif ext in ['.bat', '.cmd']:
                        file_type = 'DOS batch file'
                    elif ext in ['.ps1']:
                        file_type = 'PowerShell script'
                    else:
                        file_type = 'unknown'
            features['file_type'] = file_type
        except Exception:
            features['file_type'] = 'unknown'
        
        # Hash values
        features.update(self._calculate_hashes(file_path))
        
        return features
    
    def _extract_pe_features(self, file_path):
        """Extract PE file features"""
        
        features = {}
        
        try:
            pe = pefile.PE(file_path)
            
            # PE header features
            features['pe_machine'] = pe.FILE_HEADER.Machine
            features['pe_sections'] = pe.FILE_HEADER.NumberOfSections
            features['pe_timestamp'] = pe.FILE_HEADER.TimeDateStamp
            features['pe_characteristics'] = pe.FILE_HEADER.Characteristics
            
            # Optional header features
            if hasattr(pe, 'OPTIONAL_HEADER'):
                features['pe_subsystem'] = pe.OPTIONAL_HEADER.Subsystem
                features['pe_dll_characteristics'] = pe.OPTIONAL_HEADER.DllCharacteristics
                features['pe_image_base'] = pe.OPTIONAL_HEADER.ImageBase
                features['pe_size_of_image'] = pe.OPTIONAL_HEADER.SizeOfImage
                features['pe_entry_point'] = pe.OPTIONAL_HEADER.AddressOfEntryPoint
            
            # Section features
            features.update(self._extract_section_features(pe))
            
            # Import table features
            features.update(self._extract_import_features(pe))
            
            # Export table features
            features.update(self._extract_export_features(pe))
            
            pe.close()
            
        except Exception as e:
            self.logger.warning(f"Error extracting PE features: {str(e)}")
        
        return features
    
    def _extract_section_features(self, pe):
        """Extract section features from PE file"""
        
        features = {}
        
        total_sections = len(pe.sections)
        features['pe_total_sections'] = total_sections
        
        if total_sections > 0:
            # Section characteristics
            executable_sections = 0
            writable_sections = 0
            readable_sections = 0
            
            for section in pe.sections:
                if section.Characteristics & 0x20000000:  # IMAGE_SCN_MEM_EXECUTE
                    executable_sections += 1
                if section.Characteristics & 0x80000000:  # IMAGE_SCN_MEM_WRITE
                    writable_sections += 1
                if section.Characteristics & 0x40000000:  # IMAGE_SCN_MEM_READ
                    readable_sections += 1
            
            features['pe_executable_sections'] = executable_sections
            features['pe_writable_sections'] = writable_sections
            features['pe_readable_sections'] = readable_sections
            
            # Section entropy
            entropies = []
            for section in pe.sections:
                if section.SizeOfRawData > 0:
                    entropy = self._calculate_entropy(section.get_data())
                    entropies.append(entropy)
            
            if entropies:
                features['pe_avg_section_entropy'] = sum(entropies) / len(entropies)
                features['pe_max_section_entropy'] = max(entropies)
                features['pe_min_section_entropy'] = min(entropies)
        
        return features
    
    def _extract_import_features(self, pe):
        """Extract import table features"""
        
        features = {}
        
        try:
            if hasattr(pe, 'DIRECTORY_ENTRY_IMPORT'):
                import_dlls = []
                import_functions = []
                
                for entry in pe.DIRECTORY_ENTRY_IMPORT:
                    dll_name = entry.dll.decode('utf-8').lower()
                    import_dlls.append(dll_name)
                    
                    for imp in entry.imports:
                        if imp.name:
                            import_functions.append(imp.name.decode('utf-8'))
                
                features['pe_import_dlls'] = len(import_dlls)
                features['pe_import_functions'] = len(import_functions)
                
                # Suspicious imports
                suspicious_dlls = ['kernel32.dll', 'ntdll.dll', 'advapi32.dll', 'wininet.dll']
                features['pe_suspicious_imports'] = sum(1 for dll in import_dlls if dll in suspicious_dlls)
                
        except Exception as e:
            self.logger.warning(f"Error extracting import features: {str(e)}")
        
        return features
    
    def _extract_export_features(self, pe):
        """Extract export table features"""
        
        features = {}
        
        try:
            if hasattr(pe, 'DIRECTORY_ENTRY_EXPORT'):
                features['pe_export_functions'] = len(pe.DIRECTORY_ENTRY_EXPORT.symbols)
            else:
                features['pe_export_functions'] = 0
        except Exception as e:
            self.logger.warning(f"Error extracting export features: {str(e)}")
            features['pe_export_functions'] = 0
        
        return features
    
    def _extract_string_features(self, file_path):
        """Extract string-based features"""
        
        features = {}
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # Extract strings
            strings = self._extract_strings(content)
            features['string_count'] = len(strings)
            
            # Analyze string patterns
            features.update(self._analyze_string_patterns(strings))
            
        except Exception as e:
            self.logger.warning(f"Error extracting string features: {str(e)}")
        
        return features
    
    def _extract_strings(self, content, min_length=4):
        """Extract strings from binary content"""
        
        strings = []
        
        # ASCII strings
        ascii_strings = re.findall(b'[' + string.printable.encode() + b']{' + str(min_length).encode() + b',}', content)
        strings.extend([s.decode('ascii', errors='ignore') for s in ascii_strings])
        
        # Unicode strings
        unicode_strings = re.findall(b'(?:[' + string.printable.encode() + b']\x00){' + str(min_length).encode() + b',}', content)
        strings.extend([s.decode('utf-16le', errors='ignore') for s in unicode_strings])
        
        return strings
    
    def _analyze_string_patterns(self, strings):
        """Analyze string patterns for suspicious indicators"""
        
        features = {}
        
        # URL patterns
        url_pattern = re.compile(r'https?://[^\s]+')
        urls = [s for s in strings if url_pattern.search(s)]
        features['url_count'] = len(urls)
        
        # IP address patterns
        ip_pattern = re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b')
        ips = [s for s in strings if ip_pattern.search(s)]
        features['ip_count'] = len(ips)
        
        # Registry patterns
        registry_pattern = re.compile(r'HKEY_[A-Z_]+')
        registry_keys = [s for s in strings if registry_pattern.search(s)]
        features['registry_count'] = len(registry_keys)
        
        # Suspicious keywords
        suspicious_keywords = [
            'virus', 'malware', 'trojan', 'backdoor', 'keylogger',
            'rootkit', 'botnet', 'exploit', 'payload', 'shellcode'
        ]
        
        suspicious_count = 0
        for keyword in suspicious_keywords:
            for s in strings:
                if keyword.lower() in s.lower():
                    suspicious_count += 1
                    break
        
        features['suspicious_strings'] = suspicious_count
        
        return features
    
    def _extract_entropy_features(self, file_path):
        """Extract entropy-based features"""
        
        features = {}
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # Overall entropy
            features['file_entropy'] = self._calculate_entropy(content)
            
            # Entropy distribution
            chunk_size = 1024
            entropies = []
            
            for i in range(0, len(content), chunk_size):
                chunk = content[i:i+chunk_size]
                if len(chunk) > 0:
                    entropy = self._calculate_entropy(chunk)
                    entropies.append(entropy)
            
            if entropies:
                features['avg_chunk_entropy'] = sum(entropies) / len(entropies)
                features['max_chunk_entropy'] = max(entropies)
                features['min_chunk_entropy'] = min(entropies)
            
        except Exception as e:
            self.logger.warning(f"Error extracting entropy features: {str(e)}")
        
        return features
    
    def _extract_yara_features(self, file_path):
        """Extract YARA rule matching features"""
        
        features = {}
        
        if not HAS_YARA or not self.yara_rules:
            features['yara_matches'] = 0
            return features
        
        try:
            matches = self.yara_rules.match(file_path)
            features['yara_matches'] = len(matches)
            
            # Category-specific matches
            categories = ['trojan', 'virus', 'malware', 'packer', 'crypter']
            for category in categories:
                category_matches = [m for m in matches if category in m.rule.lower()]
                features[f'yara_{category}_matches'] = len(category_matches)
            
        except Exception as e:
            self.logger.warning(f"Error in YARA matching: {str(e)}")
            features['yara_matches'] = 0
        
        return features
    
    def _calculate_hashes(self, file_path):
        """Calculate file hashes"""
        
        hashes = {}
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            hashes['md5'] = hashlib.md5(content).hexdigest()
            hashes['sha1'] = hashlib.sha1(content).hexdigest()
            hashes['sha256'] = hashlib.sha256(content).hexdigest()
            
        except Exception as e:
            self.logger.warning(f"Error calculating hashes: {str(e)}")
        
        return hashes
    
    def _calculate_entropy(self, data):
        """Calculate Shannon entropy of data"""
        
        if not data:
            return 0
        
        # Count frequency of each byte
        byte_counts = Counter(data)
        
        # Calculate entropy
        entropy = 0
        for count in byte_counts.values():
            p = count / len(data)
            entropy -= p * math.log2(p)
        
        return entropy
    
    def _is_pe_file(self, file_path):
        """Check if file is a PE file"""
        
        try:
            pe = pefile.PE(file_path)
            pe.close()
            return True
        except:
            return False
    
    def _load_yara_rules(self):
        """Load YARA rules"""
        
        if not HAS_YARA:
            self.logger.warning("YARA not available, skipping rule loading")
            return None
        
        rules_path = 'data/yara_rules'
        
        if not os.path.exists(rules_path):
            self.logger.warning(f"YARA rules directory not found: {rules_path}")
            return None
        
        try:
            # Compile all .yar files in the directory
            rules = {}
            for file in os.listdir(rules_path):
                if file.endswith('.yar'):
                    rule_path = os.path.join(rules_path, file)
                    rule_name = file[:-4]  # Remove .yar extension
                    rules[rule_name] = rule_path
            
            if rules:
                compiled_rules = yara.compile(filepaths=rules)
                self.logger.info(f"Loaded {len(rules)} YARA rules")
                return compiled_rules
            
        except Exception as e:
            self.logger.warning(f"Error loading YARA rules: {str(e)}")
        
        return None
