package com.securecloudstorage.storage.repository;

import com.securecloudstorage.storage.entity.FileAccessLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * File Access Log Repository for MariaDB
 */
@Repository
public interface FileAccessLogRepository extends JpaRepository<FileAccessLog, Long> {
    
    List<FileAccessLog> findByFileId(String fileId);
    
    List<FileAccessLog> findByUserId(String userId);
    
    Page<FileAccessLog> findByFileIdOrderByAccessDateDesc(String fileId, Pageable pageable);
    
    Page<FileAccessLog> findByUserIdOrderByAccessDateDesc(String userId, Pageable pageable);
    
    List<FileAccessLog> findByAccessType(FileAccessLog.AccessType accessType);
    
    @Query("SELECT a FROM FileAccessLog a WHERE a.accessDate BETWEEN :startDate AND :endDate")
    List<FileAccessLog> findByAccessDateBetween(@Param("startDate") LocalDateTime startDate,
                                              @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(a) FROM FileAccessLog a WHERE a.fileId = :fileId AND a.accessType = :accessType")
    Long countByFileIdAndAccessType(@Param("fileId") String fileId, 
                                   @Param("accessType") FileAccessLog.AccessType accessType);
    
    @Query("SELECT COUNT(a) FROM FileAccessLog a WHERE a.userId = :userId AND a.accessDate >= :date")
    Long countByUserIdAndAccessDateAfter(@Param("userId") String userId, 
                                        @Param("date") LocalDateTime date);
    
    @Query("SELECT a.accessType, COUNT(a) FROM FileAccessLog a GROUP BY a.accessType")
    List<Object[]> getAccessTypeStatistics();
    
    @Query("SELECT DATE(a.accessDate), COUNT(a) FROM FileAccessLog a GROUP BY DATE(a.accessDate) ORDER BY DATE(a.accessDate)")
    List<Object[]> getDailyAccessStatistics();
    
    @Query("SELECT a.userId, COUNT(a) FROM FileAccessLog a GROUP BY a.userId ORDER BY COUNT(a) DESC")
    List<Object[]> getUserAccessStatistics();
    
    @Query("SELECT a FROM FileAccessLog a WHERE a.success = false")
    List<FileAccessLog> findFailedAccesses();
    
    @Query("SELECT a FROM FileAccessLog a WHERE a.ipAddress = :ipAddress")
    List<FileAccessLog> findByIpAddress(@Param("ipAddress") String ipAddress);
    
    @Query("SELECT DISTINCT a.ipAddress FROM FileAccessLog a WHERE a.userId = :userId")
    List<String> findDistinctIpAddressesByUserId(@Param("userId") String userId);
    
    @Query("SELECT AVG(a.downloadDuration) FROM FileAccessLog a WHERE a.accessType = 'DOWNLOAD' AND a.downloadDuration IS NOT NULL")
    Double getAverageDownloadDuration();
}
