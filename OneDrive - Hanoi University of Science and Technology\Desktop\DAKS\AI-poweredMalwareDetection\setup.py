"""
Setup script for AI-Powered Malware Detection System
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

def setup_environment():
    """Setup the development environment"""
    
    print("Setting up AI-Powered Malware Detection System...")
    
    # Create necessary directories
    create_directories()
    
    # Generate sample data
    generate_sample_data()
    
    # Install dependencies
    install_dependencies()
    
    print("Setup completed successfully!")
    print("\nTo get started:")
    print("1. python main.py --train  # Train the models")
    print("2. python main.py --file <path>  # Analyze a file")
    print("3. python main.py --monitor  # Start real-time monitoring")

def create_directories():
    """Create necessary directories"""
    
    directories = [
        'data/samples',
        'data/datasets',
        'data/yara_rules',
        'models',
        'logs',
        'logs/dynamic_analysis',
        'logs/detections',
        'quarantine',
        'sandbox',
        'temp'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {directory}")

def generate_sample_data():
    """Generate sample training data"""
    
    print("Generating sample training data...")
    
    # Run the data generation script
    try:
        from src.data_preprocessing.preprocessor import generate_sample_data
        generate_sample_data('data/training_data.csv', num_samples=2000)
        print("Sample data generated successfully!")
    except Exception as e:
        print(f"Error generating sample data: {e}")

def install_dependencies():
    """Install required dependencies"""
    
    print("Installing dependencies...")
    
    try:
        # Install requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        print("Please install manually using: pip install -r requirements.txt")

if __name__ == "__main__":
    setup_environment()
