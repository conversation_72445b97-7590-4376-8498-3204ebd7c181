"""
Machine Learning Detection Module for Network IDS
================================================

Module phát hiện anomaly và attacks sử dụng ML models
"""

import os
import pickle
import time
import threading
from typing import Dict, Any, List, Optional, Tuple
import logging
import numpy as np
import pandas as pd
from datetime import datetime
from pathlib import Path

# Machine Learning imports
try:
    from sklearn.ensemble import IsolationForest, RandomForestClassifier
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, confusion_matrix
    from sklearn.externals import joblib
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, LSTM, Dropout
    from tensorflow.keras.optimizers import Adam
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    print("Warning: ML libraries not available. Detection capabilities limited.")

class AnomalyDetector:
    """Lớp phát hiện anomaly sử dụng ML"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Khởi tạo anomaly detector
        
        Args:
            config (Dict): Cấu hình ML
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.AnomalyDetector")
        
        # Models
        self.isolation_forest = None
        self.autoencoder = None
        self.lstm_model = None
        self.random_forest = None
        
        # Preprocessing
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
        # Feature names
        self.feature_names = []
        self.is_trained = False
        
        # Runtime data
        self.training_data = []
        self.prediction_history = []
        self.model_performance = {}
        
        self.logger.info("AnomalyDetector initialized")
    
    def prepare_features(self, data: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        Chuẩn bị features cho ML
        
        Args:
            data: Danh sách feature dictionaries
            
        Returns:
            pd.DataFrame: Features đã chuẩn bị
        """
        try:
            # Convert to DataFrame
            df = pd.DataFrame(data)
            
            # Handle missing values
            df = df.fillna(0)
            
            # Select numeric features only
            numeric_features = df.select_dtypes(include=[np.number]).columns
            df = df[numeric_features]
            
            # Store feature names
            self.feature_names = list(df.columns)
            
            self.logger.info(f"Prepared {len(df)} samples with {len(self.feature_names)} features")
            return df
            
        except Exception as e:
            self.logger.error(f"Error preparing features: {str(e)}")
            return pd.DataFrame()
    
    def train_isolation_forest(self, X: pd.DataFrame) -> bool:
        """
        Huấn luyện Isolation Forest
        
        Args:
            X: Training features
            
        Returns:
            bool: True nếu thành công
        """
        if not ML_AVAILABLE:
            return False
        
        try:
            config = self.config['models']['isolation_forest']
            
            self.isolation_forest = IsolationForest(
                contamination=config.get('contamination', 0.1),
                n_estimators=config.get('n_estimators', 100),
                max_samples=config.get('max_samples', 256),
                random_state=42
            )
            
            self.isolation_forest.fit(X)
            self.logger.info("Isolation Forest training completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error training Isolation Forest: {str(e)}")
            return False
    
    def train_autoencoder(self, X: pd.DataFrame) -> bool:
        """
        Huấn luyện Autoencoder
        
        Args:
            X: Training features
            
        Returns:
            bool: True nếu thành công
        """
        if not ML_AVAILABLE:
            return False
        
        try:
            config = self.config['models']['autoencoder']
            
            # Normalize data
            X_scaled = self.scaler.fit_transform(X)
            
            # Build autoencoder
            input_dim = X_scaled.shape[1]
            encoding_dim = config.get('encoding_dim', 32)
            
            self.autoencoder = Sequential([
                Dense(encoding_dim * 2, activation='relu', input_shape=(input_dim,)),
                Dense(encoding_dim, activation='relu'),
                Dense(encoding_dim * 2, activation='relu'),
                Dense(input_dim, activation='sigmoid')
            ])
            
            self.autoencoder.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='mse'
            )
            
            # Train
            history = self.autoencoder.fit(
                X_scaled, X_scaled,
                epochs=config.get('epochs', 50),
                batch_size=config.get('batch_size', 32),
                validation_split=config.get('validation_split', 0.2),
                verbose=0
            )
            
            # Calculate reconstruction threshold
            reconstructions = self.autoencoder.predict(X_scaled)
            mse = np.mean(np.power(X_scaled - reconstructions, 2), axis=1)
            self.reconstruction_threshold = np.percentile(mse, 95)
            
            self.logger.info("Autoencoder training completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error training Autoencoder: {str(e)}")
            return False
    
    def train_lstm(self, X: pd.DataFrame) -> bool:
        """
        Huấn luyện LSTM model
        
        Args:
            X: Training features
            
        Returns:
            bool: True nếu thành công
        """
        if not ML_AVAILABLE:
            return False
        
        try:
            config = self.config['models']['lstm']
            
            # Prepare sequence data
            sequence_length = config.get('sequence_length', 10)
            X_seq, y_seq = self._prepare_sequence_data(X, sequence_length)
            
            if len(X_seq) == 0:
                self.logger.warning("Insufficient data for LSTM training")
                return False
            
            # Build LSTM model
            self.lstm_model = Sequential([
                LSTM(config.get('hidden_units', 50), return_sequences=True, 
                     input_shape=(sequence_length, X.shape[1])),
                Dropout(0.2),
                LSTM(config.get('hidden_units', 50)),
                Dropout(0.2),
                Dense(1, activation='sigmoid')
            ])
            
            self.lstm_model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='binary_crossentropy',
                metrics=['accuracy']
            )
            
            # Create labels (assume normal traffic for unsupervised learning)
            y_labels = np.zeros(len(X_seq))
            
            # Train
            history = self.lstm_model.fit(
                X_seq, y_labels,
                epochs=config.get('epochs', 30),
                batch_size=config.get('batch_size', 32),
                validation_split=0.2,
                verbose=0
            )
            
            self.logger.info("LSTM training completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error training LSTM: {str(e)}")
            return False
    
    def train_random_forest(self, X: pd.DataFrame, y: pd.Series = None) -> bool:
        """
        Huấn luyện Random Forest (supervised)
        
        Args:
            X: Training features
            y: Labels (optional)
            
        Returns:
            bool: True nếu thành công
        """
        if not ML_AVAILABLE:
            return False
        
        try:
            config = self.config['models']['random_forest']
            
            # If no labels provided, create synthetic labels
            if y is None:
                y = np.zeros(len(X))  # Assume all normal for unsupervised
            
            self.random_forest = RandomForestClassifier(
                n_estimators=config.get('n_estimators', 100),
                max_depth=config.get('max_depth', 10),
                min_samples_split=config.get('min_samples_split', 2),
                random_state=42
            )
            
            self.random_forest.fit(X, y)
            self.logger.info("Random Forest training completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error training Random Forest: {str(e)}")
            return False
    
    def _prepare_sequence_data(self, X: pd.DataFrame, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        Chuẩn bị dữ liệu sequence cho LSTM
        
        Args:
            X: Features
            sequence_length: Độ dài sequence
            
        Returns:
            Tuple: (X_sequences, y_sequences)
        """
        sequences = []
        targets = []
        
        for i in range(len(X) - sequence_length):
            seq = X.iloc[i:i+sequence_length].values
            target = X.iloc[i+sequence_length].values
            sequences.append(seq)
            targets.append(target)
        
        return np.array(sequences), np.array(targets)
    
    def predict_anomaly(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Dự đoán anomaly cho một sample
        
        Args:
            features: Feature dictionary
            
        Returns:
            Dict: Kết quả prediction
        """
        if not self.is_trained:
            return {'error': 'Models not trained'}
        
        try:
            # Prepare features
            df = pd.DataFrame([features])
            df = df.reindex(columns=self.feature_names, fill_value=0)
            
            results = {
                'is_anomaly': False,
                'confidence': 0.0,
                'model_predictions': {},
                'timestamp': time.time()
            }
            
            # Isolation Forest prediction
            if self.isolation_forest:
                prediction = self.isolation_forest.predict(df)[0]
                score = self.isolation_forest.score_samples(df)[0]
                
                results['model_predictions']['isolation_forest'] = {
                    'prediction': prediction,
                    'score': score,
                    'is_anomaly': prediction == -1
                }
            
            # Autoencoder prediction
            if self.autoencoder:
                X_scaled = self.scaler.transform(df)
                reconstruction = self.autoencoder.predict(X_scaled)
                mse = np.mean(np.power(X_scaled - reconstruction, 2))
                
                is_anomaly = mse > self.reconstruction_threshold
                
                results['model_predictions']['autoencoder'] = {
                    'reconstruction_error': mse,
                    'threshold': self.reconstruction_threshold,
                    'is_anomaly': is_anomaly
                }
            
            # LSTM prediction
            if self.lstm_model and len(self.prediction_history) >= 10:
                # Use recent history for sequence prediction
                recent_history = self.prediction_history[-10:]
                seq_data = np.array([recent_history])
                prediction = self.lstm_model.predict(seq_data)[0][0]
                
                results['model_predictions']['lstm'] = {
                    'prediction': prediction,
                    'is_anomaly': prediction > 0.5
                }
            
            # Random Forest prediction
            if self.random_forest:
                prediction = self.random_forest.predict(df)[0]
                probability = self.random_forest.predict_proba(df)[0]
                
                results['model_predictions']['random_forest'] = {
                    'prediction': prediction,
                    'probability': probability.tolist(),
                    'is_anomaly': prediction == 1
                }
            
            # Ensemble decision
            anomaly_votes = sum(1 for model_result in results['model_predictions'].values() 
                              if model_result.get('is_anomaly', False))
            
            total_models = len(results['model_predictions'])
            if total_models > 0:
                results['confidence'] = anomaly_votes / total_models
                results['is_anomaly'] = results['confidence'] > 0.5
            
            # Update history
            self.prediction_history.append(features)
            if len(self.prediction_history) > 1000:
                self.prediction_history = self.prediction_history[-1000:]
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error predicting anomaly: {str(e)}")
            return {'error': str(e)}
    
    def train_models(self, training_data: List[Dict[str, Any]]) -> bool:
        """
        Huấn luyện tất cả models
        
        Args:
            training_data: Dữ liệu huấn luyện
            
        Returns:
            bool: True nếu thành công
        """
        if not ML_AVAILABLE:
            self.logger.error("ML libraries not available")
            return False
        
        if len(training_data) < 100:
            self.logger.error("Insufficient training data")
            return False
        
        try:
            # Prepare features
            X = self.prepare_features(training_data)
            if X.empty:
                self.logger.error("No valid features extracted")
                return False
            
            # Train enabled models
            success_count = 0
            
            if self.config['models']['isolation_forest']['enabled']:
                if self.train_isolation_forest(X):
                    success_count += 1
            
            if self.config['models']['autoencoder']['enabled']:
                if self.train_autoencoder(X):
                    success_count += 1
            
            if self.config['models']['lstm']['enabled']:
                if self.train_lstm(X):
                    success_count += 1
            
            if self.config['models']['random_forest']['enabled']:
                if self.train_random_forest(X):
                    success_count += 1
            
            # Mark as trained if at least one model succeeded
            self.is_trained = success_count > 0
            
            if self.is_trained:
                self.logger.info(f"Successfully trained {success_count} models")
                return True
            else:
                self.logger.error("No models trained successfully")
                return False
            
        except Exception as e:
            self.logger.error(f"Error training models: {str(e)}")
            return False
    
    def save_models(self, model_path: str) -> bool:
        """
        Lưu models
        
        Args:
            model_path: Đường dẫn lưu models
            
        Returns:
            bool: True nếu thành công
        """
        try:
            Path(model_path).mkdir(parents=True, exist_ok=True)
            
            # Save sklearn models
            if self.isolation_forest:
                joblib.dump(self.isolation_forest, f"{model_path}/isolation_forest.pkl")
            
            if self.random_forest:
                joblib.dump(self.random_forest, f"{model_path}/random_forest.pkl")
            
            # Save preprocessing objects
            joblib.dump(self.scaler, f"{model_path}/scaler.pkl")
            joblib.dump(self.feature_names, f"{model_path}/feature_names.pkl")
            
            # Save TensorFlow models
            if self.autoencoder:
                self.autoencoder.save(f"{model_path}/autoencoder.h5")
                # Save threshold
                with open(f"{model_path}/reconstruction_threshold.txt", 'w') as f:
                    f.write(str(self.reconstruction_threshold))
            
            if self.lstm_model:
                self.lstm_model.save(f"{model_path}/lstm_model.h5")
            
            self.logger.info(f"Models saved to {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving models: {str(e)}")
            return False
    
    def load_models(self, model_path: str) -> bool:
        """
        Tải models
        
        Args:
            model_path: Đường dẫn models
            
        Returns:
            bool: True nếu thành công
        """
        try:
            model_path = Path(model_path)
            
            if not model_path.exists():
                self.logger.error(f"Model path does not exist: {model_path}")
                return False
            
            # Load sklearn models
            if (model_path / "isolation_forest.pkl").exists():
                self.isolation_forest = joblib.load(model_path / "isolation_forest.pkl")
            
            if (model_path / "random_forest.pkl").exists():
                self.random_forest = joblib.load(model_path / "random_forest.pkl")
            
            # Load preprocessing objects
            if (model_path / "scaler.pkl").exists():
                self.scaler = joblib.load(model_path / "scaler.pkl")
            
            if (model_path / "feature_names.pkl").exists():
                self.feature_names = joblib.load(model_path / "feature_names.pkl")
            
            # Load TensorFlow models
            if (model_path / "autoencoder.h5").exists():
                self.autoencoder = keras.models.load_model(model_path / "autoencoder.h5")
                
                # Load threshold
                threshold_file = model_path / "reconstruction_threshold.txt"
                if threshold_file.exists():
                    with open(threshold_file, 'r') as f:
                        self.reconstruction_threshold = float(f.read().strip())
            
            if (model_path / "lstm_model.h5").exists():
                self.lstm_model = keras.models.load_model(model_path / "lstm_model.h5")
            
            self.is_trained = True
            self.logger.info(f"Models loaded from {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading models: {str(e)}")
            return False

class MLDetector:
    """Lớp chính cho ML detection"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Khởi tạo ML detector
        
        Args:
            config: Cấu hình ML
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.MLDetector")
        
        # Anomaly detector
        self.anomaly_detector = AnomalyDetector(config)
        
        # Training configuration
        self.training_config = config.get('training', {})
        self.detection_config = config.get('detection', {})
        
        # Model path
        self.model_path = self.training_config.get('model_path', 'models/')
        
        # Try to load existing models
        self.load_models()
        
        self.logger.info("MLDetector initialized")
    
    def detect_anomaly(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Phát hiện anomaly
        
        Args:
            features: Feature dictionary
            
        Returns:
            Dict: Kết quả detection
        """
        return self.anomaly_detector.predict_anomaly(features)
    
    def train_models(self, training_data: List[Dict[str, Any]] = None) -> bool:
        """
        Huấn luyện models
        
        Args:
            training_data: Dữ liệu huấn luyện (optional)
            
        Returns:
            bool: True nếu thành công
        """
        if training_data is None:
            # Load training data from file
            training_data = self._load_training_data()
        
        if not training_data:
            self.logger.error("No training data available")
            return False
        
        # Train models
        success = self.anomaly_detector.train_models(training_data)
        
        if success:
            # Save models
            self.save_models()
        
        return success
    
    def _load_training_data(self) -> List[Dict[str, Any]]:
        """
        Tải dữ liệu huấn luyện
        
        Returns:
            List: Dữ liệu huấn luyện
        """
        data_path = self.training_config.get('data_path', 'data/training/')
        
        # This is a placeholder - in reality, you'd load from files
        # For now, generate synthetic data
        return self._generate_synthetic_data()
    
    def _generate_synthetic_data(self) -> List[Dict[str, Any]]:
        """
        Tạo dữ liệu synthetic cho demo
        
        Returns:
            List: Dữ liệu synthetic
        """
        np.random.seed(42)
        data = []
        
        # Generate normal traffic patterns
        for i in range(1000):
            sample = {
                'packet_size': np.random.normal(500, 100),
                'flow_packet_count': np.random.poisson(10),
                'flow_duration': np.random.exponential(30),
                'packets_per_second': np.random.normal(2, 0.5),
                'bytes_per_second': np.random.normal(1000, 200),
                'avg_packet_size': np.random.normal(500, 100),
                'inter_arrival_time': np.random.exponential(0.5),
                'tcp_flags': np.random.randint(0, 32),
                'payload_entropy': np.random.normal(3, 1),
                'is_tcp': np.random.choice([0, 1], p=[0.3, 0.7]),
                'is_udp': np.random.choice([0, 1], p=[0.7, 0.3]),
                'hour_of_day': np.random.randint(0, 24),
                'day_of_week': np.random.randint(0, 7)
            }
            data.append(sample)
        
        # Generate some anomalous patterns
        for i in range(100):
            sample = {
                'packet_size': np.random.normal(1500, 200),  # Larger packets
                'flow_packet_count': np.random.poisson(100),  # More packets
                'flow_duration': np.random.exponential(5),   # Shorter duration
                'packets_per_second': np.random.normal(20, 5),  # Higher rate
                'bytes_per_second': np.random.normal(10000, 2000),  # Higher bandwidth
                'avg_packet_size': np.random.normal(1500, 200),
                'inter_arrival_time': np.random.exponential(0.05),  # Faster
                'tcp_flags': np.random.randint(0, 32),
                'payload_entropy': np.random.normal(1, 0.5),  # Lower entropy
                'is_tcp': 1,
                'is_udp': 0,
                'hour_of_day': np.random.randint(0, 24),
                'day_of_week': np.random.randint(0, 7)
            }
            data.append(sample)
        
        return data
    
    def save_models(self) -> bool:
        """
        Lưu models
        
        Returns:
            bool: True nếu thành công
        """
        return self.anomaly_detector.save_models(self.model_path)
    
    def load_models(self) -> bool:
        """
        Tải models
        
        Returns:
            bool: True nếu thành công
        """
        return self.anomaly_detector.load_models(self.model_path)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Lấy thông tin về models
        
        Returns:
            Dict: Thông tin models
        """
        return {
            'is_trained': self.anomaly_detector.is_trained,
            'feature_count': len(self.anomaly_detector.feature_names),
            'feature_names': self.anomaly_detector.feature_names,
            'models_available': {
                'isolation_forest': self.anomaly_detector.isolation_forest is not None,
                'autoencoder': self.anomaly_detector.autoencoder is not None,
                'lstm': self.anomaly_detector.lstm_model is not None,
                'random_forest': self.anomaly_detector.random_forest is not None
            }
        }
