package com.securecloudstorage.monitoring.service;

import com.securecloudstorage.monitoring.dto.ServiceHealthResponse;
import com.securecloudstorage.monitoring.entity.HealthCheck;
import com.securecloudstorage.monitoring.repository.HealthCheckRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Health Check Service
 * Dịch vụ kiểm tra health của các services
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HealthCheckService {

    private final HealthCheckRepository healthCheckRepository;
    private final RestTemplate restTemplate;
    private final AlertService alertService;

    // Service endpoints configuration
    private final Map<String, String> serviceEndpoints = Map.of(
        "api-gateway", "http://localhost:8080/actuator/health",
        "storage-service", "http://localhost:8082/actuator/health",
        "user-service", "http://localhost:8083/actuator/health",
        "security-service", "http://localhost:8084/actuator/health",
        "monitoring-service", "http://localhost:8085/actuator/health"
    );

    // Cache for health status
    private final Map<String, ServiceHealthResponse> healthCache = new ConcurrentHashMap<>();

    /**
     * Kiểm tra health của tất cả services
     */
    public List<ServiceHealthResponse> checkAllServices() {
        List<ServiceHealthResponse> healthResponses = new ArrayList<>();
        
        try {
            // Create parallel health checks
            List<CompletableFuture<ServiceHealthResponse>> futures = serviceEndpoints.entrySet().stream()
                .map(entry -> CompletableFuture.supplyAsync(() -> checkService(entry.getKey())))
                .toList();
            
            // Wait for all health checks to complete
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .join();
            
            // Collect results
            for (CompletableFuture<ServiceHealthResponse> future : futures) {
                try {
                    healthResponses.add(future.get());
                } catch (Exception e) {
                    log.error("Error getting health check result: ", e);
                }
            }
            
            return healthResponses;
            
        } catch (Exception e) {
            log.error("Error checking all services health: ", e);
            return healthResponses;
        }
    }

    /**
     * Kiểm tra health của service cụ thể
     */
    public ServiceHealthResponse checkService(String serviceName) {
        try {
            // Check cache first
            ServiceHealthResponse cached = healthCache.get(serviceName);
            if (cached != null && isRecentCheck(cached.getLastCheck())) {
                return cached;
            }
            
            ServiceHealthResponse response = new ServiceHealthResponse();
            response.setServiceName(serviceName);
            response.setLastCheck(LocalDateTime.now());
            
            String healthUrl = serviceEndpoints.get(serviceName);
            if (healthUrl == null) {
                return createUnknownServiceResponse(serviceName);
            }
            
            response.setUrl(healthUrl);
            
            // Perform health check
            long startTime = System.currentTimeMillis();
            
            try {
                Map<String, Object> healthData = restTemplate.getForObject(healthUrl, Map.class);
                long responseTime = System.currentTimeMillis() - startTime;
                
                response.setResponseTime(responseTime);
                response.setStatus("UP");
                response.setHealth("HEALTHY");
                response.setDescription("Service is healthy");
                
                // Parse health data
                if (healthData != null) {
                    parseHealthData(response, healthData);
                }
                
            } catch (ResourceAccessException e) {
                long responseTime = System.currentTimeMillis() - startTime;
                response.setResponseTime(responseTime);
                response.setStatus("DOWN");
                response.setHealth("CRITICAL");
                response.setDescription("Service is not responding: " + e.getMessage());
                
                // Create alert for down service
                createServiceDownAlert(serviceName, e.getMessage());
            }
            
            // Add service-specific checks
            addServiceSpecificChecks(response);
            
            // Check dependencies
            checkDependencies(response);
            
            // Set metadata
            setMetadata(response);
            
            // Save to database
            saveHealthCheck(response);
            
            // Update cache
            healthCache.put(serviceName, response);
            
            return response;
            
        } catch (Exception e) {
            log.error("Error checking health for service {}: ", serviceName, e);
            return createErrorResponse(serviceName, e.getMessage());
        }
    }

    /**
     * Scheduled health check cho tất cả services
     */
    @Scheduled(fixedRate = 30000) // Every 30 seconds
    public void scheduledHealthCheck() {
        try {
            log.debug("Starting scheduled health check...");
            
            List<ServiceHealthResponse> healthResponses = checkAllServices();
            
            // Analyze health trends
            analyzeHealthTrends(healthResponses);
            
            // Check for service issues
            checkForServiceIssues(healthResponses);
            
            log.debug("Scheduled health check completed");
            
        } catch (Exception e) {
            log.error("Error during scheduled health check: ", e);
        }
    }

    /**
     * Kiểm tra health của database
     */
    public ServiceHealthResponse checkDatabaseHealth(String databaseName) {
        ServiceHealthResponse response = new ServiceHealthResponse();
        response.setServiceName("database-" + databaseName);
        response.setLastCheck(LocalDateTime.now());
        
        try {
            // Mock database health check
            response.setStatus("UP");
            response.setHealth("HEALTHY");
            response.setDescription("Database is healthy");
            response.setResponseTime(25L);
            
            // Add database-specific metrics
            Map<String, Object> details = new HashMap<>();
            details.put("connectionCount", 45);
            details.put("maxConnections", 100);
            details.put("queryTime", 15.5);
            details.put("storage", "65% used");
            response.setDetails(details);
            
            return response;
            
        } catch (Exception e) {
            log.error("Error checking database health: ", e);
            response.setStatus("DOWN");
            response.setHealth("CRITICAL");
            response.setDescription("Database check failed: " + e.getMessage());
            return response;
        }
    }

    /**
     * Kiểm tra health của external services
     */
    public ServiceHealthResponse checkExternalServiceHealth(String serviceName, String url) {
        ServiceHealthResponse response = new ServiceHealthResponse();
        response.setServiceName("external-" + serviceName);
        response.setUrl(url);
        response.setLastCheck(LocalDateTime.now());
        
        try {
            long startTime = System.currentTimeMillis();
            
            // Simple HTTP health check
            restTemplate.getForObject(url, String.class);
            
            long responseTime = System.currentTimeMillis() - startTime;
            
            response.setResponseTime(responseTime);
            response.setStatus("UP");
            response.setHealth("HEALTHY");
            response.setDescription("External service is reachable");
            
            return response;
            
        } catch (Exception e) {
            log.error("Error checking external service {}: ", serviceName, e);
            response.setStatus("DOWN");
            response.setHealth("CRITICAL");
            response.setDescription("External service is not reachable: " + e.getMessage());
            return response;
        }
    }

    /**
     * Lấy health history
     */
    public List<HealthCheck> getHealthHistory(String serviceName, int days) {
        try {
            LocalDateTime since = LocalDateTime.now().minusDays(days);
            return healthCheckRepository.findByServiceNameAndTimestampAfterOrderByTimestampDesc(
                serviceName, since);
        } catch (Exception e) {
            log.error("Error getting health history for {}: ", serviceName, e);
            return new ArrayList<>();
        }
    }

    /**
     * Lấy health summary
     */
    public Map<String, Object> getHealthSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        try {
            List<ServiceHealthResponse> healthResponses = checkAllServices();
            
            int totalServices = healthResponses.size();
            int healthyServices = (int) healthResponses.stream()
                .filter(r -> "UP".equals(r.getStatus()))
                .count();
            int warningServices = (int) healthResponses.stream()
                .filter(r -> "WARNING".equals(r.getHealth()))
                .count();
            int criticalServices = (int) healthResponses.stream()
                .filter(r -> "CRITICAL".equals(r.getHealth()))
                .count();
            int downServices = (int) healthResponses.stream()
                .filter(r -> "DOWN".equals(r.getStatus()))
                .count();
            
            summary.put("totalServices", totalServices);
            summary.put("healthyServices", healthyServices);
            summary.put("warningServices", warningServices);
            summary.put("criticalServices", criticalServices);
            summary.put("downServices", downServices);
            summary.put("overallHealth", calculateOverallHealth(healthResponses));
            summary.put("lastCheck", LocalDateTime.now());
            
            return summary;
            
        } catch (Exception e) {
            log.error("Error getting health summary: ", e);
            summary.put("error", "Failed to get health summary");
            return summary;
        }
    }

    // Private helper methods

    private boolean isRecentCheck(LocalDateTime lastCheck) {
        return lastCheck != null && 
               lastCheck.isAfter(LocalDateTime.now().minusMinutes(1));
    }

    private ServiceHealthResponse createUnknownServiceResponse(String serviceName) {
        ServiceHealthResponse response = new ServiceHealthResponse();
        response.setServiceName(serviceName);
        response.setStatus("UNKNOWN");
        response.setHealth("UNKNOWN");
        response.setDescription("Service endpoint not configured");
        response.setLastCheck(LocalDateTime.now());
        return response;
    }

    private ServiceHealthResponse createErrorResponse(String serviceName, String error) {
        ServiceHealthResponse response = new ServiceHealthResponse();
        response.setServiceName(serviceName);
        response.setStatus("DOWN");
        response.setHealth("CRITICAL");
        response.setDescription("Health check failed: " + error);
        response.setLastCheck(LocalDateTime.now());
        return response;
    }

    private void parseHealthData(ServiceHealthResponse response, Map<String, Object> healthData) {
        try {
            // Parse status
            String status = (String) healthData.get("status");
            if ("UP".equals(status)) {
                response.setStatus("UP");
                response.setHealth("HEALTHY");
            } else if ("DOWN".equals(status)) {
                response.setStatus("DOWN");
                response.setHealth("CRITICAL");
            } else {
                response.setStatus("UNKNOWN");
                response.setHealth("WARNING");
            }
            
            // Parse components
            @SuppressWarnings("unchecked")
            Map<String, Object> components = (Map<String, Object>) healthData.get("components");
            if (components != null) {
                List<ServiceHealthResponse.HealthCheck> checks = new ArrayList<>();
                
                for (Map.Entry<String, Object> entry : components.entrySet()) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> component = (Map<String, Object>) entry.getValue();
                    
                    ServiceHealthResponse.HealthCheck check = new ServiceHealthResponse.HealthCheck();
                    check.setName(entry.getKey());
                    check.setStatus((String) component.get("status"));
                    check.setTimestamp(LocalDateTime.now());
                    
                    @SuppressWarnings("unchecked")
                    Map<String, Object> details = (Map<String, Object>) component.get("details");
                    if (details != null) {
                        check.setDetails(details);
                    }
                    
                    checks.add(check);
                }
                
                response.setChecks(checks);
            }
            
            // Set details
            response.setDetails(healthData);
            
        } catch (Exception e) {
            log.error("Error parsing health data: ", e);
        }
    }

    private void addServiceSpecificChecks(ServiceHealthResponse response) {
        String serviceName = response.getServiceName();
        
        // Add service-specific health checks
        List<ServiceHealthResponse.HealthCheck> checks = response.getChecks();
        if (checks == null) {
            checks = new ArrayList<>();
        }
        
        // Custom checks based on service type
        switch (serviceName) {
            case "api-gateway":
                addApiGatewayChecks(checks);
                break;
            case "storage-service":
                addStorageServiceChecks(checks);
                break;
            case "user-service":
                addUserServiceChecks(checks);
                break;
            case "security-service":
                addSecurityServiceChecks(checks);
                break;
            default:
                break;
        }
        
        response.setChecks(checks);
    }

    private void addApiGatewayChecks(List<ServiceHealthResponse.HealthCheck> checks) {
        // Check rate limiting
        ServiceHealthResponse.HealthCheck rateLimitCheck = new ServiceHealthResponse.HealthCheck();
        rateLimitCheck.setName("rate-limiting");
        rateLimitCheck.setStatus("UP");
        rateLimitCheck.setDescription("Rate limiting is functional");
        rateLimitCheck.setTimestamp(LocalDateTime.now());
        checks.add(rateLimitCheck);
        
        // Check circuit breaker
        ServiceHealthResponse.HealthCheck circuitBreakerCheck = new ServiceHealthResponse.HealthCheck();
        circuitBreakerCheck.setName("circuit-breaker");
        circuitBreakerCheck.setStatus("UP");
        circuitBreakerCheck.setDescription("Circuit breaker is healthy");
        circuitBreakerCheck.setTimestamp(LocalDateTime.now());
        checks.add(circuitBreakerCheck);
    }

    private void addStorageServiceChecks(List<ServiceHealthResponse.HealthCheck> checks) {
        // Check disk space
        ServiceHealthResponse.HealthCheck diskCheck = new ServiceHealthResponse.HealthCheck();
        diskCheck.setName("disk-space");
        diskCheck.setStatus("UP");
        diskCheck.setDescription("Sufficient disk space available");
        diskCheck.setTimestamp(LocalDateTime.now());
        checks.add(diskCheck);
        
        // Check encryption service
        ServiceHealthResponse.HealthCheck encryptionCheck = new ServiceHealthResponse.HealthCheck();
        encryptionCheck.setName("encryption");
        encryptionCheck.setStatus("UP");
        encryptionCheck.setDescription("Encryption service is operational");
        encryptionCheck.setTimestamp(LocalDateTime.now());
        checks.add(encryptionCheck);
    }

    private void addUserServiceChecks(List<ServiceHealthResponse.HealthCheck> checks) {
        // Check JWT validation
        ServiceHealthResponse.HealthCheck jwtCheck = new ServiceHealthResponse.HealthCheck();
        jwtCheck.setName("jwt-validation");
        jwtCheck.setStatus("UP");
        jwtCheck.setDescription("JWT validation is working");
        jwtCheck.setTimestamp(LocalDateTime.now());
        checks.add(jwtCheck);
    }

    private void addSecurityServiceChecks(List<ServiceHealthResponse.HealthCheck> checks) {
        // Check threat detection
        ServiceHealthResponse.HealthCheck threatCheck = new ServiceHealthResponse.HealthCheck();
        threatCheck.setName("threat-detection");
        threatCheck.setStatus("UP");
        threatCheck.setDescription("Threat detection is active");
        threatCheck.setTimestamp(LocalDateTime.now());
        checks.add(threatCheck);
    }

    private void checkDependencies(ServiceHealthResponse response) {
        List<ServiceHealthResponse.DependencyHealth> dependencies = new ArrayList<>();
        
        // Add database dependency
        ServiceHealthResponse.DependencyHealth dbDependency = new ServiceHealthResponse.DependencyHealth();
        dbDependency.setName("database");
        dbDependency.setType("DATABASE");
        dbDependency.setStatus("UP");
        dbDependency.setHost("localhost");
        dbDependency.setPort(5432);
        dbDependency.setResponseTime(25L);
        dbDependency.setLastCheck(LocalDateTime.now());
        dependencies.add(dbDependency);
        
        // Add Redis dependency (if applicable)
        if ("api-gateway".equals(response.getServiceName())) {
            ServiceHealthResponse.DependencyHealth redisDependency = new ServiceHealthResponse.DependencyHealth();
            redisDependency.setName("redis");
            redisDependency.setType("DATABASE");
            redisDependency.setStatus("UP");
            redisDependency.setHost("localhost");
            redisDependency.setPort(6379);
            redisDependency.setResponseTime(15L);
            redisDependency.setLastCheck(LocalDateTime.now());
            dependencies.add(redisDependency);
        }
        
        response.setDependencies(dependencies);
        response.setIsDependencyHealthy(dependencies.stream()
            .allMatch(dep -> "UP".equals(dep.getStatus())));
    }

    private void setMetadata(ServiceHealthResponse response) {
        Map<String, String> metadata = new HashMap<>();
        metadata.put("version", "1.0.0");
        metadata.put("environment", "production");
        metadata.put("profile", "prod");
        metadata.put("buildTime", "2024-01-01T00:00:00Z");
        response.setMetadata(metadata);
    }

    private void saveHealthCheck(ServiceHealthResponse response) {
        try {
            HealthCheck healthCheck = new HealthCheck();
            healthCheck.setServiceName(response.getServiceName());
            healthCheck.setStatus(response.getStatus());
            healthCheck.setHealth(response.getHealth());
            healthCheck.setResponseTime(response.getResponseTime());
            healthCheck.setTimestamp(response.getLastCheck());
            healthCheck.setDescription(response.getDescription());
            
            healthCheckRepository.save(healthCheck);
        } catch (Exception e) {
            log.error("Error saving health check: ", e);
        }
    }

    private void createServiceDownAlert(String serviceName, String message) {
        try {
            Map<String, Object> alertData = new HashMap<>();
            alertData.put("title", "Service Down Alert");
            alertData.put("description", "Service " + serviceName + " is down: " + message);
            alertData.put("severity", "CRITICAL");
            alertData.put("service", serviceName);
            alertData.put("type", "SERVICE_DOWN");
            
            alertService.createAlert(alertData);
        } catch (Exception e) {
            log.error("Error creating service down alert: ", e);
        }
    }

    private void analyzeHealthTrends(List<ServiceHealthResponse> healthResponses) {
        // Analyze health trends and patterns
        for (ServiceHealthResponse response : healthResponses) {
            try {
                List<HealthCheck> history = getHealthHistory(response.getServiceName(), 1);
                
                // Check for degrading performance
                if (history.size() > 1) {
                    HealthCheck latest = history.get(0);
                    HealthCheck previous = history.get(1);
                    
                    if (latest.getResponseTime() != null && previous.getResponseTime() != null) {
                        double performanceChange = (latest.getResponseTime() - previous.getResponseTime()) 
                                                 / (double) previous.getResponseTime() * 100;
                        
                        if (performanceChange > 50) {
                            log.warn("Performance degradation detected for service {}: {}% increase in response time",
                                response.getServiceName(), performanceChange);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error analyzing health trends for {}: ", response.getServiceName(), e);
            }
        }
    }

    private void checkForServiceIssues(List<ServiceHealthResponse> healthResponses) {
        for (ServiceHealthResponse response : healthResponses) {
            try {
                // Check for high response times
                if (response.getResponseTime() != null && response.getResponseTime() > 5000) {
                    log.warn("High response time detected for service {}: {}ms",
                        response.getServiceName(), response.getResponseTime());
                }
                
                // Check for service down
                if ("DOWN".equals(response.getStatus())) {
                    log.error("Service {} is down", response.getServiceName());
                }
                
                // Check for critical health
                if ("CRITICAL".equals(response.getHealth())) {
                    log.error("Service {} has critical health issues", response.getServiceName());
                }
            } catch (Exception e) {
                log.error("Error checking service issues for {}: ", response.getServiceName(), e);
            }
        }
    }

    private double calculateOverallHealth(List<ServiceHealthResponse> healthResponses) {
        if (healthResponses.isEmpty()) return 0.0;
        
        int totalServices = healthResponses.size();
        int healthyServices = (int) healthResponses.stream()
            .filter(r -> "UP".equals(r.getStatus()) && "HEALTHY".equals(r.getHealth()))
            .count();
        
        return (double) healthyServices / totalServices * 100;
    }
}
