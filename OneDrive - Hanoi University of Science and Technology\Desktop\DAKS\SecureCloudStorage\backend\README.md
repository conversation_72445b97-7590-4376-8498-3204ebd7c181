# Secure Cloud Storage Backend API Documentation

## Overview
Hệ thống Secure Cloud Storage Backend bao gồm các microservices tích hợp Network IDS và AI-Malware Detection để cung cấp giải pháp lưu trữ đám mây an toàn.

## Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │ Storage Service │    │  User Service   │
│   (Port 8080)   │    │   (Port 8082)   │    │   (Port 8083)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│Security Service │    │Monitoring Service│    │   Network IDS   │
│   (Port 8084)   │    │   (Port 8085)   │    │   (Port 5000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  AI-Malware     │
                    │  Detection      │
                    │  (Port 8086)    │
                    └─────────────────┘
```

## Services

### 1. API Gateway (Port 8080)
**Chức năng**: Điểm vào duy nhất cho tất cả API calls, xử lý authentication, rate limiting, và routing.

**Features**:
- JWT Authentication
- Rate Limiting với Redis
- Circuit Breaker Pattern
- CORS Configuration
- Security Filtering tích hợp Network IDS và AI-Malware Detection

**Endpoints**:
- `/api/storage/**` → Storage Service
- `/api/users/**` → User Service
- `/api/security/**` → Security Service
- `/api/monitoring/**` → Monitoring Service
- `/api/ids/**` → Network IDS
- `/api/malware/**` → AI-Malware Detection

### 2. Storage Service (Port 8082)
**Chức năng**: Quản lý file upload, download, encryption, và AI-Malware scanning.

**Features**:
- File Upload với size limit (100MB)
- File Encryption/Decryption
- AI-Malware Scanning tự động
- File Quarantine
- File Sharing
- Access Control

**API Endpoints**:

#### Upload File
```http
POST /api/files/upload
Content-Type: multipart/form-data
Authorization: Bearer <token>

Parameters:
- file: MultipartFile (required)
- description: String (optional)

Response:
{
  "success": true,
  "fileId": "uuid",
  "fileName": "example.pdf",
  "fileSize": 1024,
  "scanned": true,
  "safe": true,
  "message": "File uploaded successfully"
}
```

#### Download File
```http
GET /api/files/download/{fileId}
Authorization: Bearer <token>

Response: Binary file content
```

#### Get File Info
```http
GET /api/files/{fileId}
Authorization: Bearer <token>

Response:
{
  "fileId": "uuid",
  "fileName": "example.pdf",
  "fileSize": 1024,
  "contentType": "application/pdf",
  "uploadDate": "2024-01-01T00:00:00",
  "description": "My document",
  "encrypted": true,
  "quarantined": false
}
```

#### List User Files
```http
GET /api/files/user/{userId}?page=0&size=10
Authorization: Bearer <token>

Response:
{
  "files": [
    {
      "fileId": "uuid",
      "fileName": "example.pdf",
      "fileSize": 1024,
      "uploadDate": "2024-01-01T00:00:00",
      "quarantined": false
    }
  ],
  "totalElements": 50,
  "totalPages": 5,
  "currentPage": 0
}
```

#### Delete File
```http
DELETE /api/files/{fileId}
Authorization: Bearer <token>

Response: 200 OK
```

#### Share File
```http
POST /api/files/{fileId}/share
Authorization: Bearer <token>
Content-Type: application/json

Body:
{
  "targetUserId": "user123",
  "permission": "READ"
}

Response:
{
  "shareToken": "share-token-uuid"
}
```

#### Get Scan Result
```http
GET /api/files/{fileId}/scan-result
Authorization: Bearer <token>

Response:
{
  "safe": true,
  "confidence": 0.95,
  "threatType": null,
  "description": "File is safe",
  "scanDate": "2024-01-01T00:00:00"
}
```

### 3. User Service (Port 8083)
**Chức năng**: Quản lý người dùng, authentication, và authorization.

**Features**:
- User Registration/Login
- JWT Token Management
- Password Hashing
- User Profile Management
- Role-based Access Control

**API Endpoints**:

#### Register User
```http
POST /api/users/register
Content-Type: application/json

Body:
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "John",
  "lastName": "Doe"
}

Response:
{
  "success": true,
  "userId": "uuid",
  "message": "User registered successfully"
}
```

#### Login
```http
POST /api/users/login
Content-Type: application/json

Body:
{
  "username": "john_doe",
  "password": "securePassword123"
}

Response:
{
  "success": true,
  "token": "jwt-token",
  "refreshToken": "refresh-token",
  "expiresIn": 86400,
  "user": {
    "userId": "uuid",
    "username": "john_doe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  }
}
```

#### Get User Profile
```http
GET /api/users/profile
Authorization: Bearer <token>

Response:
{
  "userId": "uuid",
  "username": "john_doe",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "createdDate": "2024-01-01T00:00:00",
  "lastLogin": "2024-01-01T00:00:00"
}
```

#### Update Profile
```http
PUT /api/users/profile
Authorization: Bearer <token>
Content-Type: application/json

Body:
{
  "firstName": "John",
  "lastName": "Smith",
  "email": "<EMAIL>"
}

Response:
{
  "success": true,
  "message": "Profile updated successfully"
}
```

#### Change Password
```http
POST /api/users/change-password
Authorization: Bearer <token>
Content-Type: application/json

Body:
{
  "currentPassword": "oldPassword123",
  "newPassword": "newPassword123"
}

Response:
{
  "success": true,
  "message": "Password changed successfully"
}
```

#### Refresh Token
```http
POST /api/users/refresh
Content-Type: application/json

Body:
{
  "refreshToken": "refresh-token"
}

Response:
{
  "success": true,
  "token": "new-jwt-token",
  "expiresIn": 86400
}
```

### 4. Security Service (Port 8084)
**Chức năng**: Quản lý bảo mật, audit logs, và security policies.

**Features**:
- Security Audit Logging
- Access Control Management
- Security Policy Management
- Threat Detection Integration

### 5. Monitoring Service (Port 8085)
**Chức năng**: Giám sát hệ thống, metrics, và alerting.

**Features**:
- System Health Monitoring
- Performance Metrics
- Alert Management
- Dashboard Integration

### 6. Network IDS Integration (Port 5000)
**Chức năng**: Tích hợp với Network IDS Python service để phát hiện xâm nhập mạng.

**Features**:
- Real-time Network Monitoring
- Threat Detection
- Alert Generation
- Network Statistics

### 7. AI-Malware Detection Integration (Port 8086)
**Chức năng**: Tích hợp với AI-Malware Detection Python service để scan file.

**Features**:
- AI-based Malware Detection
- File Scanning
- Threat Classification
- Confidence Scoring

## Security Features

### 1. Authentication & Authorization
- JWT-based authentication
- Role-based access control
- Session management
- Token refresh mechanism

### 2. File Security
- AES-256 encryption for stored files
- File integrity verification
- Quarantine system for malicious files
- Access logging

### 3. Network Security
- Network IDS integration
- Real-time threat monitoring
- IP blocking
- Rate limiting

### 4. AI Security
- Machine learning-based malware detection
- Behavioral analysis
- Threat classification
- Confidence scoring

## Deployment

### Prerequisites
- Docker & Docker Compose
- Java 17+
- Maven 3.8+
- Redis
- PostgreSQL

### Quick Start
1. Clone the repository
2. Navigate to backend directory
3. Run build script:
   ```bash
   # Linux/Mac
   ./build.sh
   
   # Windows
   build.bat
   ```

### Manual Deployment
1. Build services:
   ```bash
   cd api-gateway && mvn clean package -DskipTests && cd ..
   cd storage-service && mvn clean package -DskipTests && cd ..
   cd user-service && mvn clean package -DskipTests && cd ..
   ```

2. Start with Docker Compose:
   ```bash
   docker-compose up --build -d
   ```

3. Check service health:
   ```bash
   curl http://localhost:8080/actuator/health
   ```

## Configuration

### Environment Variables
- `SPRING_PROFILES_ACTIVE`: Application profile (dev, test, prod)
- `REDIS_HOST`: Redis server host
- `REDIS_PORT`: Redis server port
- `POSTGRES_HOST`: PostgreSQL host
- `POSTGRES_PORT`: PostgreSQL port
- `POSTGRES_DB`: Database name
- `POSTGRES_USER`: Database username
- `POSTGRES_PASSWORD`: Database password
- `JWT_SECRET`: JWT signing secret
- `ENCRYPTION_KEY`: File encryption key

### Database Configuration
- PostgreSQL for production
- H2 in-memory for development
- Automatic schema creation
- Connection pooling

### Redis Configuration
- Used for rate limiting
- Session storage
- Cache management

## Monitoring & Observability

### Health Checks
All services expose health endpoints:
- `/actuator/health` - Service health status
- `/actuator/info` - Service information
- `/actuator/metrics` - Performance metrics

### Logging
- Structured logging with JSON format
- Centralized log aggregation
- Different log levels per environment
- Request/response logging

### Metrics
- Prometheus metrics exposition
- Grafana dashboards
- Custom business metrics
- Performance monitoring

## Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Email format is invalid"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### HTTP Status Codes
- `200 OK` - Success
- `201 Created` - Resource created
- `400 Bad Request` - Invalid input
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Access denied
- `404 Not Found` - Resource not found
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

## Testing

### Unit Tests
```bash
mvn test
```

### Integration Tests
```bash
mvn integration-test
```

### Load Testing
```bash
# Using Apache Bench
ab -n 1000 -c 10 http://localhost:8080/api/files/upload

# Using wrk
wrk -t12 -c400 -d30s http://localhost:8080/api/files/list
```

## Troubleshooting

### Common Issues

#### Service Not Starting
1. Check Docker is running
2. Verify port availability
3. Check service logs: `docker-compose logs service-name`

#### Database Connection Issues
1. Verify PostgreSQL is running
2. Check connection string
3. Verify credentials

#### Authentication Issues
1. Check JWT secret configuration
2. Verify token expiration
3. Check user credentials

#### File Upload Issues
1. Check file size limits
2. Verify storage directory permissions
3. Check AI-Malware service connectivity

### Log Analysis
```bash
# Check all service logs
docker-compose logs -f

# Check specific service
docker-compose logs -f api-gateway

# Check last 100 lines
docker-compose logs --tail=100 storage-service
```

## Performance Optimization

### Caching Strategy
- Redis for session caching
- File metadata caching
- Query result caching

### Database Optimization
- Connection pooling
- Query optimization
- Index management

### File Storage Optimization
- Chunked file upload
- Compression
- CDN integration

## Security Best Practices

### Development
- Use HTTPS in production
- Implement proper CORS
- Validate all inputs
- Use parameterized queries
- Regular security audits

### Production
- Use strong encryption keys
- Implement rate limiting
- Monitor security logs
- Regular security updates
- Backup encryption keys

## API Rate Limits

| Endpoint | Limit | Window |
|----------|-------|--------|
| `/api/files/upload` | 10 requests | 1 minute |
| `/api/files/download` | 100 requests | 1 minute |
| `/api/users/login` | 5 requests | 1 minute |
| `/api/users/register` | 3 requests | 1 minute |
| Other endpoints | 1000 requests | 1 hour |

## Support

For issues and support:
1. Check this documentation
2. Review service logs
3. Check GitHub issues
4. Contact development team

## License

This project is licensed under the MIT License.
