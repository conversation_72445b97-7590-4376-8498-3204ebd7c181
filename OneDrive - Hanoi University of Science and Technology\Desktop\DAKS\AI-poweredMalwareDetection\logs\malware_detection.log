2025-07-16 09:50:24,913 - src.models.ensemble_model - [32mINFO[0m - Initialized 4 models
2025-07-16 09:50:24,914 - src.models.ensemble_model - [32mINFO[0m - Loaded models: ['random_forest', 'svm', 'xgboost']
2025-07-16 09:50:24,914 - __main__ - [32mINFO[0m - Starting model training...
2025-07-16 09:50:24,914 - src.models.ensemble_model - [32mINFO[0m - Starting ensemble training...
2025-07-16 09:50:24,943 - src.models.ensemble_model - [32mINFO[0m - Loaded training data: 2000 samples, 17 features
2025-07-16 09:50:25,179 - src.models.ensemble_model - [32mINFO[0m - Training random_forest...
2025-07-16 09:50:25,727 - src.models.ensemble_model - [32mINFO[0m - random_forest - Accuracy: 0.8150, Precision: 0.8134, Recall: 0.8150, F1: 0.8141
2025-07-16 09:50:25,727 - src.models.ensemble_model - [32mINFO[0m - Training svm...
2025-07-16 09:50:26,903 - src.models.ensemble_model - [32mINFO[0m - svm - Accuracy: 0.7450, Precision: 0.7475, Recall: 0.7450, F1: 0.7462
2025-07-16 09:50:26,903 - src.models.ensemble_model - [32mINFO[0m - Training xgboost...
2025-07-16 09:50:28,698 - src.models.ensemble_model - [32mINFO[0m - xgboost - Accuracy: 0.8125, Precision: 0.8121, Recall: 0.8125, F1: 0.8123
2025-07-16 09:50:28,698 - src.models.ensemble_model - [32mINFO[0m - Training neural network...
2025-07-16 09:50:29,211 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\backend.py:873: The name tf.get_default_graph is deprecated. Please use tf.compat.v1.get_default_graph instead.

2025-07-16 09:50:29,582 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\optimizers\__init__.py:309: The name tf.train.Optimizer is deprecated. Please use tf.compat.v1.train.Optimizer instead.

2025-07-16 09:50:30,337 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\utils\tf_utils.py:492: The name tf.ragged.RaggedTensorValue is deprecated. Please use tf.compat.v1.ragged.RaggedTensorValue instead.

2025-07-16 09:50:30,758 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\engine\base_layer_utils.py:384: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 09:50:46,573 - src.models.ensemble_model - [32mINFO[0m - Neural Network - Test Accuracy: 0.7525
2025-07-16 09:50:46,724 - src.models.ensemble_model - [32mINFO[0m - Models saved successfully
2025-07-16 09:50:46,724 - src.models.ensemble_model - [32mINFO[0m - Ensemble training completed
2025-07-16 09:50:46,724 - __main__ - [32mINFO[0m - Model training completed
2025-07-16 09:51:52,130 - src.models.ensemble_model - [32mINFO[0m - Initialized 4 models
2025-07-16 09:51:54,040 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\backend.py:1398: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 09:51:54,327 - src.models.ensemble_model - [32mINFO[0m - Loaded models: ['random_forest', 'svm', 'xgboost', 'neural_network']
2025-07-16 09:51:54,333 - __main__ - [32mINFO[0m - Analyzing file: test_file.txt
2025-07-16 09:51:54,333 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 09:51:54,490 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: test_file.txt
2025-07-16 09:51:54,499 - src.models.ensemble_model - [31mERROR[0m - Error in ensemble prediction: The feature names should match those that were passed during fit.
Feature names unseen at fit time:
- avg_chunk_entropy
- file_type
- max_chunk_entropy
- md5
- min_chunk_entropy
- ...

2025-07-16 09:51:54,500 - __main__ - [31mERROR[0m - Error: 'NoneType' object is not subscriptable
2025-07-16 09:53:29,078 - src.models.ensemble_model - [32mINFO[0m - Initialized 4 models
2025-07-16 09:53:30,670 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\backend.py:1398: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 09:53:30,836 - src.models.ensemble_model - [32mINFO[0m - Loaded models: ['random_forest', 'svm', 'xgboost', 'neural_network']
2025-07-16 09:53:30,836 - __main__ - [32mINFO[0m - Starting model training...
2025-07-16 09:53:30,839 - src.models.ensemble_model - [32mINFO[0m - Starting ensemble training...
2025-07-16 09:53:30,853 - src.models.ensemble_model - [32mINFO[0m - Loaded training data: 2000 samples, 17 features
2025-07-16 09:53:31,331 - src.models.ensemble_model - [32mINFO[0m - Training random_forest...
2025-07-16 09:53:32,003 - src.models.ensemble_model - [32mINFO[0m - random_forest - Accuracy: 0.8150, Precision: 0.8134, Recall: 0.8150, F1: 0.8141
2025-07-16 09:53:32,003 - src.models.ensemble_model - [32mINFO[0m - Training svm...
2025-07-16 09:53:33,428 - src.models.ensemble_model - [32mINFO[0m - svm - Accuracy: 0.7450, Precision: 0.7475, Recall: 0.7450, F1: 0.7462
2025-07-16 09:53:33,428 - src.models.ensemble_model - [32mINFO[0m - Training xgboost...
2025-07-16 09:53:34,171 - src.models.ensemble_model - [32mINFO[0m - xgboost - Accuracy: 0.8125, Precision: 0.8121, Recall: 0.8125, F1: 0.8123
2025-07-16 09:53:34,174 - src.models.ensemble_model - [32mINFO[0m - Training neural network...
2025-07-16 09:53:34,362 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\optimizers\__init__.py:309: The name tf.train.Optimizer is deprecated. Please use tf.compat.v1.train.Optimizer instead.

2025-07-16 09:53:34,694 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\utils\tf_utils.py:492: The name tf.ragged.RaggedTensorValue is deprecated. Please use tf.compat.v1.ragged.RaggedTensorValue instead.

2025-07-16 09:53:35,001 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\engine\base_layer_utils.py:384: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 09:53:49,992 - src.models.ensemble_model - [32mINFO[0m - Neural Network - Test Accuracy: 0.7500
2025-07-16 09:53:50,130 - src.models.ensemble_model - [32mINFO[0m - Models saved successfully
2025-07-16 09:53:50,130 - src.models.ensemble_model - [32mINFO[0m - Ensemble training completed
2025-07-16 09:53:50,130 - __main__ - [32mINFO[0m - Model training completed
2025-07-16 09:54:17,229 - src.models.ensemble_model - [32mINFO[0m - Initialized 4 models
2025-07-16 09:54:18,911 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\backend.py:1398: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 09:54:19,106 - src.models.ensemble_model - [32mINFO[0m - Loaded models: ['random_forest', 'svm', 'xgboost', 'neural_network']
2025-07-16 09:54:19,106 - __main__ - [32mINFO[0m - Analyzing file: test_file.txt
2025-07-16 09:54:19,107 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 09:54:19,299 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: test_file.txt
2025-07-16 09:54:19,299 - src.models.ensemble_model - [31mERROR[0m - Error in ensemble prediction: The feature names should match those that were passed during fit.
Feature names must be in the same order as they were in fit.

2025-07-16 09:54:19,299 - __main__ - [31mERROR[0m - Error: 'NoneType' object is not subscriptable
2025-07-16 09:56:25,548 - src.models.ensemble_model - [32mINFO[0m - Initialized 4 models
2025-07-16 09:56:27,389 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\backend.py:1398: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 09:56:27,609 - src.models.ensemble_model - [32mINFO[0m - Loaded models: ['random_forest', 'svm', 'xgboost', 'neural_network']
2025-07-16 09:56:27,609 - __main__ - [32mINFO[0m - Starting model training...
2025-07-16 09:56:27,609 - src.models.ensemble_model - [32mINFO[0m - Starting ensemble training...
2025-07-16 09:56:27,627 - src.models.ensemble_model - [32mINFO[0m - Loaded training data: 2000 samples, 17 features
2025-07-16 09:56:27,995 - src.models.ensemble_model - [32mINFO[0m - Training random_forest...
2025-07-16 09:56:28,698 - src.models.ensemble_model - [32mINFO[0m - random_forest - Accuracy: 0.8150, Precision: 0.8134, Recall: 0.8150, F1: 0.8141
2025-07-16 09:56:28,698 - src.models.ensemble_model - [32mINFO[0m - Training svm...
2025-07-16 09:56:30,352 - src.models.ensemble_model - [32mINFO[0m - svm - Accuracy: 0.7450, Precision: 0.7475, Recall: 0.7450, F1: 0.7462
2025-07-16 09:56:30,352 - src.models.ensemble_model - [32mINFO[0m - Training xgboost...
2025-07-16 09:56:30,997 - src.models.ensemble_model - [32mINFO[0m - xgboost - Accuracy: 0.8125, Precision: 0.8121, Recall: 0.8125, F1: 0.8123
2025-07-16 09:56:31,000 - src.models.ensemble_model - [32mINFO[0m - Training neural network...
2025-07-16 09:56:31,171 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\optimizers\__init__.py:309: The name tf.train.Optimizer is deprecated. Please use tf.compat.v1.train.Optimizer instead.

2025-07-16 09:56:31,541 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\utils\tf_utils.py:492: The name tf.ragged.RaggedTensorValue is deprecated. Please use tf.compat.v1.ragged.RaggedTensorValue instead.

2025-07-16 09:56:31,874 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\engine\base_layer_utils.py:384: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 09:56:46,014 - src.models.ensemble_model - [32mINFO[0m - Neural Network - Test Accuracy: 0.7525
2025-07-16 09:56:46,161 - src.models.ensemble_model - [32mINFO[0m - Models saved successfully
2025-07-16 09:56:46,163 - src.models.ensemble_model - [32mINFO[0m - Ensemble training completed
2025-07-16 09:56:46,163 - __main__ - [32mINFO[0m - Model training completed
2025-07-16 10:00:35,729 - src.models.ensemble_model - [32mINFO[0m - Initialized 4 models
2025-07-16 10:00:37,355 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\backend.py:1398: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 10:00:37,526 - src.models.ensemble_model - [32mINFO[0m - Loaded models: ['random_forest', 'svm', 'xgboost', 'neural_network']
2025-07-16 10:00:37,526 - __main__ - [32mINFO[0m - Analyzing file: C:\Windows\notepad.exe
2025-07-16 10:00:37,531 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:00:38,290 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\notepad.exe
2025-07-16 10:01:09,309 - src.models.ensemble_model - [32mINFO[0m - Initialized 4 models
2025-07-16 10:01:11,061 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\backend.py:1398: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 10:01:11,401 - src.models.ensemble_model - [32mINFO[0m - Loaded models: ['random_forest', 'svm', 'xgboost', 'neural_network']
2025-07-16 10:01:11,401 - __main__ - [32mINFO[0m - Scanning directory: C:\Windows\System32
2025-07-16 10:01:11,423 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:11,837 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\07409496-a423-4a3e-b620-2cfb01a9318d_HyperV-ComputeNetwork.dll
2025-07-16 10:01:12,645 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:12,692 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\0ae3b998-9a38-4b72-a4c4-06849441518d_Servicing-Stack.dll
2025-07-16 10:01:13,040 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:13,080 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\3bc29097-7317-41d3-93b9-38a48f99d48a_mssrch.dll
2025-07-16 10:01:13,454 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:13,497 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\4545ffe2-0dc4-4df4-9d02-299ef204635e_hvsocket.dll
2025-07-16 10:01:13,861 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:13,920 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\5E37410B-D6F1-471D-AE27-563CEAC0D6B2
2025-07-16 10:01:14,258 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:14,288 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\69fe178f-26e7-43a9-aa7d-2b616b672dde_eventlogservice.dll
2025-07-16 10:01:14,600 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:14,635 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\6bea57fb-8dfb-4177-9ae8-42e8b3529933_RuntimeDeviceInstall.dll
2025-07-16 10:01:14,972 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:14,995 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@AdvancedKeySettingsNotification.png
2025-07-16 10:01:15,291 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:15,306 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@AppHelpToast.png
2025-07-16 10:01:15,693 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:15,724 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@AudioToastIcon.png
2025-07-16 10:01:16,257 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:16,282 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@BackgroundAccessToastIcon.png
2025-07-16 10:01:16,690 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:16,708 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@bitlockertoastimage.png
2025-07-16 10:01:17,050 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:17,079 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@edptoastimage.png
2025-07-16 10:01:17,461 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:17,481 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@EnrollmentToastIcon.png
2025-07-16 10:01:17,745 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:17,866 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@facial-recognition-windows-hello-rejuv.gif
2025-07-16 10:01:18,160 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:18,292 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@facial-recognition-windows-hello.gif
2025-07-16 10:01:18,540 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:18,553 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@language_notification_icon.png
2025-07-16 10:01:18,857 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:18,870 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@optionalfeatures.png
2025-07-16 10:01:19,140 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:19,155 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@StorageSenseToastIcon.png
2025-07-16 10:01:19,424 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:19,436 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@VpnToastIcon.png
2025-07-16 10:01:19,707 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:19,719 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@WindowsHelloFaceToastIcon.png
2025-07-16 10:01:19,989 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:20,004 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@WindowsHelloFaceToastIconRejuv.png
2025-07-16 10:01:20,319 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:20,333 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@WindowsUpdateToastIcon.contrast-black.png
2025-07-16 10:01:20,632 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:20,649 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@WindowsUpdateToastIcon.contrast-white.png
2025-07-16 10:01:20,907 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:20,915 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@WindowsUpdateToastIcon.png
2025-07-16 10:01:21,226 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:21,245 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@WirelessDisplayToast.png
2025-07-16 10:01:21,546 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:21,561 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\@WLOGO_96x96.png
2025-07-16 10:01:21,810 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:22,426 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aadauthhelper.dll
2025-07-16 10:01:22,717 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:24,236 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aadcloudap.dll
2025-07-16 10:01:24,657 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:24,981 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aadjcsp.dll
2025-07-16 10:01:25,251 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:27,383 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aadtb.dll
2025-07-16 10:01:27,649 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:28,015 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aadWamExtension.dll
2025-07-16 10:01:28,258 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:29,116 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AarSvc.dll
2025-07-16 10:01:29,347 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:30,650 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AboutSettingsHandlers.dll
2025-07-16 10:01:30,892 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:31,637 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AboveLockAppHost.dll
2025-07-16 10:01:31,908 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:32,372 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\accessibilitycpl.dll
2025-07-16 10:01:32,668 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:33,213 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\accountaccessor.dll
2025-07-16 10:01:33,484 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:33,804 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AccountHealth.dll
2025-07-16 10:01:34,255 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:35,101 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AccountsRt.dll
2025-07-16 10:01:35,371 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:35,881 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AcGenral.dll
2025-07-16 10:01:36,172 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:37,498 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AcLayers.dll
2025-07-16 10:01:37,853 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:37,921 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\acledit.dll
2025-07-16 10:01:38,206 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:39,338 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aclui.dll
2025-07-16 10:01:39,664 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:40,971 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\acmigration.dll
2025-07-16 10:01:41,307 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:41,770 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ACPBackgroundManagerPolicy.dll
2025-07-16 10:01:42,082 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:42,330 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\acppage.dll
2025-07-16 10:01:42,613 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:42,689 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\acproxy.dll
2025-07-16 10:01:42,950 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:43,129 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AcSpecfc.dll
2025-07-16 10:01:43,419 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:44,002 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ActionCenter.dll
2025-07-16 10:01:44,294 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:44,529 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ActionCenterCPL.dll
2025-07-16 10:01:44,784 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:45,280 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ActionQueue.dll
2025-07-16 10:01:45,525 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:45,822 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ActivationClient.dll
2025-07-16 10:01:46,056 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:47,582 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ActivationManager.dll
2025-07-16 10:01:47,868 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:48,469 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\activeds.dll
2025-07-16 10:01:48,792 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:49,022 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\activeds.tlb
2025-07-16 10:01:49,444 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:49,482 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ActiveHours.png
2025-07-16 10:01:49,946 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:50,254 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ActiveSyncCsp.dll
2025-07-16 10:01:50,690 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:53,511 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ActiveSyncProvider.dll
2025-07-16 10:01:53,812 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:55,659 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\actxprxy.dll
2025-07-16 10:01:56,120 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:56,429 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AcWinRT.dll
2025-07-16 10:01:56,861 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:56,943 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AcXtrnal.dll
2025-07-16 10:01:57,292 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:57,436 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AdaptiveCards.dll
2025-07-16 10:01:57,715 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:57,877 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AddressParser.dll
2025-07-16 10:01:58,163 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:58,252 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\adhapi.dll
2025-07-16 10:01:58,529 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:58,741 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\adhsvc.dll
2025-07-16 10:01:59,006 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:01:59,359 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\adprovider.dll
2025-07-16 10:01:59,772 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:00,558 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\adsldp.dll
2025-07-16 10:02:00,868 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:01,401 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\adsldpc.dll
2025-07-16 10:02:01,831 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:02,489 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\adsmsext.dll
2025-07-16 10:02:02,892 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:03,574 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\adsnt.dll
2025-07-16 10:02:03,907 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:04,386 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\adtschema.dll
2025-07-16 10:02:04,677 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:05,151 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AdvancedEmojiDS.dll
2025-07-16 10:02:05,456 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:07,054 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\advapi32.dll
2025-07-16 10:02:07,332 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:07,349 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\advapi32res.dll
2025-07-16 10:02:07,635 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:07,984 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\advpack.dll
2025-07-16 10:02:08,280 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:08,319 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aeevts.dll
2025-07-16 10:02:08,597 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:10,899 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aeinv.dll
2025-07-16 10:02:11,199 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:12,923 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aemarebackup.dll
2025-07-16 10:02:13,211 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:14,298 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aepic.dll
2025-07-16 10:02:14,592 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:17,071 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\agentactivationruntime.dll
2025-07-16 10:02:17,353 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:17,448 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\agentactivationruntimestarter.exe
2025-07-16 10:02:17,774 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:19,452 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\agentactivationruntimewindows.dll
2025-07-16 10:02:19,728 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:20,447 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AggregatorHost.exe
2025-07-16 10:02:20,733 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:25,657 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aitstatic.exe
2025-07-16 10:02:26,002 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:26,451 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\alg.exe
2025-07-16 10:02:26,750 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:26,773 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\amcompat.tlb
2025-07-16 10:02:27,082 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:27,275 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\amsi.dll
2025-07-16 10:02:27,599 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:27,649 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\amsiproxy.dll
2025-07-16 10:02:27,942 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:28,177 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\amstream.dll
2025-07-16 10:02:28,542 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:29,140 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\apds.dll
2025-07-16 10:02:29,463 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:30,161 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\APHostClient.dll
2025-07-16 10:02:30,497 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:30,536 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\APHostRes.dll
2025-07-16 10:02:30,875 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:31,590 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\APHostService.dll
2025-07-16 10:02:31,896 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:33,449 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\apisampling.dll
2025-07-16 10:02:33,846 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:34,447 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ApiSetHost.AppExecutionAlias.dll
2025-07-16 10:02:34,812 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:34,995 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\apisetschema.dll
2025-07-16 10:02:35,331 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:39,574 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\APMon.dll
2025-07-16 10:02:39,962 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:40,136 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\APMonUI.dll
2025-07-16 10:02:40,490 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:43,260 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppContracts.dll
2025-07-16 10:02:43,634 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:44,404 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppExtension.dll
2025-07-16 10:02:44,888 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:48,207 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appfootprint.dll
2025-07-16 10:02:48,890 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:50,712 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\apphelp.dll
2025-07-16 10:02:51,168 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:51,368 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Apphlpdm.dll
2025-07-16 10:02:52,294 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:53,094 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppHostRegistrationVerifier.exe
2025-07-16 10:02:53,441 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:54,006 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appidapi.dll
2025-07-16 10:02:54,348 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:54,525 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appidcertstorecheck.exe
2025-07-16 10:02:54,896 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:55,423 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appidpolicyconverter.exe
2025-07-16 10:02:55,800 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:56,135 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appidsvc.dll
2025-07-16 10:02:56,469 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:56,608 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appidtel.exe
2025-07-16 10:02:56,969 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:57,137 - src.models.ensemble_model - [32mINFO[0m - Initialized 4 models
2025-07-16 10:02:58,157 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\backend.py:1398: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 10:02:58,405 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appinfo.dll
2025-07-16 10:02:58,557 - src.models.ensemble_model - [32mINFO[0m - Loaded models: ['random_forest', 'svm', 'xgboost', 'neural_network']
2025-07-16 10:02:58,557 - __main__ - [32mINFO[0m - Analyzing file: C:\Windows\calc.exe
2025-07-16 10:02:58,557 - __main__ - [31mERROR[0m - Error: File not found: C:\Windows\calc.exe
2025-07-16 10:02:58,859 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:59,005 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appinfoext.dll
2025-07-16 10:02:59,477 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:02:59,685 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppInstallerBackgroundUpdate.exe
2025-07-16 10:03:00,438 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:00,936 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppInstallerPrompt.Desktop.dll
2025-07-16 10:03:01,585 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:02,540 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ApplicationControlCSP.dll
2025-07-16 10:03:03,096 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:06,701 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ApplicationFrame.dll
2025-07-16 10:03:07,061 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:07,374 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ApplicationFrameHost.exe
2025-07-16 10:03:07,946 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:09,097 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ApplicationTargetedFeatureDatabase.dll
2025-07-16 10:03:09,403 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:09,680 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppListBackupLauncher.dll
2025-07-16 10:03:09,946 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:10,620 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppLockerCSP.dll
2025-07-16 10:03:10,871 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:12,351 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ApplyTrustOffline.exe
2025-07-16 10:03:12,649 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:13,042 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppMon.dll
2025-07-16 10:03:13,387 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:14,552 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppointmentActivation.dll
2025-07-16 10:03:14,916 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:18,008 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppointmentApis.dll
2025-07-16 10:03:18,298 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:23,334 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appraiser.dll
2025-07-16 10:03:23,696 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:25,382 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppReadiness.dll
2025-07-16 10:03:25,726 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:25,779 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\apprepapi.dll
2025-07-16 10:03:26,066 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:27,533 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppResolver.dll
2025-07-16 10:03:27,899 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:28,547 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ApproveChildRequest.exe
2025-07-16 10:03:29,146 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:30,254 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appsruprov.dll
2025-07-16 10:03:30,643 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:32,770 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\appwiz.cpl
2025-07-16 10:03:33,167 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:35,074 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppxAllUserStore.dll
2025-07-16 10:03:35,523 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:36,567 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppXApplicabilityBlob.dll
2025-07-16 10:03:36,850 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:37,461 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppxApplicabilityEngine.dll
2025-07-16 10:03:37,861 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:43,267 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppXDeploymentClient.dll
2025-07-16 10:03:43,649 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:03:55,100 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppXDeploymentExtensions.desktop.dll
2025-07-16 10:03:55,509 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:06,820 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppXDeploymentExtensions.onecore.dll
2025-07-16 10:04:07,328 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:09,469 - src.models.ensemble_model - [32mINFO[0m - Initialized 4 models
2025-07-16 10:04:10,745 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\backend.py:1398: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 10:04:11,360 - src.models.ensemble_model - [32mINFO[0m - Loaded models: ['random_forest', 'svm', 'xgboost', 'neural_network']
2025-07-16 10:04:11,367 - __main__ - [32mINFO[0m - Analyzing file: C:\Windows\System32\cmd.exe
2025-07-16 10:04:11,369 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:14,304 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmd.exe
2025-07-16 10:04:28,640 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppXDeploymentServer.dll
2025-07-16 10:04:29,013 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:33,717 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppxPackaging.dll
2025-07-16 10:04:34,266 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:34,320 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppxProvisioning.xml
2025-07-16 10:04:34,960 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:37,808 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppxSip.dll
2025-07-16 10:04:38,586 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:38,742 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppxStreamingDataSourcePS.dll
2025-07-16 10:04:39,202 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:40,524 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AppxSysprep.dll
2025-07-16 10:04:40,994 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:41,671 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Apx01000.dll
2025-07-16 10:04:42,167 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:42,615 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ApxSvc.dll
2025-07-16 10:04:43,080 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:46,360 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\archiveint.dll
2025-07-16 10:04:46,670 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:46,839 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ARP.EXE
2025-07-16 10:04:47,130 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:47,155 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\asferror.dll
2025-07-16 10:04:47,667 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:47,825 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\aspnet_counters.dll
2025-07-16 10:04:48,173 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:48,643 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AssignedAccessRuntime.dll
2025-07-16 10:04:49,046 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:49,392 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\asycfilt.dll
2025-07-16 10:04:49,914 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:50,130 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\at.exe
2025-07-16 10:04:50,472 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:50,788 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AtBroker.exe
2025-07-16 10:04:51,074 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:51,489 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\atl.dll
2025-07-16 10:04:51,863 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:52,203 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\atl110.dll
2025-07-16 10:04:52,600 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:52,872 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\atlthunk.dll
2025-07-16 10:04:53,184 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:53,341 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\atmlib.dll
2025-07-16 10:04:53,692 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:53,765 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\attrib.exe
2025-07-16 10:04:54,139 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:56,086 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\audiodg.exe
2025-07-16 10:04:56,538 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:04:58,220 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AudioEndpointBuilder.dll
2025-07-16 10:04:58,671 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:05,234 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AudioEng.dll
2025-07-16 10:05:06,419 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:10,636 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AudioHandlers.dll
2025-07-16 10:05:11,081 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:13,177 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AUDIOKSE.dll
2025-07-16 10:05:13,815 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:14,199 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\audioresourceregistrar.dll
2025-07-16 10:05:14,659 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:22,509 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AudioSes.dll
2025-07-16 10:05:23,162 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:36,226 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\audiosrv.dll
2025-07-16 10:05:36,884 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:40,430 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AudioSrvPolicyManager.dll
2025-07-16 10:05:40,957 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:41,612 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\auditcse.dll
2025-07-16 10:05:42,093 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:42,542 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\auditpol.exe
2025-07-16 10:05:43,100 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:43,474 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\auditpolcore.dll
2025-07-16 10:05:43,821 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:44,529 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AuthBroker.dll
2025-07-16 10:05:44,991 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:45,347 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AuthBrokerUI.dll
2025-07-16 10:05:45,756 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:46,034 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AuthExt.dll
2025-07-16 10:05:46,568 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:47,448 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\authfwcfg.dll
2025-07-16 10:05:47,848 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:05:48,410 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AuthFWGP.dll
2025-07-16 10:05:49,012 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:06,623 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AuthFWSnapin.dll
2025-07-16 10:06:07,430 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:07,715 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AuthFWWizFwk.dll
2025-07-16 10:06:08,267 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:09,322 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AuthHost.exe
2025-07-16 10:06:09,988 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:10,155 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AuthHostProxy.dll
2025-07-16 10:06:10,744 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:12,797 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\authui.dll
2025-07-16 10:06:13,469 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:16,114 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\authz.dll
2025-07-16 10:06:16,926 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:21,444 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\autochk.exe
2025-07-16 10:06:21,997 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:22,396 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\autofstx.exe
2025-07-16 10:06:22,951 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:23,440 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AutomaticAppSignInPolicy.dll
2025-07-16 10:06:23,979 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:25,017 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\autopilot.dll
2025-07-16 10:06:25,503 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:25,662 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\autopilotdiag.dll
2025-07-16 10:06:26,131 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:27,111 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\autoplay.dll
2025-07-16 10:06:27,608 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:29,155 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\autotimesvc.dll
2025-07-16 10:06:29,630 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:29,932 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AverageRoom.bin
2025-07-16 10:06:30,576 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:31,157 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\avicap32.dll
2025-07-16 10:06:31,696 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:32,245 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\avifil32.dll
2025-07-16 10:06:32,671 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:32,832 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\avrt.dll
2025-07-16 10:06:33,287 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:33,871 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AxInstSv.dll
2025-07-16 10:06:34,318 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:34,565 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AxInstUI.exe
2025-07-16 10:06:35,124 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:35,237 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\azman.msc
2025-07-16 10:06:35,969 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:38,155 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\azroles.dll
2025-07-16 10:06:38,531 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:39,450 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\azroleui.dll
2025-07-16 10:06:39,829 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:39,984 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\AzSqlExt.dll
2025-07-16 10:06:40,308 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:40,485 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BackgroundMediaPolicy.dll
2025-07-16 10:06:40,781 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:40,898 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\backgroundTaskHost.exe
2025-07-16 10:06:41,212 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:41,364 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BackgroundTransferHost.exe
2025-07-16 10:06:41,729 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:41,855 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BamSettingsClient.dll
2025-07-16 10:06:42,261 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:42,676 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BarcodeProvisioningPlugin.dll
2025-07-16 10:06:43,367 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:44,069 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\basecsp.dll
2025-07-16 10:06:44,480 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:44,766 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\basesrv.dll
2025-07-16 10:06:45,217 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:45,472 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bash.exe
2025-07-16 10:06:45,847 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:46,281 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\batmeter.dll
2025-07-16 10:06:46,876 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:47,244 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\battery-report.html
2025-07-16 10:06:47,776 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:49,011 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bcastdvr.proxy.dll
2025-07-16 10:06:49,635 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:50,510 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BcastDVRBroker.dll
2025-07-16 10:06:50,996 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:52,711 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BcastDVRClient.dll
2025-07-16 10:06:53,169 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:06:54,511 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BcastDVRCommon.dll
2025-07-16 10:06:55,197 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:00,846 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bcastdvruserservice.dll
2025-07-16 10:07:01,257 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:02,745 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bcd.dll
2025-07-16 10:07:03,184 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:04,260 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bcdboot.exe
2025-07-16 10:07:04,613 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:05,866 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bcdedit.exe
2025-07-16 10:07:06,237 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:06,519 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bcdprov.dll
2025-07-16 10:07:06,926 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:07,116 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bcdsrv.dll
2025-07-16 10:07:07,471 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:12,886 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BCP47Langs.dll
2025-07-16 10:07:13,378 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:14,034 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BCP47mrm.dll
2025-07-16 10:07:14,461 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:14,600 - src.models.ensemble_model - [32mINFO[0m - Initialized 4 models
2025-07-16 10:07:15,652 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bcrypt.dll
2025-07-16 10:07:16,753 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:16,856 - tensorflow - [33mWARNING[0m - From C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\keras\src\backend.py:1398: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.

2025-07-16 10:07:18,031 - src.models.ensemble_model - [32mINFO[0m - Loaded models: ['random_forest', 'svm', 'xgboost', 'neural_network']
2025-07-16 10:07:22,273 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bcryptprimitives.dll
2025-07-16 10:07:22,784 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:23,621 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bdaplgin.ax
2025-07-16 10:07:24,375 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:25,376 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BdeHdCfgLib.dll
2025-07-16 10:07:26,142 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:26,410 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bderepair.dll
2025-07-16 10:07:26,790 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:28,005 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bdesvc.dll
2025-07-16 10:07:28,303 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:28,400 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bdeui.dll
2025-07-16 10:07:28,708 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:28,853 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BdeUISrv.exe
2025-07-16 10:07:29,198 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:29,442 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bdeunlock.exe
2025-07-16 10:07:29,916 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:32,723 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BFE.DLL
2025-07-16 10:07:33,131 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:33,264 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bi.dll
2025-07-16 10:07:33,654 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:34,059 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bidispl.dll
2025-07-16 10:07:34,467 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:35,015 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bindfltapi.dll
2025-07-16 10:07:35,370 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:36,412 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BingASDS.dll
2025-07-16 10:07:36,771 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:37,136 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BingFilterDS.dll
2025-07-16 10:07:37,509 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:52,654 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BingMaps.dll
2025-07-16 10:07:52,996 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:54,527 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BingOnlineServices.dll
2025-07-16 10:07:54,850 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:55,638 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BioCredProv.dll
2025-07-16 10:07:56,025 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:57,101 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BioIso.exe
2025-07-16 10:07:57,391 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:07:59,108 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bisrv.dll
2025-07-16 10:07:59,436 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:00,125 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BitLockerCsp.dll
2025-07-16 10:08:00,455 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:00,759 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BitLockerDeviceEncryption.exe
2025-07-16 10:08:01,075 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:01,246 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BitLockerWizardElev.exe
2025-07-16 10:08:01,539 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:01,888 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bitsadmin.exe
2025-07-16 10:08:02,250 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:02,432 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bitsigd.dll
2025-07-16 10:08:02,884 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:02,981 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bitsperf.dll
2025-07-16 10:08:03,286 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:03,402 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BitsProxy.dll
2025-07-16 10:08:03,730 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:04,700 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\biwinrt.dll
2025-07-16 10:08:05,199 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:05,325 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BlbEvents.dll
2025-07-16 10:08:05,644 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:05,671 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\blbres.dll
2025-07-16 10:08:05,981 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:06,173 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\blb_ps.dll
2025-07-16 10:08:06,501 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:07,350 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BluetoothApis.dll
2025-07-16 10:08:07,676 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:07,911 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BluetoothDesktopHandlers.dll
2025-07-16 10:08:08,218 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:08,624 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BluetoothOppPushClient.dll
2025-07-16 10:08:09,071 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:09,118 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BluetoothPairingSystemToastIcon.contrast-black.png
2025-07-16 10:08:09,537 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:09,558 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BluetoothPairingSystemToastIcon.contrast-high.png
2025-07-16 10:08:09,926 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:09,956 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BluetoothPairingSystemToastIcon.contrast-white.png
2025-07-16 10:08:10,388 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:10,423 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BluetoothPairingSystemToastIcon.png
2025-07-16 10:08:10,764 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:10,781 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BluetoothSystemToastIcon.contrast-white.png
2025-07-16 10:08:11,074 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:11,088 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BluetoothSystemToastIcon.png
2025-07-16 10:08:11,382 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:11,585 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bnmanager.dll
2025-07-16 10:08:11,922 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:13,275 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\boot.sdi
2025-07-16 10:08:13,592 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:14,165 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BootCriticalUpdatePlugin.dll
2025-07-16 10:08:14,567 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:14,721 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bootim.exe
2025-07-16 10:08:15,031 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:15,586 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BootMenuUX.dll
2025-07-16 10:08:15,902 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:16,061 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bootsect.exe
2025-07-16 10:08:16,375 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:16,410 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bootstr.dll
2025-07-16 10:08:16,720 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:17,182 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bootsvc.dll
2025-07-16 10:08:17,508 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:21,203 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bootux.dll
2025-07-16 10:08:21,463 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:21,534 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BOOTVID.DLL
2025-07-16 10:08:21,861 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:21,896 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bopomofo.uce
2025-07-16 10:08:22,193 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:22,255 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bridgeres.dll
2025-07-16 10:08:22,599 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:22,760 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bridgeunattend.exe
2025-07-16 10:08:23,119 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:23,133 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BrokerFileDialog.dat
2025-07-16 10:08:23,583 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:24,430 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BrokerFileDialog.dll
2025-07-16 10:08:24,839 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:25,873 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BrokerLib.dll
2025-07-16 10:08:26,209 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:26,417 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\browcli.dll
2025-07-16 10:08:26,766 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:27,791 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\browserbroker.dll
2025-07-16 10:08:28,270 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:28,687 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\browserexport.exe
2025-07-16 10:08:29,036 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:29,385 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\browser_broker.exe
2025-07-16 10:08:29,699 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:29,779 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\browseui.dll
2025-07-16 10:08:30,121 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:32,689 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BTAGService.dll
2025-07-16 10:08:33,039 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:34,768 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BthAvctpSvc.dll
2025-07-16 10:08:35,210 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:36,250 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BthAvrcp.dll
2025-07-16 10:08:36,555 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:36,674 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BthAvrcpAppSvc.dll
2025-07-16 10:08:36,998 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:37,308 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bthci.dll
2025-07-16 10:08:37,680 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:37,792 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BthMtpContextHandler.dll
2025-07-16 10:08:38,164 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:38,403 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bthpanapi.dll
2025-07-16 10:08:38,786 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:39,734 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BthpanContextHandler.dll
2025-07-16 10:08:40,169 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:40,886 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bthprops.cpl
2025-07-16 10:08:41,376 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:43,015 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BthRadioMedia.dll
2025-07-16 10:08:43,312 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:44,164 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bthserv.dll
2025-07-16 10:08:44,456 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:44,848 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BthTelemetry.dll
2025-07-16 10:08:45,174 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:45,285 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\bthudtask.exe
2025-07-16 10:08:45,566 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:45,780 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\btpanui.dll
2025-07-16 10:08:46,073 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:46,846 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Bubbles.scr
2025-07-16 10:08:47,176 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:47,354 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\BWContextHandler.dll
2025-07-16 10:08:47,664 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:48,038 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ByteCodeGenerator.exe
2025-07-16 10:08:48,327 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:48,350 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\c4d66f00-b6f0-4439-ac9b-c5ea13fe54d7_HyperV-ComputeCore.dll
2025-07-16 10:08:48,677 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:49,061 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cabapi.dll
2025-07-16 10:08:49,440 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:49,789 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cabinet.dll
2025-07-16 10:08:50,085 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:50,447 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cabview.dll
2025-07-16 10:08:50,745 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:50,855 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cacls.exe
2025-07-16 10:08:51,154 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:51,214 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\calc.exe
2025-07-16 10:08:51,495 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:51,670 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CallButtons.dll
2025-07-16 10:08:51,967 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:52,050 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CallButtons.ProxyStub.dll
2025-07-16 10:08:52,386 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:52,824 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CallHistoryClient.dll
2025-07-16 10:08:53,103 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:53,360 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CameraCaptureUI.dll
2025-07-16 10:08:53,626 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:53,709 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CameraSettingsUIHost.exe
2025-07-16 10:08:54,004 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:54,091 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\camext.dll
2025-07-16 10:08:54,367 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:54,628 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CapabilityAccessHandlers.dll
2025-07-16 10:08:54,941 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:57,613 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CapabilityAccessManager.Desktop.Storage.dll
2025-07-16 10:08:57,903 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:08:59,892 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CapabilityAccessManager.dll
2025-07-16 10:09:00,208 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:01,017 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CapabilityAccessManagerClient.dll
2025-07-16 10:09:01,332 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:02,277 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\capauthz.dll
2025-07-16 10:09:02,648 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:03,011 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\capiprovider.dll
2025-07-16 10:09:03,388 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:03,490 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\capisp.dll
2025-07-16 10:09:04,011 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:04,472 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CaptureService.dll
2025-07-16 10:09:04,804 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:04,980 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CastingShellExt.dll
2025-07-16 10:09:05,351 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:05,659 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CastLaunch.dll
2025-07-16 10:09:05,963 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:06,127 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CastSrv.exe
2025-07-16 10:09:06,436 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:07,343 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\catsrv.dll
2025-07-16 10:09:07,622 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:07,737 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\catsrvps.dll
2025-07-16 10:09:08,031 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:09,088 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\catsrvut.dll
2025-07-16 10:09:09,364 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:11,021 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CBDHSvc.dll
2025-07-16 10:09:11,357 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:11,762 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cca.dll
2025-07-16 10:09:12,262 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:12,856 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cdd.dll
2025-07-16 10:09:13,199 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:15,098 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cdosys.dll
2025-07-16 10:09:15,358 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:25,232 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cdp.dll
2025-07-16 10:09:25,841 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:31,558 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cdprt.dll
2025-07-16 10:09:31,865 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:33,396 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cdpsvc.dll
2025-07-16 10:09:33,687 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:34,851 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cdpusersvc.dll
2025-07-16 10:09:35,181 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:35,339 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cellulardatacapabilityhandler.dll
2025-07-16 10:09:35,632 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:36,175 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cemapi.dll
2025-07-16 10:09:36,485 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:36,546 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cero.rs
2025-07-16 10:09:36,863 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:38,598 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\certca.dll
2025-07-16 10:09:38,924 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:39,860 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\certcli.dll
2025-07-16 10:09:40,138 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:40,261 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\certCredProvider.dll
2025-07-16 10:09:40,554 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:40,706 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\certenc.dll
2025-07-16 10:09:40,990 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:45,909 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CertEnroll.dll
2025-07-16 10:09:46,201 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:46,396 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CertEnrollCtrl.exe
2025-07-16 10:09:46,690 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:47,435 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CertEnrollUI.dll
2025-07-16 10:09:48,033 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:48,091 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\certlm.msc
2025-07-16 10:09:48,572 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:51,598 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\certmgr.dll
2025-07-16 10:09:51,900 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:51,965 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\certmgr.msc
2025-07-16 10:09:52,277 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:52,451 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CertPKICmdlet.dll
2025-07-16 10:09:52,760 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:53,078 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CertPolEng.dll
2025-07-16 10:09:53,367 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:53,684 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\certprop.dll
2025-07-16 10:09:53,977 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:54,896 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\certreq.exe
2025-07-16 10:09:55,194 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:57,917 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\certutil.exe
2025-07-16 10:09:58,218 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:58,907 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cewmdm.dll
2025-07-16 10:09:59,209 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:09:59,391 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cfgbkend.dll
2025-07-16 10:09:59,653 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:00,304 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cfgmgr32.dll
2025-07-16 10:10:00,611 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:01,089 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CfgSPCellular.dll
2025-07-16 10:10:01,661 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:02,262 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CfgSPPolicy.dll
2025-07-16 10:10:02,574 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:02,979 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cflapi.dll
2025-07-16 10:10:03,311 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:03,407 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cfmifs.dll
2025-07-16 10:10:03,685 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:03,735 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cfmifsproxy.dll
2025-07-16 10:10:04,041 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:15,897 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Chakra.dll
2025-07-16 10:10:16,184 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:16,567 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Chakradiag.dll
2025-07-16 10:10:16,837 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:17,328 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Chakrathunk.dll
2025-07-16 10:10:17,624 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:17,765 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\changepk.exe
2025-07-16 10:10:18,070 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:18,805 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\charmap.exe
2025-07-16 10:10:19,092 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:19,353 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\chartv.dll
2025-07-16 10:10:19,704 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:22,706 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ChatApis.dll
2025-07-16 10:10:23,248 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:23,436 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\chcp.com
2025-07-16 10:10:24,015 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:24,344 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CheckNetIsolation.exe
2025-07-16 10:10:24,865 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:25,021 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\chkdsk.exe
2025-07-16 10:10:25,414 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:25,539 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\chkntfs.exe
2025-07-16 10:10:25,947 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:26,143 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\choice.exe
2025-07-16 10:10:26,580 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:28,133 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ChsStrokeDS.dll
2025-07-16 10:10:28,575 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:28,837 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\chs_singlechar_pinyin.dat
2025-07-16 10:10:29,224 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:31,230 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ChtBopomofoDS.dll
2025-07-16 10:10:31,540 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:32,497 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ChtCangjieDS.dll
2025-07-16 10:10:32,813 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:33,780 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ChtHkStrokeDS.dll
2025-07-16 10:10:34,079 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:34,886 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ChtQuickDS.dll
2025-07-16 10:10:35,175 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:36,131 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ChxAPDS.dll
2025-07-16 10:10:36,454 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:37,336 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ChxDecoder.dll
2025-07-16 10:10:37,667 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:38,743 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ChxHAPDS.dll
2025-07-16 10:10:39,073 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:39,774 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\chxinputrouter.dll
2025-07-16 10:10:40,056 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:40,255 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\chxranker.dll
2025-07-16 10:10:40,544 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:40,599 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CHxReadingStringIME.dll
2025-07-16 10:10:40,893 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:43,479 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ci.dll
2025-07-16 10:10:43,799 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:44,137 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cic.dll
2025-07-16 10:10:44,447 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:44,537 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CIDiag.exe
2025-07-16 10:10:44,819 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:45,724 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cimfs.dll
2025-07-16 10:10:46,170 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:46,401 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cipher.exe
2025-07-16 10:10:46,827 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:46,962 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CIRCoInst.dll
2025-07-16 10:10:47,489 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:10:54,389 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CiTool.exe
2025-07-16 10:11:01,179 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:05,889 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\clbcatq.dll
2025-07-16 10:11:06,430 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:06,945 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cldapi.dll
2025-07-16 10:11:07,467 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:08,128 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cleanmgr.exe
2025-07-16 10:11:08,588 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:08,996 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CleanPCCSP.dll
2025-07-16 10:11:09,365 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:09,754 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\clfsw32.dll
2025-07-16 10:11:10,653 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:11,070 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cliconfg.dll
2025-07-16 10:11:11,508 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:11,600 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cliconfg.exe
2025-07-16 10:11:11,903 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:11,984 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cliconfg.rll
2025-07-16 10:11:12,340 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:12,441 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\clip.exe
2025-07-16 10:11:12,780 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:13,222 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ClipboardServer.dll
2025-07-16 10:11:13,574 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:13,855 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Clipc.dll
2025-07-16 10:11:14,152 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:14,338 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ClipRenew.exe
2025-07-16 10:11:14,622 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:16,205 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ClipSVC.dll
2025-07-16 10:11:16,519 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:18,046 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ClipUp.exe
2025-07-16 10:11:18,349 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:18,573 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ClipUpgrade.exe
2025-07-16 10:11:18,899 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:20,503 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\clipwinrt.dll
2025-07-16 10:11:20,834 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:21,967 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cloudAP.dll
2025-07-16 10:11:22,268 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:22,677 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudDesktopCSP.dll
2025-07-16 10:11:23,016 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:23,261 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudDomainJoinAUG.dll
2025-07-16 10:11:23,604 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:24,347 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudDomainJoinDataModelServer.dll
2025-07-16 10:11:24,629 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:25,844 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudExperienceHost.dll
2025-07-16 10:11:26,167 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:27,020 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudExperienceHostBroker.dll
2025-07-16 10:11:27,327 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:27,461 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudExperienceHostBroker.exe
2025-07-16 10:11:27,765 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:30,096 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudExperienceHostCommon.dll
2025-07-16 10:11:30,420 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:30,800 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudExperienceHostRedirection.dll
2025-07-16 10:11:31,077 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:31,745 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudExperienceHostUser.dll
2025-07-16 10:11:32,067 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:32,620 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudIdWxhExtension.dll
2025-07-16 10:11:32,905 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:33,118 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudNotifications.exe
2025-07-16 10:11:33,415 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:41,966 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudRecoveryDownloadTool.dll
2025-07-16 10:11:42,268 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:45,304 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CloudRestoreLauncher.dll
2025-07-16 10:11:45,624 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:45,755 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\clrhost.dll
2025-07-16 10:11:46,098 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:48,401 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\clusapi.dll
2025-07-16 10:11:48,819 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:48,943 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmcfg32.dll
2025-07-16 10:11:49,367 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:51,206 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmd.exe
2025-07-16 10:11:52,143 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:52,567 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmdext.dll
2025-07-16 10:11:53,092 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:54,671 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmdial32.dll
2025-07-16 10:11:55,070 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:55,210 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmdkey.exe
2025-07-16 10:11:55,618 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:55,847 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmdl32.exe
2025-07-16 10:11:56,240 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:56,417 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmgrcspps.dll
2025-07-16 10:11:56,842 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:57,653 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmifw.dll
2025-07-16 10:11:58,106 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:58,348 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmintegrator.dll
2025-07-16 10:11:58,773 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:58,975 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmlua.dll
2025-07-16 10:11:59,484 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:11:59,713 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmmon32.exe
2025-07-16 10:12:00,156 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:00,296 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmpbk32.dll
2025-07-16 10:12:00,630 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:00,824 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmstp.exe
2025-07-16 10:12:01,134 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:01,206 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmstplua.dll
2025-07-16 10:12:01,491 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:01,638 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cmutil.dll
2025-07-16 10:12:01,992 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:02,120 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cngcredui.dll
2025-07-16 10:12:02,396 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:02,594 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cngprovider.dll
2025-07-16 10:12:02,885 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:03,016 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cnvfat.dll
2025-07-16 10:12:03,338 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:03,385 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cob-au.rs
2025-07-16 10:12:03,723 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:04,117 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CodeIntegrityAggregator.dll
2025-07-16 10:12:04,434 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:04,509 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cofire.exe
2025-07-16 10:12:04,844 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:04,994 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cofiredm.dll
2025-07-16 10:12:05,293 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:05,529 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\colbact.dll
2025-07-16 10:12:05,812 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:06,340 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\COLORCNV.DLL
2025-07-16 10:12:06,638 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:06,746 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\colorcpl.exe
2025-07-16 10:12:07,087 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:07,407 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\colorui.dll
2025-07-16 10:12:07,708 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:16,271 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\combase.dll
2025-07-16 10:12:16,595 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:16,654 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\comcat.dll
2025-07-16 10:12:16,957 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:18,052 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\comctl32.dll
2025-07-16 10:12:18,327 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:20,570 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\comdlg32.dll
2025-07-16 10:12:20,872 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:20,935 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\comexp.msc
2025-07-16 10:12:21,230 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:22,255 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\coml2.dll
2025-07-16 10:12:22,579 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:22,663 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\comp.exe
2025-07-16 10:12:22,987 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:23,162 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\compact.exe
2025-07-16 10:12:23,478 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:23,650 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CompatAggregator.dll
2025-07-16 10:12:23,988 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:24,391 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CompatTelRunner.exe
2025-07-16 10:12:24,679 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:24,752 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\compmgmt.msc
2025-07-16 10:12:25,065 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:25,230 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CompMgmtLauncher.exe
2025-07-16 10:12:25,541 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:26,264 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ComposableShellProxyStub.dll
2025-07-16 10:12:26,562 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:27,285 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ComposerFramework.dll
2025-07-16 10:12:27,596 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:27,760 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CompPkgSrv.exe
2025-07-16 10:12:28,058 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:28,380 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CompPkgSup.dll
2025-07-16 10:12:28,978 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:29,194 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\compstui.dll
2025-07-16 10:12:29,525 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:31,880 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\computecore.dll
2025-07-16 10:12:32,207 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:32,239 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\computelibeventlog.dll
2025-07-16 10:12:32,538 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:32,742 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\computenetwork.dll
2025-07-16 10:12:33,048 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:33,152 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ComputerDefaults.exe
2025-07-16 10:12:33,520 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:33,539 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ComputerToastIcon.contrast-white.png
2025-07-16 10:12:33,820 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:33,833 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ComputerToastIcon.png
2025-07-16 10:12:34,155 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:35,051 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\computestorage.dll
2025-07-16 10:12:35,374 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:35,602 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\comrepl.dll
2025-07-16 10:12:35,945 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:35,966 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\comres.dll
2025-07-16 10:12:36,264 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:36,626 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\comsnap.dll
2025-07-16 10:12:36,968 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:40,592 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\comsvcs.dll
2025-07-16 10:12:40,974 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:45,906 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\comuid.dll
2025-07-16 10:12:46,319 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:48,147 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\concrt140.dll
2025-07-16 10:12:48,601 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:52,368 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\concrt140d.dll
2025-07-16 10:12:52,850 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:54,259 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\configmanager2.dll
2025-07-16 10:12:54,597 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:54,842 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ConfigureExpandedStorage.dll
2025-07-16 10:12:55,120 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:57,180 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\conhost.exe
2025-07-16 10:12:57,494 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:58,142 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\connect.dll
2025-07-16 10:12:58,447 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:58,563 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ConnectedAccountState.dll
2025-07-16 10:12:58,884 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:59,100 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ConnectionAttributionApi.dll
2025-07-16 10:12:59,379 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:12:59,765 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\consent.exe
2025-07-16 10:13:00,137 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:01,223 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ConsentExperienceCommon.dll
2025-07-16 10:13:01,529 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:01,813 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ConsentUX.dll
2025-07-16 10:13:02,103 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:02,643 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ConsentUxClient.dll
2025-07-16 10:13:02,966 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:03,516 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\console.dll
2025-07-16 10:13:03,816 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:04,485 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ConsoleLogon.dll
2025-07-16 10:13:04,823 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:07,767 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ConstraintIndex.Search.dll
2025-07-16 10:13:08,062 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:08,264 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ContactActivation.dll
2025-07-16 10:13:08,576 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:10,789 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ContactApis.dll
2025-07-16 10:13:11,092 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:11,549 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ContactHarvesterDS.dll
2025-07-16 10:13:11,874 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:12,639 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\container.dll
2025-07-16 10:13:12,979 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:13,455 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\containerdevicemanagement.dll
2025-07-16 10:13:13,774 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:16,744 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ContentDeliveryManager.Utilities.dll
2025-07-16 10:13:17,091 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:17,202 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\control.exe
2025-07-16 10:13:17,533 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:25,041 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ControlCenter.dll
2025-07-16 10:13:25,369 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:25,473 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\convert.exe
2025-07-16 10:13:25,767 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:26,148 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\convertvhd.exe
2025-07-16 10:13:26,435 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:26,498 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\coreaudiopolicymanagerext.dll
2025-07-16 10:13:26,803 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:27,212 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\coredpus.dll
2025-07-16 10:13:27,572 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:27,727 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\coredpussvr.exe
2025-07-16 10:13:28,062 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:28,775 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\coreglobconfig.dll
2025-07-16 10:13:29,066 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:29,460 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CoreMas.dll
2025-07-16 10:13:30,000 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:33,089 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CoreMessaging.dll
2025-07-16 10:13:33,397 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:33,426 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CoreMmRes.dll
2025-07-16 10:13:33,736 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:34,210 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CorePrivacySettingsStore.dll
2025-07-16 10:13:34,560 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:38,657 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CoreShell.dll
2025-07-16 10:13:39,000 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:40,249 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CoreShellAPI.dll
2025-07-16 10:13:40,604 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:41,109 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CoreShellExtFramework.dll
2025-07-16 10:13:41,419 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:52,235 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CoreUIComponents.dll
2025-07-16 10:13:52,710 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:53,469 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\correngine.dll
2025-07-16 10:13:53,898 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:54,450 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CourtesyEngine.dll
2025-07-16 10:13:54,748 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:54,753 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cpa_64.vp
2025-07-16 10:13:55,094 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:56,132 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CPFilters.dll
2025-07-16 10:13:56,466 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:56,881 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CredDialogBroker.dll
2025-07-16 10:13:57,180 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:58,163 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CredentialEnrollmentManager.exe
2025-07-16 10:13:58,474 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:58,690 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CredentialEnrollmentManagerForUser.dll
2025-07-16 10:13:58,993 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:13:59,399 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CredentialUIBroker.exe
2025-07-16 10:13:59,707 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:00,226 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CredProv2faHelper.dll
2025-07-16 10:14:00,506 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:00,703 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CredProvCommonCore.dll
2025-07-16 10:14:00,995 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:02,494 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CredProvDataModel.dll
2025-07-16 10:14:02,820 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:03,215 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CredProvHelper.dll
2025-07-16 10:14:03,488 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:04,531 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\credprovhost.dll
2025-07-16 10:14:04,835 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:05,775 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\credprovs.dll
2025-07-16 10:14:06,095 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:06,603 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\credprovslegacy.dll
2025-07-16 10:14:06,889 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:07,013 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\credssp.dll
2025-07-16 10:14:07,375 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:07,605 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\credui.dll
2025-07-16 10:14:07,930 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:08,053 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\credwiz.exe
2025-07-16 10:14:08,416 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:08,438 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CriticallyLowBatterySystemToastIcon.contrast-white.png
2025-07-16 10:14:08,755 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:08,770 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CriticallyLowBatterySystemToastIcon.png
2025-07-16 10:14:09,100 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:12,353 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\crypt32.dll
2025-07-16 10:14:12,645 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:12,747 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptbase.dll
2025-07-16 10:14:13,038 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:13,481 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptcatsvc.dll
2025-07-16 10:14:13,793 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:13,888 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptdlg.dll
2025-07-16 10:14:14,176 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:14,540 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptdll.dll
2025-07-16 10:14:14,841 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:14,999 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptext.dll
2025-07-16 10:14:15,302 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:15,810 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptnet.dll
2025-07-16 10:14:16,110 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:17,084 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptngc.dll
2025-07-16 10:14:17,624 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:21,527 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptoss.dll
2025-07-16 10:14:21,840 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:22,578 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CryptoWinRT.dll
2025-07-16 10:14:22,896 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:23,083 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptsp.dll
2025-07-16 10:14:23,413 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:23,795 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptsvc.dll
2025-07-16 10:14:24,071 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:24,278 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\crypttpmeksvc.dll
2025-07-16 10:14:24,581 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:25,443 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptui.dll
2025-07-16 10:14:25,751 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:25,965 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptuiwizard.dll
2025-07-16 10:14:26,279 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:26,558 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cryptxml.dll
2025-07-16 10:14:26,855 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:26,997 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cscapi.dll
2025-07-16 10:14:27,303 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:27,392 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cscdll.dll
2025-07-16 10:14:27,726 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:28,261 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cscript.exe
2025-07-16 10:14:28,561 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:28,722 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CspCellularSettings.dll
2025-07-16 10:14:29,025 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:29,288 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\csplte.dll
2025-07-16 10:14:29,683 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:30,077 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CspProxy.dll
2025-07-16 10:14:30,675 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:30,730 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\csrr.rs
2025-07-16 10:14:31,013 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:31,181 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\csrsrv.dll
2025-07-16 10:14:31,492 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:31,538 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\csrss.exe
2025-07-16 10:14:31,819 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:31,892 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CSystemEventsBrokerClient.dll
2025-07-16 10:14:32,186 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:32,222 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ctac.json
2025-07-16 10:14:32,537 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:32,653 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ctfmon.exe
2025-07-16 10:14:32,986 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:33,199 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cttune.exe
2025-07-16 10:14:33,529 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:33,636 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cttunesvr.exe
2025-07-16 10:14:33,921 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:35,069 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\curl.exe
2025-07-16 10:14:35,365 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:35,685 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CustomInstallExec.exe
2025-07-16 10:14:35,995 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:36,133 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cxbtd64.sys
2025-07-16 10:14:36,436 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:36,947 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cxcredprov.dll
2025-07-16 10:14:37,289 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:37,871 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\CXHProvisioningServer.dll
2025-07-16 10:14:38,240 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:38,382 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\cxsken64.sys
2025-07-16 10:14:38,681 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:38,715 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_037.NLS
2025-07-16 10:14:39,034 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:39,073 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10000.NLS
2025-07-16 10:14:39,383 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:39,477 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10001.NLS
2025-07-16 10:14:39,763 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:39,869 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10002.NLS
2025-07-16 10:14:40,175 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:40,275 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10003.NLS
2025-07-16 10:14:40,581 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:40,628 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10004.NLS
2025-07-16 10:14:40,967 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:41,022 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10005.NLS
2025-07-16 10:14:41,364 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:41,406 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10006.NLS
2025-07-16 10:14:41,709 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:41,752 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10007.NLS
2025-07-16 10:14:42,095 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:42,179 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10008.NLS
2025-07-16 10:14:42,525 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:42,570 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10010.NLS
2025-07-16 10:14:42,869 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:42,910 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10017.NLS
2025-07-16 10:14:43,223 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:43,259 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10021.NLS
2025-07-16 10:14:43,569 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:43,609 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10029.NLS
2025-07-16 10:14:43,902 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:43,934 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10079.NLS
2025-07-16 10:14:44,346 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:44,432 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10081.NLS
2025-07-16 10:14:44,926 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:45,023 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_10082.NLS
2025-07-16 10:14:45,371 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:45,403 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1026.NLS
2025-07-16 10:14:45,725 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:45,802 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1047.NLS
2025-07-16 10:14:46,114 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:46,158 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1140.NLS
2025-07-16 10:14:46,458 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:46,492 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1141.NLS
2025-07-16 10:14:47,070 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:47,109 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1142.NLS
2025-07-16 10:14:47,395 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:47,427 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1143.NLS
2025-07-16 10:14:47,752 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:47,787 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1144.NLS
2025-07-16 10:14:48,080 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:48,127 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1145.NLS
2025-07-16 10:14:48,463 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:48,516 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1146.NLS
2025-07-16 10:14:48,800 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:48,839 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1147.NLS
2025-07-16 10:14:49,144 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:49,191 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1148.NLS
2025-07-16 10:14:49,536 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:49,598 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1149.NLS
2025-07-16 10:14:49,994 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:50,054 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1250.NLS
2025-07-16 10:14:50,453 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:50,510 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1251.NLS
2025-07-16 10:14:50,896 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:50,940 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1252.NLS
2025-07-16 10:14:51,256 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:51,291 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1253.NLS
2025-07-16 10:14:51,584 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:51,637 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1254.NLS
2025-07-16 10:14:51,955 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:52,008 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1255.NLS
2025-07-16 10:14:52,344 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:52,379 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1256.NLS
2025-07-16 10:14:52,667 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:52,715 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1257.NLS
2025-07-16 10:14:53,034 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:53,087 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1258.NLS
2025-07-16 10:14:53,357 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:53,452 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_1361.NLS
2025-07-16 10:14:53,761 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:53,851 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20000.NLS
2025-07-16 10:14:54,163 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:54,278 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20001.NLS
2025-07-16 10:14:54,580 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:54,667 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20002.NLS
2025-07-16 10:14:54,988 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:55,148 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20003.NLS
2025-07-16 10:14:55,459 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:55,548 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20004.NLS
2025-07-16 10:14:55,843 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:55,945 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20005.NLS
2025-07-16 10:14:56,278 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:56,385 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20105.NLS
2025-07-16 10:14:56,912 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:56,951 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20106.NLS
2025-07-16 10:14:57,280 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:57,333 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20107.NLS
2025-07-16 10:14:57,623 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:57,658 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20108.NLS
2025-07-16 10:14:57,943 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:57,984 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20127.NLS
2025-07-16 10:14:58,267 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:58,345 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20261.NLS
2025-07-16 10:14:58,679 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:58,729 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20269.NLS
2025-07-16 10:14:59,010 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:59,051 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20273.NLS
2025-07-16 10:14:59,344 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:59,379 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20277.NLS
2025-07-16 10:14:59,683 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:14:59,722 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20278.NLS
2025-07-16 10:15:00,045 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:00,093 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20280.NLS
2025-07-16 10:15:00,403 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:00,446 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20284.NLS
2025-07-16 10:15:00,788 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:00,826 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20285.NLS
2025-07-16 10:15:01,105 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:01,148 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20290.NLS
2025-07-16 10:15:01,490 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:01,531 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20297.NLS
2025-07-16 10:15:01,882 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:01,936 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20420.NLS
2025-07-16 10:15:02,352 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:02,401 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20423.NLS
2025-07-16 10:15:02,740 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:02,782 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20424.NLS
2025-07-16 10:15:03,107 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:03,153 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20833.NLS
2025-07-16 10:15:03,500 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:03,554 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20838.NLS
2025-07-16 10:15:03,870 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:03,911 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20866.NLS
2025-07-16 10:15:04,214 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:04,254 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20871.NLS
2025-07-16 10:15:04,610 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:04,651 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20880.NLS
2025-07-16 10:15:04,946 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:04,988 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20905.NLS
2025-07-16 10:15:05,297 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:05,332 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20924.NLS
2025-07-16 10:15:05,629 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:05,714 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20932.NLS
2025-07-16 10:15:06,013 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:06,096 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20936.NLS
2025-07-16 10:15:06,411 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:06,521 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_20949.NLS
2025-07-16 10:15:06,806 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:06,850 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_21025.NLS
2025-07-16 10:15:07,160 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:07,192 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_21027.NLS
2025-07-16 10:15:07,485 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:07,530 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_21866.NLS
2025-07-16 10:15:07,816 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:07,855 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_28591.NLS
2025-07-16 10:15:08,161 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:08,200 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_28592.NLS
2025-07-16 10:15:08,507 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:08,549 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_28593.NLS
2025-07-16 10:15:08,892 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:08,937 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_28594.NLS
2025-07-16 10:15:09,226 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:09,274 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_28595.NLS
2025-07-16 10:15:09,610 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:09,717 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_28596.NLS
2025-07-16 10:15:10,071 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:10,145 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_28597.NLS
2025-07-16 10:15:10,484 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:10,531 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_28598.NLS
2025-07-16 10:15:10,865 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:10,911 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_28599.NLS
2025-07-16 10:15:11,249 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:11,302 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\c_28603.nls
2025-07-16 10:15:11,678 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:11,719 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_28605.NLS
2025-07-16 10:15:12,087 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:12,132 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_437.NLS
2025-07-16 10:15:12,419 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:12,451 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_500.NLS
2025-07-16 10:15:12,764 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:13,481 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\c_64.cpa
2025-07-16 10:15:13,809 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:13,864 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_708.NLS
2025-07-16 10:15:14,154 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:14,211 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_720.NLS
2025-07-16 10:15:14,516 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:14,559 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_737.NLS
2025-07-16 10:15:14,873 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:14,942 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_775.NLS
2025-07-16 10:15:15,249 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:15,294 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_850.NLS
2025-07-16 10:15:15,597 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:15,656 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_852.NLS
2025-07-16 10:15:15,963 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:16,008 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_855.NLS
2025-07-16 10:15:16,306 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:16,360 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_857.NLS
2025-07-16 10:15:16,685 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:16,727 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_858.NLS
2025-07-16 10:15:17,055 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:17,102 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_860.NLS
2025-07-16 10:15:17,405 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:17,453 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_861.NLS
2025-07-16 10:15:17,789 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:17,857 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_862.NLS
2025-07-16 10:15:18,148 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:18,191 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_863.NLS
2025-07-16 10:15:18,485 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:18,540 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_864.NLS
2025-07-16 10:15:18,856 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:18,897 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_865.NLS
2025-07-16 10:15:19,207 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:19,249 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_866.NLS
2025-07-16 10:15:19,727 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:19,827 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_869.NLS
2025-07-16 10:15:20,161 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:20,201 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_870.NLS
2025-07-16 10:15:20,509 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:20,552 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_874.NLS
2025-07-16 10:15:20,852 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:20,887 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_875.NLS
2025-07-16 10:15:21,196 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:21,303 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_932.NLS
2025-07-16 10:15:21,639 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:21,751 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_936.NLS
2025-07-16 10:15:22,078 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:22,223 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_949.NLS
2025-07-16 10:15:22,593 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:22,952 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_950.NLS
2025-07-16 10:15:23,520 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:23,981 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_G18030.DLL
2025-07-16 10:15:24,743 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:25,171 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\c_GSM7.DLL
2025-07-16 10:15:26,076 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:26,271 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_IS2022.DLL
2025-07-16 10:15:27,275 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:27,516 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\C_ISCII.DLL
2025-07-16 10:15:28,057 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:43,895 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d2d1.dll
2025-07-16 10:15:44,171 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:45,211 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d2d1debug3.dll
2025-07-16 10:15:45,563 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:47,053 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d10.dll
2025-07-16 10:15:47,387 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:47,475 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d10core.dll
2025-07-16 10:15:47,785 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:48,438 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d10level9.dll
2025-07-16 10:15:48,741 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:48,793 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d10ref.dll
2025-07-16 10:15:49,094 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:49,156 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d10sdklayers.dll
2025-07-16 10:15:49,479 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:15:59,504 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d10warp.dll
2025-07-16 10:15:59,784 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:00,118 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d10_1.dll
2025-07-16 10:16:00,452 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:00,923 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d10_1core.dll
2025-07-16 10:16:01,223 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:06,643 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d11.dll
2025-07-16 10:16:06,959 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:08,245 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d11on12.dll
2025-07-16 10:16:08,566 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:10,153 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d11_3SDKLayers.dll
2025-07-16 10:16:10,446 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:10,811 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3D12.dll
2025-07-16 10:16:11,111 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:16,332 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3D12Core.dll
2025-07-16 10:16:16,638 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:25,352 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d12SDKLayers.dll
2025-07-16 10:16:25,671 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:25,740 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d8thk.dll
2025-07-16 10:16:26,058 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:29,058 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d9.dll
2025-07-16 10:16:29,351 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:31,610 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3d9on12.dll
2025-07-16 10:16:31,941 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:33,615 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_33.dll
2025-07-16 10:16:33,931 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:35,498 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_34.dll
2025-07-16 10:16:35,858 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:38,602 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_35.dll
2025-07-16 10:16:38,892 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:41,549 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_36.dll
2025-07-16 10:16:41,850 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:44,487 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_37.dll
2025-07-16 10:16:44,803 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:47,552 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_38.dll
2025-07-16 10:16:47,856 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:51,437 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_39.dll
2025-07-16 10:16:51,780 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:16:55,260 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_40.dll
2025-07-16 10:16:55,543 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:03,371 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_41.dll
2025-07-16 10:17:03,788 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:14,230 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_42.dll
2025-07-16 10:17:14,572 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:18,837 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_43.dll
2025-07-16 10:17:19,210 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:27,549 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DCompiler_47.dll
2025-07-16 10:17:27,845 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:28,434 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dconfig.exe
2025-07-16 10:17:28,780 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:35,468 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dcsx_42.dll
2025-07-16 10:17:35,816 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:37,336 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dcsx_43.dll
2025-07-16 10:17:37,613 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:38,256 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dref9.dll
2025-07-16 10:17:38,526 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:39,128 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DSCache.dll
2025-07-16 10:17:39,441 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:40,060 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10.dll
2025-07-16 10:17:40,395 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:41,228 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_33.dll
2025-07-16 10:17:41,536 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:42,146 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_34.dll
2025-07-16 10:17:42,437 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:43,328 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_35.dll
2025-07-16 10:17:43,612 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:44,386 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_36.dll
2025-07-16 10:17:44,767 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:45,742 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_37.dll
2025-07-16 10:17:46,024 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:46,772 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_38.dll
2025-07-16 10:17:47,090 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:48,054 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_39.dll
2025-07-16 10:17:48,354 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:49,152 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_40.dll
2025-07-16 10:17:49,459 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:50,504 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_41.dll
2025-07-16 10:17:50,876 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:51,829 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_42.dll
2025-07-16 10:17:52,112 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:52,739 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx10_43.dll
2025-07-16 10:17:53,065 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:53,547 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx11_42.dll
2025-07-16 10:17:53,859 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:17:54,243 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx11_43.dll
2025-07-16 10:17:54,544 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:18:01,973 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_24.dll
2025-07-16 10:18:02,291 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:18:09,877 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_25.dll
2025-07-16 10:18:10,196 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:18:18,308 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_26.dll
2025-07-16 10:18:18,649 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:18:26,638 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_27.dll
2025-07-16 10:18:26,950 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:18:35,147 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_28.dll
2025-07-16 10:18:35,428 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:18:43,700 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_29.dll
2025-07-16 10:18:44,027 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:18:52,828 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_30.dll
2025-07-16 10:18:53,112 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:19:06,442 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_31.dll
2025-07-16 10:19:06,831 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:19:13,021 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_32.dll
2025-07-16 10:19:13,343 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:19:19,596 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_33.dll
2025-07-16 10:19:19,895 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:19:25,621 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_34.dll
2025-07-16 10:19:25,977 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:19:32,602 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_35.dll
2025-07-16 10:19:32,924 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:19:41,462 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d3dx9_36.dll
2025-07-16 10:19:41,780 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:19:50,495 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DX9_37.dll
2025-07-16 10:19:50,932 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:19:59,418 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DX9_38.dll
2025-07-16 10:19:59,822 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:08,093 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DX9_39.dll
2025-07-16 10:20:08,458 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:15,834 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DX9_40.dll
2025-07-16 10:20:16,207 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:23,271 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DX9_41.dll
2025-07-16 10:20:23,553 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:26,972 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DX9_42.dll
2025-07-16 10:20:27,275 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:33,752 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\D3DX9_43.dll
2025-07-16 10:20:34,182 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:34,228 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\d4d78066-e6db-44b7-b5cd-2eb82dce620c_HyperV-ComputeLegacy.dll
2025-07-16 10:20:34,590 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:35,424 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dab.dll
2025-07-16 10:20:35,823 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:35,929 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dabapi.dll
2025-07-16 10:20:36,313 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:37,069 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dafAspInfraProvider.dll
2025-07-16 10:20:37,446 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:38,942 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dafBth.dll
2025-07-16 10:20:39,376 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:39,771 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DafDnsSd.dll
2025-07-16 10:20:40,188 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:40,756 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dafDockingProvider.dll
2025-07-16 10:20:41,194 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:42,181 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DAFESCL.dll
2025-07-16 10:20:42,460 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:42,870 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DafGip.dll
2025-07-16 10:20:43,189 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:44,019 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DAFIPP.dll
2025-07-16 10:20:44,336 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:45,181 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DAFMCP.dll
2025-07-16 10:20:45,523 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:46,099 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dafpos.dll
2025-07-16 10:20:46,370 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:46,824 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DafPrintProvider.dll
2025-07-16 10:20:47,119 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:47,525 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dafupnp.dll
2025-07-16 10:20:47,849 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:48,173 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dafWCN.dll
2025-07-16 10:20:48,462 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:49,301 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dafWfdProvider.dll
2025-07-16 10:20:49,581 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:50,101 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DAFWiProv.dll
2025-07-16 10:20:50,498 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:51,098 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DAFWSD.dll
2025-07-16 10:20:51,381 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:51,680 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DAMediaManager.dll
2025-07-16 10:20:51,966 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:52,610 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DaOtpCredentialProvider.dll
2025-07-16 10:20:52,903 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:54,249 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\das.dll
2025-07-16 10:20:54,606 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:54,941 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dasHost.exe
2025-07-16 10:20:55,258 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:55,441 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dataclen.dll
2025-07-16 10:20:55,772 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:56,877 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DataExchange.dll
2025-07-16 10:20:57,225 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:58,028 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DataExchangeHost.exe
2025-07-16 10:20:58,325 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:58,675 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DataStoreCacheDumpTool.exe
2025-07-16 10:20:58,946 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:59,161 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\davclnt.dll
2025-07-16 10:20:59,505 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:20:59,623 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\davhlpr.dll
2025-07-16 10:20:59,949 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:01,238 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DavSyncProvider.dll
2025-07-16 10:21:01,702 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:05,025 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\daxexec.dll
2025-07-16 10:21:05,327 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:05,776 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dbgcore.dll
2025-07-16 10:21:06,089 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:18,946 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dbgeng.dll
2025-07-16 10:21:19,235 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:23,564 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dbghelp.dll
2025-07-16 10:21:23,886 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:25,702 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DbgModel.dll
2025-07-16 10:21:25,957 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:26,130 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dbnetlib.dll
2025-07-16 10:21:26,411 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:26,481 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dbnmpntw.dll
2025-07-16 10:21:26,773 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:27,234 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dccw.exe
2025-07-16 10:21:27,577 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:27,636 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dciman32.dll
2025-07-16 10:21:27,925 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:30,233 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dcntel.dll
2025-07-16 10:21:30,550 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:30,604 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dcomcnfg.exe
2025-07-16 10:21:30,896 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:35,201 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dcomp.dll
2025-07-16 10:21:35,488 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:36,933 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dcsvc.dll
2025-07-16 10:21:37,198 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:37,301 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DDACLSys.dll
2025-07-16 10:21:37,856 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:37,950 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DdcClaimsApi.dll
2025-07-16 10:21:38,242 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:38,382 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DdcComImplementationsDesktop.dll
2025-07-16 10:21:38,725 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:39,831 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DDDS.dll
2025-07-16 10:21:40,183 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:42,441 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ddisplay.dll
2025-07-16 10:21:43,122 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:43,465 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ddodiag.exe
2025-07-16 10:21:44,079 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:44,234 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DDOIProxy.dll
2025-07-16 10:21:44,637 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:44,852 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DDORes.dll
2025-07-16 10:21:45,252 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:48,719 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ddraw.dll
2025-07-16 10:21:49,301 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:49,943 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ddrawex.dll
2025-07-16 10:21:50,684 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:50,942 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\declaredconfiguration.dll
2025-07-16 10:21:51,551 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:51,605 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DefaultAccountTile.png
2025-07-16 10:21:52,822 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:21:53,048 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DefaultDeviceManager.dll
2025-07-16 10:21:53,655 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:00,016 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DefaultHrtfs.bin
2025-07-16 10:22:00,514 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:00,756 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DefaultPrinterProvider.dll
2025-07-16 10:22:01,356 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:01,367 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DefaultQuestions.json
2025-07-16 10:22:02,292 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:03,223 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Defrag.exe
2025-07-16 10:22:04,122 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:04,307 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\defragproxy.dll
2025-07-16 10:22:05,023 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:05,115 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\defragres.dll
2025-07-16 10:22:06,070 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:10,343 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\defragsvc.dll
2025-07-16 10:22:11,046 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:11,359 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\delegatorprovider.dll
2025-07-16 10:22:12,006 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:12,093 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeliveryOptimizationMIProv.mof
2025-07-16 10:22:12,816 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:12,881 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeliveryOptimizationMIProvUninstall.mof
2025-07-16 10:22:13,621 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:14,058 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\deploymentcsphelper.exe
2025-07-16 10:22:14,803 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:15,157 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\deploymentcsps.dll
2025-07-16 10:22:16,508 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:17,668 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\desk.cpl
2025-07-16 10:22:18,521 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:19,003 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\deskadp.dll
2025-07-16 10:22:19,706 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:20,070 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\deskmon.dll
2025-07-16 10:22:20,706 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:21,594 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\desktopimgdownldr.exe
2025-07-16 10:22:22,210 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:22,743 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DesktopShellAppStateContract.dll
2025-07-16 10:22:23,273 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:25,271 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DesktopShellExt.dll
2025-07-16 10:22:25,567 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:26,445 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DesktopSwitcherDataModel.dll
2025-07-16 10:22:26,738 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:26,767 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DetailedReading-Default.xml
2025-07-16 10:22:27,114 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:27,356 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DevDispItemProvider.dll
2025-07-16 10:22:27,703 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:29,070 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeveloperOptionsSettingsHandlers.dll
2025-07-16 10:22:29,347 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:29,561 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\devenum.dll
2025-07-16 10:22:29,913 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:30,620 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\deviceaccess.dll
2025-07-16 10:22:30,946 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:31,133 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\deviceassociation.dll
2025-07-16 10:22:31,725 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:32,066 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceCensus.exe
2025-07-16 10:22:32,408 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:33,770 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceCenter.dll
2025-07-16 10:22:34,105 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:34,377 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceCompanionAppInstall.dll
2025-07-16 10:22:34,698 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:34,926 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceCredential.dll
2025-07-16 10:22:35,207 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:35,417 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceCredentialDeployment.exe
2025-07-16 10:22:35,722 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:36,203 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceDirectoryClient.dll
2025-07-16 10:22:36,486 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:36,624 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceDisplayStatusManager.dll
2025-07-16 10:22:36,921 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:37,437 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceDriverRetrievalClient.dll
2025-07-16 10:22:37,742 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:37,803 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceEject.exe
2025-07-16 10:22:38,079 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:38,440 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceElementSource.dll
2025-07-16 10:22:38,738 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:39,537 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceEnroller.exe
2025-07-16 10:22:40,035 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:40,076 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceFeatureDDF.json
2025-07-16 10:22:40,633 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:46,585 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceFlows.DataModel.dll
2025-07-16 10:22:46,892 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:47,129 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceMetadataRetrievalClient.dll
2025-07-16 10:22:47,499 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:48,780 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\devicengccredprov.dll
2025-07-16 10:22:49,083 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:49,643 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DevicePairing.dll
2025-07-16 10:22:49,970 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:50,450 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DevicePairingExperienceMEM.dll
2025-07-16 10:22:50,860 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:51,343 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DevicePairingFolder.dll
2025-07-16 10:22:51,697 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:51,766 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DevicePairingProxy.dll
2025-07-16 10:22:52,324 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:52,681 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DevicePairingWizard.exe
2025-07-16 10:22:53,136 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:53,339 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceProperties.exe
2025-07-16 10:22:53,697 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:53,887 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceReactivation.dll
2025-07-16 10:22:54,237 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:54,501 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\deviceregistration.dll
2025-07-16 10:22:54,815 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:57,259 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceSetupManager.dll
2025-07-16 10:22:57,602 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:58,114 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceSetupManagerAPI.dll
2025-07-16 10:22:58,532 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:22:58,933 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceSetupStatusProvider.dll
2025-07-16 10:22:59,264 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:02,365 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DevicesFlowBroker.dll
2025-07-16 10:23:02,689 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:03,591 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceSoftwareInstallationClient.dll
2025-07-16 10:23:03,906 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:04,204 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceUpdateAgent.dll
2025-07-16 10:23:04,542 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:04,606 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DeviceUxRes.dll
2025-07-16 10:23:04,943 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:06,455 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\devinv.dll
2025-07-16 10:23:06,773 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:06,895 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\devmgmt.msc
2025-07-16 10:23:07,202 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:09,131 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\devmgr.dll
2025-07-16 10:23:09,617 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:09,668 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DevModeRunAsUserConfig.msc
2025-07-16 10:23:10,356 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:10,903 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\devobj.dll
2025-07-16 10:23:11,223 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:11,556 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DevPropMgr.dll
2025-07-16 10:23:11,871 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:12,149 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DevQueryBroker.dll
2025-07-16 10:23:12,438 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:12,761 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\devrtl.dll
2025-07-16 10:23:13,099 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:13,122 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dev_64.vp
2025-07-16 10:23:13,431 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:13,547 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dfdts.dll
2025-07-16 10:23:13,896 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:14,053 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DFDWiz.exe
2025-07-16 10:23:14,392 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:14,630 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dfrgui.exe
2025-07-16 10:23:15,041 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:15,281 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dfscli.dll
2025-07-16 10:23:15,641 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:22,338 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dfshim.dll
2025-07-16 10:23:22,722 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:23,078 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DfsShlEx.dll
2025-07-16 10:23:23,414 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:23,512 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dhcpcmonitor.dll
2025-07-16 10:23:23,828 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:24,416 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dhcpcore.dll
2025-07-16 10:23:24,731 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:25,552 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dhcpcore6.dll
2025-07-16 10:23:25,851 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:26,148 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dhcpcsvc.dll
2025-07-16 10:23:26,457 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:26,637 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dhcpcsvc6.dll
2025-07-16 10:23:26,960 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:27,670 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dhcpsapi.dll
2025-07-16 10:23:28,090 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:30,102 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DiagCpl.dll
2025-07-16 10:23:30,520 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:30,714 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\diagnosticdataquery.dll
2025-07-16 10:23:31,049 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:31,163 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DiagnosticDataSettings.dll
2025-07-16 10:23:31,785 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:32,060 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DiagnosticInvoker.dll
2025-07-16 10:23:32,365 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:33,133 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DiagnosticLogCSP.dll
2025-07-16 10:23:33,442 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:35,861 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\diagperf.dll
2025-07-16 10:23:36,328 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:37,501 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DiagSvc.dll
2025-07-16 10:23:37,786 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:47,053 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\diagtrack.dll
2025-07-16 10:23:47,370 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:47,800 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dialclient.dll
2025-07-16 10:23:48,358 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:48,470 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dialer.exe
2025-07-16 10:23:48,753 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:49,177 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dialserver.dll
2025-07-16 10:23:49,727 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:50,627 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DictationManager.dll
2025-07-16 10:23:51,055 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:51,833 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\difxapi.dll
2025-07-16 10:23:52,565 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:53,072 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dimsjob.dll
2025-07-16 10:23:53,605 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:53,947 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dimsroam.dll
2025-07-16 10:23:54,279 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:54,713 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dinput.dll
2025-07-16 10:23:55,026 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:55,821 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dinput8.dll
2025-07-16 10:23:56,154 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:56,235 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Direct2DDesktop.dll
2025-07-16 10:23:56,518 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:23:58,431 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\directmanipulation.dll
2025-07-16 10:23:58,767 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:00,471 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DirectML.Debug.dll
2025-07-16 10:24:00,854 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:08,634 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\directml.dll
2025-07-16 10:24:08,963 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:09,206 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\directsr.dll
2025-07-16 10:24:09,544 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:10,125 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\directxdatabasehelper.dll
2025-07-16 10:24:10,477 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:10,695 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\directxdatabaseupdater.exe
2025-07-16 10:24:11,021 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:11,752 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\discan.dll
2025-07-16 10:24:12,054 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:12,109 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\diskmgmt.msc
2025-07-16 10:24:12,426 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:12,683 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\diskpart.exe
2025-07-16 10:24:12,957 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:13,036 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\diskperf.exe
2025-07-16 10:24:13,316 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:13,764 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\diskraid.exe
2025-07-16 10:24:14,035 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:14,153 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DiskSnapshot.conf
2025-07-16 10:24:14,418 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:14,587 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DiskSnapshot.exe
2025-07-16 10:24:14,872 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:15,005 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\diskusage.exe
2025-07-16 10:24:15,311 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:15,834 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Dism.exe
2025-07-16 10:24:16,179 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:17,878 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DismApi.dll
2025-07-16 10:24:18,230 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:20,014 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DispBroker.Desktop.dll
2025-07-16 10:24:20,495 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:21,507 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DispBroker.dll
2025-07-16 10:24:21,796 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:22,103 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dispdiag.exe
2025-07-16 10:24:22,427 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:22,557 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dispex.dll
2025-07-16 10:24:22,824 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:23,154 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Display.dll
2025-07-16 10:24:23,436 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:23,815 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DisplayManager.dll
2025-07-16 10:24:24,142 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:25,238 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DisplaySwitch.exe
2025-07-16 10:24:25,517 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:25,531 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DisplaySystemToastIcon.contrast-white.png
2025-07-16 10:24:25,820 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:25,835 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DisplaySystemToastIcon.png
2025-07-16 10:24:26,132 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:26,167 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\djctq.rs
2025-07-16 10:24:26,628 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:26,820 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\djoin.exe
2025-07-16 10:24:27,108 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:27,161 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dllhost.exe
2025-07-16 10:24:27,443 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:27,507 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dllhst3g.exe
2025-07-16 10:24:27,832 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:28,424 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dlnashext.dll
2025-07-16 10:24:28,701 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:28,752 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DMAlertListener.ProxyStub.dll
2025-07-16 10:24:29,057 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:29,215 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DmApiSetExtImplDesktop.dll
2025-07-16 10:24:29,499 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:29,520 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DMAppsRes.dll
2025-07-16 10:24:29,823 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:30,301 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmcertinst.exe
2025-07-16 10:24:30,620 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:30,792 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmcfghost.exe
2025-07-16 10:24:31,108 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:31,559 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmcfgutils.dll
2025-07-16 10:24:31,878 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:32,119 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmclient.exe
2025-07-16 10:24:32,397 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:33,775 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmcmnutils.dll
2025-07-16 10:24:34,059 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:34,114 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmcommandlineutils.dll
2025-07-16 10:24:34,396 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:34,780 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmcsps.dll
2025-07-16 10:24:35,279 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:35,857 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmdlgs.dll
2025-07-16 10:24:36,151 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:36,767 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmdskmgr.dll
2025-07-16 10:24:37,047 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:37,067 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmdskres.dll
2025-07-16 10:24:37,358 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:37,376 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmdskres2.dll
2025-07-16 10:24:37,665 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:39,070 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmenrollengine.dll
2025-07-16 10:24:39,351 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:39,866 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmenterprisediagnostics.dll
2025-07-16 10:24:40,146 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:40,499 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmintf.dll
2025-07-16 10:24:40,805 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:40,867 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmiso8601utils.dll
2025-07-16 10:24:41,128 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:41,330 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmloader.dll
2025-07-16 10:24:41,632 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:41,729 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DmNotificationBroker.exe
2025-07-16 10:24:42,004 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:42,271 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmocx.dll
2025-07-16 10:24:42,552 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:42,636 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmoleaututils.dll
2025-07-16 10:24:42,914 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:43,016 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DmOmaCpMo.exe
2025-07-16 10:24:43,310 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:44,780 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DmOsConfig.dll
2025-07-16 10:24:45,095 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:45,205 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmprocessxmlfiltered.dll
2025-07-16 10:24:45,482 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:45,564 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmpushproxy.dll
2025-07-16 10:24:45,850 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:46,531 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DMPushRouterCore.dll
2025-07-16 10:24:46,829 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:53,240 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DMRCDecoder.dll
2025-07-16 10:24:53,662 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:55,344 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DMRServer.dll
2025-07-16 10:24:55,683 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:56,366 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmsynth.dll
2025-07-16 10:24:56,643 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:57,072 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmusic.dll
2025-07-16 10:24:57,399 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:57,481 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmutil.dll
2025-07-16 10:24:57,803 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:58,105 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmvdsitf.dll
2025-07-16 10:24:58,403 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:58,861 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmview.ocx
2025-07-16 10:24:59,147 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:59,370 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmwappushsvc.dll
2025-07-16 10:24:59,652 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:24:59,870 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmwmicsp.dll
2025-07-16 10:25:00,188 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:00,515 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dmxmlhelputils.dll
2025-07-16 10:25:00,820 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:00,980 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dns-sd.exe
2025-07-16 10:25:01,265 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:03,287 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dnsapi.dll
2025-07-16 10:25:03,578 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:03,694 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dnscacheugc.exe
2025-07-16 10:25:03,950 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:04,248 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DnsClientCSP.dll
2025-07-16 10:25:04,521 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:04,653 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dnscmmc.dll
2025-07-16 10:25:04,969 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:05,016 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dnsext.dll
2025-07-16 10:25:05,280 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:06,561 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dnsrslvr.dll
2025-07-16 10:25:06,828 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:06,970 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dnssd.dll
2025-07-16 10:25:07,248 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:07,762 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dnssdX.dll
2025-07-16 10:25:08,073 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:08,384 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Docking.VirtualInput.dll
2025-07-16 10:25:08,655 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:08,728 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DockInterface.ProxyStub.dll
2025-07-16 10:25:09,048 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:11,556 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\doclient.dll
2025-07-16 10:25:11,847 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:11,996 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\docprop.dll
2025-07-16 10:25:12,271 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:12,330 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DocumentPerformanceEvents.dll
2025-07-16 10:25:12,633 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:14,161 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DolbyDecMFT.dll
2025-07-16 10:25:14,443 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:14,527 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DolbyDecMFT_redirect.dll
2025-07-16 10:25:14,829 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:15,027 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\domgmt.dll
2025-07-16 10:25:15,336 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:16,275 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\domiprov.dll
2025-07-16 10:25:16,570 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:17,422 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dosettings.dll
2025-07-16 10:25:17,696 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:17,789 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\doskey.exe
2025-07-16 10:25:18,155 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:18,442 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dosvc.dll
2025-07-16 10:25:18,765 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:18,999 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dot3api.dll
2025-07-16 10:25:19,339 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:19,755 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dot3cfg.dll
2025-07-16 10:25:20,232 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:20,484 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dot3gpclnt.dll
2025-07-16 10:25:20,738 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:21,334 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dot3gpui.dll
2025-07-16 10:25:21,616 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:21,823 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dot3msm.dll
2025-07-16 10:25:22,079 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:22,941 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dot3svc.dll
2025-07-16 10:25:23,232 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:23,590 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dot3ui.dll
2025-07-16 10:25:23,896 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:23,972 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpapi.dll
2025-07-16 10:25:24,246 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:24,372 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpapimig.exe
2025-07-16 10:25:24,754 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:24,984 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpapiprovider.dll
2025-07-16 10:25:25,290 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:26,041 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpapisrv.dll
2025-07-16 10:25:26,306 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:26,443 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DpiScaling.exe
2025-07-16 10:25:26,722 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:26,982 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dplcsp.dll
2025-07-16 10:25:27,237 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:27,264 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpnaddr.dll
2025-07-16 10:25:27,566 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:27,695 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpnathlp.dll
2025-07-16 10:25:27,974 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:28,870 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpnet.dll
2025-07-16 10:25:29,139 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:29,208 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpnhpast.dll
2025-07-16 10:25:29,499 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:29,548 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpnhupnp.dll
2025-07-16 10:25:29,838 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:29,877 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpnlobby.dll
2025-07-16 10:25:30,160 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:30,227 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpnsvr.exe
2025-07-16 10:25:30,517 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:30,809 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dps.dll
2025-07-16 10:25:31,073 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:33,791 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dpx.dll
2025-07-16 10:25:34,099 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:34,251 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DragDropExperienceDataExchangeDelegated.dll
2025-07-16 10:25:34,542 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:34,753 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\driverquery.exe
2025-07-16 10:25:35,060 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:35,148 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\drprov.dll
2025-07-16 10:25:35,428 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:35,496 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DrtmAuthTxt.wim
2025-07-16 10:25:35,805 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:36,733 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\drvinst.exe
2025-07-16 10:25:36,985 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:37,373 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\drvsetup.dll
2025-07-16 10:25:37,689 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:40,183 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\drvstore.dll
2025-07-16 10:25:40,439 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:40,543 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsauth.dll
2025-07-16 10:25:40,806 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:41,639 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DscCore.dll
2025-07-16 10:25:41,934 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:42,252 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DscCoreConfProv.dll
2025-07-16 10:25:42,512 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:42,626 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsclient.dll
2025-07-16 10:25:42,915 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:43,122 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dscproxy.dll
2025-07-16 10:25:43,412 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:43,490 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DscTimer.dll
2025-07-16 10:25:43,769 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:44,165 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsdmo.dll
2025-07-16 10:25:44,445 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:44,876 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dskquota.dll
2025-07-16 10:25:45,150 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:45,602 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dskquoui.dll
2025-07-16 10:25:45,871 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:45,945 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DsmUserTask.exe
2025-07-16 10:25:46,204 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:47,088 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsound.dll
2025-07-16 10:25:47,366 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:47,463 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsparse.dll
2025-07-16 10:25:47,774 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:50,935 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsprop.dll
2025-07-16 10:25:55,740 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:25:57,596 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsquery.dll
2025-07-16 10:25:58,055 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:01,052 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsreg.dll
2025-07-16 10:26:01,358 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:02,282 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsregcmd.exe
2025-07-16 10:26:02,615 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:02,727 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsregtask.dll
2025-07-16 10:26:03,011 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:03,086 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsrole.dll
2025-07-16 10:26:03,383 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:03,492 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dssec.dat
2025-07-16 10:26:03,806 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:03,929 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dssec.dll
2025-07-16 10:26:04,190 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:04,519 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dssenh.dll
2025-07-16 10:26:04,790 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:05,143 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dssvc.dll
2025-07-16 10:26:05,434 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:05,513 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dstokenclean.exe
2025-07-16 10:26:05,797 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:06,146 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Dsui.dll
2025-07-16 10:26:06,445 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:06,626 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dsuiext.dll
2025-07-16 10:26:06,896 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:07,045 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dswave.dll
2025-07-16 10:26:07,350 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:07,568 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dtdump.exe
2025-07-16 10:26:07,907 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:08,116 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dtsh.dll
2025-07-16 10:26:08,427 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:08,523 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DTSPipelinePerf160.dll
2025-07-16 10:26:08,806 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:09,087 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DuCsps.dll
2025-07-16 10:26:09,456 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:12,556 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dui70.dll
2025-07-16 10:26:12,852 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:14,117 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\duser.dll
2025-07-16 10:26:14,397 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:14,535 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dusmapi.dll
2025-07-16 10:26:14,802 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:15,668 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dusmsvc.dll
2025-07-16 10:26:15,974 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:16,118 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dusmtask.exe
2025-07-16 10:26:16,448 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:16,510 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dvdplay.exe
2025-07-16 10:26:16,771 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:17,080 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dwm.exe
2025-07-16 10:26:17,326 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:17,737 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dwmapi.dll
2025-07-16 10:26:18,029 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:25,233 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dwmcore.dll
2025-07-16 10:26:25,733 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:25,923 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dwmghost.dll
2025-07-16 10:26:26,185 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:26,417 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dwminit.dll
2025-07-16 10:26:26,704 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:27,189 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dwmredir.dll
2025-07-16 10:26:27,442 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:30,182 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dwmscene.dll
2025-07-16 10:26:30,494 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:34,396 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DWrite.dll
2025-07-16 10:26:34,685 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:35,277 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DWWIN.EXE
2025-07-16 10:26:35,584 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:26:37,218 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DXCap.exe
2025-07-16 10:26:37,482 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:29,722 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DXCaptureReplay.dll
2025-07-16 10:27:30,050 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:30,658 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DXCore.dll
2025-07-16 10:27:31,045 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:31,930 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DXCpl.exe
2025-07-16 10:27:32,248 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:33,322 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxdiag.exe
2025-07-16 10:27:33,648 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:34,370 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxdiagn.dll
2025-07-16 10:27:34,749 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:37,318 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxgi.dll
2025-07-16 10:27:37,640 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:37,929 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxgiadaptercache.exe
2025-07-16 10:27:38,248 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:38,451 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DXGIDebug.dll
2025-07-16 10:27:38,765 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:38,978 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxgwdi.dll
2025-07-16 10:27:39,304 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:41,640 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxilconv.dll
2025-07-16 10:27:41,975 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:42,034 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxmasf.dll
2025-07-16 10:27:42,369 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:43,102 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DXP.dll
2025-07-16 10:27:43,453 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:43,546 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxpps.dll
2025-07-16 10:27:43,885 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:44,833 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Dxpserver.exe
2025-07-16 10:27:45,144 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:45,803 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DxpTaskSync.dll
2025-07-16 10:27:46,112 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:46,986 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxtmsft.dll
2025-07-16 10:27:47,403 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:47,826 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DXToolsMonitor.dll
2025-07-16 10:27:48,138 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:53,735 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DXToolsOfflineAnalysis.dll
2025-07-16 10:27:54,044 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:54,121 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DxToolsReportGenerator.dll
2025-07-16 10:27:54,436 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:54,730 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DXToolsReporting.dll
2025-07-16 10:27:55,077 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:55,570 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxtrans.dll
2025-07-16 10:27:55,929 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:56,555 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dxva2.dll
2025-07-16 10:27:56,916 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:57,295 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DynamicLong.bin
2025-07-16 10:27:57,651 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:58,043 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DynamicMedium.bin
2025-07-16 10:27:58,365 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:58,551 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\DynamicShort.bin
2025-07-16 10:27:58,857 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:59,067 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\dynamoapi.dll
2025-07-16 10:27:59,420 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:59,571 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EAMProgressHandler.dll
2025-07-16 10:27:59,893 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:27:59,977 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Eap3Host.exe
2025-07-16 10:28:00,321 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:00,869 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eapp3hst.dll
2025-07-16 10:28:01,227 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:02,045 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eappcfg.dll
2025-07-16 10:28:02,362 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:02,978 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eappcfgui.dll
2025-07-16 10:28:03,311 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:03,651 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eappgnui.dll
2025-07-16 10:28:03,956 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:04,827 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eapphost.dll
2025-07-16 10:28:05,158 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:05,353 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eappprxy.dll
2025-07-16 10:28:05,653 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:05,873 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eapprovp.dll
2025-07-16 10:28:06,178 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:07,154 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eapputil.dll
2025-07-16 10:28:07,483 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:07,792 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eapsimextdesktop.dll
2025-07-16 10:28:08,114 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:08,399 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eapsvc.dll
2025-07-16 10:28:08,732 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:09,163 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EapTeapAuth.dll
2025-07-16 10:28:09,663 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:10,013 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EapTeapConfig.dll
2025-07-16 10:28:10,324 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:10,672 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EapTeapExt.dll
2025-07-16 10:28:10,979 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:10,999 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EarbudsSystemToastIcon.contrast-white.png
2025-07-16 10:28:11,306 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:11,327 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EarbudsSystemToastIcon.png
2025-07-16 10:28:11,625 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:11,694 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\easconsent.dll
2025-07-16 10:28:12,002 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:12,618 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EaseOfAccessDialog.exe
2025-07-16 10:28:12,952 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:13,265 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\easinvoker.exe
2025-07-16 10:28:13,573 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:13,674 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\easinvoker.proxystub.dll
2025-07-16 10:28:13,955 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:14,146 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EASPolicyManagerBrokerHost.exe
2025-07-16 10:28:14,462 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:14,533 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EasPolicyManagerBrokerPS.dll
2025-07-16 10:28:14,844 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:15,227 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\easwrt.dll
2025-07-16 10:28:15,576 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:15,703 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EcoScoreTask.dll
2025-07-16 10:28:16,017 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:16,038 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ecoscore_config.json
2025-07-16 10:28:16,338 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:19,210 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\edgeangle.dll
2025-07-16 10:28:19,545 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:28:27,211 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EdgeContent.dll
2025-07-16 10:28:27,566 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:05,095 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\edgehtml.dll
2025-07-16 10:31:05,495 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:06,772 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\edgeIso.dll
2025-07-16 10:31:07,187 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:09,586 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EdgeManager.dll
2025-07-16 10:31:09,908 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:10,154 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EdgeResetPlugin.dll
2025-07-16 10:31:10,604 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:12,192 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EditBufferTestHook.dll
2025-07-16 10:31:12,491 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:12,697 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EditionUpgradeHelper.dll
2025-07-16 10:31:12,999 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:13,333 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EditionUpgradeManagerObj.dll
2025-07-16 10:31:13,649 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:13,989 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\edpauditapi.dll
2025-07-16 10:31:14,302 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:14,614 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EDPCleanup.exe
2025-07-16 10:31:14,895 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:15,443 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\edpcsp.dll
2025-07-16 10:31:15,739 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:15,996 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\edpnotify.exe
2025-07-16 10:31:16,548 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:17,051 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\edptask.dll
2025-07-16 10:31:17,500 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:18,281 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\edputil.dll
2025-07-16 10:31:18,602 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:18,828 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EduPrintProv.exe
2025-07-16 10:31:19,109 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:19,765 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eeprov.dll
2025-07-16 10:31:20,073 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:20,598 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eeutil.dll
2025-07-16 10:31:21,075 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:21,526 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\efsadu.dll
2025-07-16 10:31:21,823 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:24,360 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\efscore.dll
2025-07-16 10:31:24,620 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:24,968 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\efsext.dll
2025-07-16 10:31:25,268 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:25,457 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\efslsaext.dll
2025-07-16 10:31:25,732 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:25,948 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\efssvc.dll
2025-07-16 10:31:26,240 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:26,328 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\efsui.exe
2025-07-16 10:31:26,605 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:26,974 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\efsutil.dll
2025-07-16 10:31:27,225 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:28,917 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\efswrt.dll
2025-07-16 10:31:29,230 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:29,406 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EhStorAPI.dll
2025-07-16 10:31:29,696 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:29,846 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EhStorAuthn.exe
2025-07-16 10:31:30,121 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:30,250 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EhStorPwdMgr.dll
2025-07-16 10:31:30,534 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:30,760 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EhStorShell.dll
2025-07-16 10:31:31,054 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:31,680 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\els.dll
2025-07-16 10:31:31,952 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:32,259 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ELSCore.dll
2025-07-16 10:31:32,523 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:32,726 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\elshyph.dll
2025-07-16 10:31:33,095 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:34,710 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\elslad.dll
2025-07-16 10:31:35,009 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:35,163 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\elsTrans.dll
2025-07-16 10:31:35,502 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:38,070 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EmailApis.dll
2025-07-16 10:31:38,372 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:38,860 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\embeddedmodesvc.dll
2025-07-16 10:31:39,164 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:39,314 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\embeddedmodesvcapi.dll
2025-07-16 10:31:39,633 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:39,772 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EmojiDS.dll
2025-07-16 10:31:40,096 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:40,186 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\encapi.dll
2025-07-16 10:31:40,509 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:41,881 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\energy.dll
2025-07-16 10:31:42,202 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:42,866 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\energyprov.dll
2025-07-16 10:31:43,191 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:43,288 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\energytask.dll
2025-07-16 10:31:43,595 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:43,798 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\enrollmentapi.dll
2025-07-16 10:31:44,199 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:45,028 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EnterpriseAPNCsp.dll
2025-07-16 10:31:45,596 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:45,867 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EnterpriseAppMgmtClient.dll
2025-07-16 10:31:46,364 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:31:48,059 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EnterpriseAppMgmtSvc.dll
2025-07-16 10:31:48,433 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:01,210 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\enterprisecsps.dll
2025-07-16 10:32:01,806 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:02,467 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EnterpriseDesktopAppMgmtCSP.dll
2025-07-16 10:32:03,058 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:03,151 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\enterpriseetw.dll
2025-07-16 10:32:03,703 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:05,663 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EnterpriseModernAppMgmtCSP.dll
2025-07-16 10:32:06,295 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:07,011 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\enterpriseresourcemanager.dll
2025-07-16 10:32:07,653 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:08,769 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EoAExperiences.exe
2025-07-16 10:32:09,308 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:09,669 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eqossnap.dll
2025-07-16 10:32:10,146 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:11,454 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ErrorDetails.dll
2025-07-16 10:32:11,993 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:12,290 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ErrorDetailsCore.dll
2025-07-16 10:32:12,743 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:14,853 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\es.dll
2025-07-16 10:32:15,294 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:15,806 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EsclProtocol.dll
2025-07-16 10:32:16,231 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:17,839 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EsclScan.dll
2025-07-16 10:32:18,311 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:18,802 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EsclWiaDriver.dll
2025-07-16 10:32:19,219 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:19,434 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EsdSip.dll
2025-07-16 10:32:19,806 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:37,191 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\esent.dll
2025-07-16 10:32:37,729 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:38,110 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\esentprf.dll
2025-07-16 10:32:38,638 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:41,625 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\esentutl.exe
2025-07-16 10:32:42,002 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:43,672 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\esevss.dll
2025-07-16 10:32:44,044 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:45,073 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eShims.dll
2025-07-16 10:32:45,847 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:46,226 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\esimtool.exe
2025-07-16 10:32:47,010 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:47,208 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\esrb.rs
2025-07-16 10:32:47,917 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:49,515 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EthernetMediaManager.dll
2025-07-16 10:32:49,894 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:49,924 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ETWCoreUIComponentsResources.dll
2025-07-16 10:32:50,311 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:50,420 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ETWESEProviderResources.dll
2025-07-16 10:32:50,826 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:51,015 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EtwRundown.dll
2025-07-16 10:32:51,601 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:53,826 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eudcedit.exe
2025-07-16 10:32:54,309 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:54,890 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eUICCsCSP.dll
2025-07-16 10:32:55,269 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:55,608 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EventAggregation.dll
2025-07-16 10:32:56,060 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:56,221 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eventcls.dll
2025-07-16 10:32:56,644 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:56,833 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eventcreate.exe
2025-07-16 10:32:57,361 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:57,387 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\EventViewer_EventDetails.xsl
2025-07-16 10:32:57,888 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:58,188 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eventvwr.exe
2025-07-16 10:32:58,726 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:32:58,865 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\eventvwr.msc
2025-07-16 10:32:59,259 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:02,579 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\evr.dll
2025-07-16 10:33:03,381 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:04,831 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ExecModelClient.dll
2025-07-16 10:33:05,263 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:05,798 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\execmodelproxy.dll
2025-07-16 10:33:06,242 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:06,525 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\expand.exe
2025-07-16 10:33:06,988 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:17,645 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ExplorerFrame.dll
2025-07-16 10:33:18,416 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:19,818 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ExSMime.dll
2025-07-16 10:33:20,499 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:20,723 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\extrac32.exe
2025-07-16 10:33:21,324 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:21,950 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ExtrasXmlParser.dll
2025-07-16 10:33:22,917 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:23,070 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\f1db7d81-95be-4911-935a-8ab71629112a_HyperV-IsolatedVM.dll
2025-07-16 10:33:24,329 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:24,585 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\f3ahvoas.dll
2025-07-16 10:33:25,698 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:25,929 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\f989b52d-f928-44a3-9bf1-bf0c1da6a0d6_HyperV-DeviceVirtualization.dll
2025-07-16 10:33:27,242 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:34,127 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\facecredentialprovider.dll
2025-07-16 10:33:34,885 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:43,946 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Facilitator.dll
2025-07-16 10:33:44,976 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:45,680 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Family.Authentication.dll
2025-07-16 10:33:46,524 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:48,003 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Family.Cache.dll
2025-07-16 10:33:49,202 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:51,899 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Family.Client.dll
2025-07-16 10:33:52,761 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:54,094 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Family.SyncEngine.dll
2025-07-16 10:33:54,571 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:54,698 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FamilySafetyExt.dll
2025-07-16 10:33:55,118 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:57,450 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Faultrep.dll
2025-07-16 10:33:58,085 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:58,613 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FaxPrinterInstaller.dll
2025-07-16 10:33:59,200 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:33:59,474 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fc.exe
2025-07-16 10:34:00,354 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:05,997 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fcon.dll
2025-07-16 10:34:06,483 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:07,074 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdBth.dll
2025-07-16 10:34:07,543 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:07,632 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdBthProxy.dll
2025-07-16 10:34:08,103 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:08,328 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FdDevQuery.dll
2025-07-16 10:34:08,789 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:09,827 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fde.dll
2025-07-16 10:34:10,398 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:11,089 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdeploy.dll
2025-07-16 10:34:11,713 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:11,918 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdPHost.dll
2025-07-16 10:34:12,429 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:12,777 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdPnp.dll
2025-07-16 10:34:13,204 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:13,942 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdprint.dll
2025-07-16 10:34:14,418 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:14,696 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdProxy.dll
2025-07-16 10:34:15,444 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:15,637 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FDResPub.dll
2025-07-16 10:34:16,088 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:16,662 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdSSDP.dll
2025-07-16 10:34:17,087 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:17,515 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdWCN.dll
2025-07-16 10:34:18,111 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:18,342 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdWNet.dll
2025-07-16 10:34:19,063 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:20,621 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fdWSD.dll
2025-07-16 10:34:21,366 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:21,511 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FeatureToastBulldogImg.png
2025-07-16 10:34:22,096 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:22,137 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FeatureToastDlpImg.png
2025-07-16 10:34:22,581 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:23,807 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\feclient.dll
2025-07-16 10:34:24,268 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:24,628 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ffbroker.dll
2025-07-16 10:34:25,100 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:26,049 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhcat.dll
2025-07-16 10:34:26,506 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:28,206 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhcfg.dll
2025-07-16 10:34:28,670 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:28,954 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhcleanup.dll
2025-07-16 10:34:29,380 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:30,394 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhcpl.dll
2025-07-16 10:34:30,824 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:31,591 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhengine.dll
2025-07-16 10:34:32,091 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:32,842 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhevents.dll
2025-07-16 10:34:33,306 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:33,636 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhmanagew.exe
2025-07-16 10:34:34,025 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:35,634 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhsettingsprovider.dll
2025-07-16 10:34:36,516 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:37,026 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhshl.dll
2025-07-16 10:34:37,441 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:37,758 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhsrchapi.dll
2025-07-16 10:34:38,203 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:38,536 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhsrchph.dll
2025-07-16 10:34:38,973 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:39,389 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhsvc.dll
2025-07-16 10:34:39,827 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:40,006 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhsvcctl.dll
2025-07-16 10:34:40,474 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:40,787 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhtask.dll
2025-07-16 10:34:41,232 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:41,463 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhuxadapter.dll
2025-07-16 10:34:41,929 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:42,001 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhuxapi.dll
2025-07-16 10:34:42,452 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:42,561 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhuxcommon.dll
2025-07-16 10:34:43,027 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:43,267 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhuxgraphics.dll
2025-07-16 10:34:44,034 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:45,903 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fhuxpresentation.dll
2025-07-16 10:34:47,449 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:51,647 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fidocredprov.dll
2025-07-16 10:34:52,765 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:53,325 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FileAppxStreamingDataSource.dll
2025-07-16 10:34:54,233 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:55,596 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FileDialogBroker.exe
2025-07-16 10:34:56,403 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:34:57,711 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FileHistory.exe
2025-07-16 10:34:58,923 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:06,754 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\filemgmt.dll
2025-07-16 10:35:07,592 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:09,812 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FilterDS.dll
2025-07-16 10:35:10,611 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:10,867 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\find.exe
2025-07-16 10:35:11,499 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:11,973 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\findnetprinters.dll
2025-07-16 10:35:13,086 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:13,551 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\findstr.exe
2025-07-16 10:35:14,260 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:14,547 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\finger.exe
2025-07-16 10:35:15,343 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:16,384 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fingerprintcredential.dll
2025-07-16 10:35:16,926 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:17,035 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Firewall.cpl
2025-07-16 10:35:17,642 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:20,966 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FirewallAPI.dll
2025-07-16 10:35:21,399 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:23,958 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FirewallControlPanel.dll
2025-07-16 10:35:24,517 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:26,412 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FirewallUX.dll
2025-07-16 10:35:27,317 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:27,903 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FirmwareAttestationServerProxyStub.dll
2025-07-16 10:35:28,513 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:28,826 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fixmapi.exe
2025-07-16 10:35:29,440 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:35,860 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FlightSettings.dll
2025-07-16 10:35:36,367 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:36,535 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fltLib.dll
2025-07-16 10:35:37,038 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:37,351 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fltMC.exe
2025-07-16 10:35:37,874 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:38,229 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fltmgrres.dll
2025-07-16 10:35:38,783 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:35:59,627 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FluencyDS.dll
2025-07-16 10:36:00,769 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:01,778 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fmapi.dll
2025-07-16 10:36:02,703 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:04,121 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fmifs.dll
2025-07-16 10:36:05,197 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:07,392 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fms.dll
2025-07-16 10:36:08,054 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:08,951 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FNTCACHE.DAT
2025-07-16 10:36:09,416 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:15,143 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FntCache.dll
2025-07-16 10:36:15,489 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:15,605 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fodhelper.exe
2025-07-16 10:36:15,941 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:16,098 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Fondue.exe
2025-07-16 10:36:16,473 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:17,988 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fontdrvhost.exe
2025-07-16 10:36:18,474 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:21,404 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fontext.dll
2025-07-16 10:36:21,810 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:22,034 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FontGlyphAnimator.dll
2025-07-16 10:36:22,428 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:22,643 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fontgroupsoverride.dll
2025-07-16 10:36:23,115 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:23,513 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FontProvider.dll
2025-07-16 10:36:23,840 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:24,117 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fontsub.dll
2025-07-16 10:36:24,447 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:24,890 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fontview.exe
2025-07-16 10:36:25,381 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:25,696 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\forfiles.exe
2025-07-16 10:36:26,328 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:26,545 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\format.com
2025-07-16 10:36:27,325 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:27,454 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fpb.rs
2025-07-16 10:36:27,871 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:28,297 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fphc.dll
2025-07-16 10:36:28,851 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:30,323 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\framedyn.dll
2025-07-16 10:36:30,930 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:34,696 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\framedynos.dll
2025-07-16 10:36:35,429 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:43,985 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FrameServer.dll
2025-07-16 10:36:45,038 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:36:55,418 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FrameServerClient.dll
2025-07-16 10:36:56,741 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:37:19,391 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FrameServerCore.dll
2025-07-16 10:37:21,755 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:37:27,950 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FrameServerMonitor.dll
2025-07-16 10:37:29,020 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:37:33,857 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FrameServerMonitorClient.dll
2025-07-16 10:37:35,385 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:37:36,664 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\frprov.dll
2025-07-16 10:37:38,650 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:37:38,961 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fsavailux.exe
2025-07-16 10:37:40,628 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:37:42,053 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FsIso.exe
2025-07-16 10:37:43,616 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:37:43,980 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fsmgmt.msc
2025-07-16 10:37:44,661 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:37:52,837 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FsNVSDeviceSource.dll
2025-07-16 10:37:53,956 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:37:55,466 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fsquirt.exe
2025-07-16 10:37:56,490 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:37:57,407 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fssres.dll
2025-07-16 10:37:58,415 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:04,440 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fstx.dll
2025-07-16 10:38:05,746 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:10,021 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fsutil.exe
2025-07-16 10:38:12,379 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:13,684 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fsutilext.dll
2025-07-16 10:38:15,053 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:16,469 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fthsvc.dll
2025-07-16 10:38:18,388 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:19,760 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ftp.exe
2025-07-16 10:38:20,995 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:24,431 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fundisc.dll
2025-07-16 10:38:25,642 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:35,809 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fveapi.dll
2025-07-16 10:38:36,296 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:39,079 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fveapibase.dll
2025-07-16 10:38:39,670 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:40,064 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fvecerts.dll
2025-07-16 10:38:40,801 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:43,994 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fvecpl.dll
2025-07-16 10:38:45,057 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:46,397 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fvenotify.exe
2025-07-16 10:38:48,013 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:49,366 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fveskybackup.dll
2025-07-16 10:38:50,012 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:53,130 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fveui.dll
2025-07-16 10:38:54,086 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:38:58,632 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fvewiz.dll
2025-07-16 10:38:59,151 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:00,778 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FvSDK_x64.dll
2025-07-16 10:39:01,507 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:03,586 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fwbase.dll
2025-07-16 10:39:04,255 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:05,229 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fwcfg.dll
2025-07-16 10:39:06,303 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:07,576 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fwmdmcsp.dll
2025-07-16 10:39:07,967 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:09,390 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\fwpolicyiomgr.dll
2025-07-16 10:39:09,796 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:10,937 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FWPUCLNT.DLL
2025-07-16 10:39:11,227 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:11,509 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FwRemoteSvr.dll
2025-07-16 10:39:11,824 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:12,434 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSAPI.dll
2025-07-16 10:39:12,812 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:13,117 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSCOM.dll
2025-07-16 10:39:13,467 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:14,878 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSCOMEX.dll
2025-07-16 10:39:15,456 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:16,326 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSCOMPOSE.dll
2025-07-16 10:39:16,649 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:16,713 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSCOMPOSERES.dll
2025-07-16 10:39:17,143 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:18,030 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSCOVER.exe
2025-07-16 10:39:18,359 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:18,396 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSEVENT.dll
2025-07-16 10:39:18,717 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:18,928 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSMON.dll
2025-07-16 10:39:19,200 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:19,900 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSRESM.dll
2025-07-16 10:39:20,166 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:20,394 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSROUTE.dll
2025-07-16 10:39:20,674 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:21,378 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSST.dll
2025-07-16 10:39:21,649 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:22,834 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSSVC.exe
2025-07-16 10:39:23,116 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:23,628 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXST30.dll
2025-07-16 10:39:23,960 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:24,337 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSTIFF.dll
2025-07-16 10:39:24,661 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:24,785 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSUNATD.exe
2025-07-16 10:39:25,069 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:25,388 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\FXSUTILITY.dll
2025-07-16 10:39:25,658 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:25,768 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\g711codc.ax
2025-07-16 10:39:26,033 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:26,753 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GameBarPresenceWriter.exe
2025-07-16 10:39:27,031 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:27,106 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GameBarPresenceWriter.proxy.dll
2025-07-16 10:39:27,377 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:27,440 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GameChatOverlayExt.dll
2025-07-16 10:39:27,758 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:28,077 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GameChatTranscription.dll
2025-07-16 10:39:28,340 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:28,983 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GameInput.dll
2025-07-16 10:39:29,263 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:29,387 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GameInputSvc.exe
2025-07-16 10:39:29,713 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:29,913 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gamemode.dll
2025-07-16 10:39:30,162 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:32,742 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GamePanel.exe
2025-07-16 10:39:33,017 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:33,086 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GamePanelExternalHook.dll
2025-07-16 10:39:33,355 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:33,392 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gamestreamingext.dll
2025-07-16 10:39:33,685 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:33,701 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GameSystemToastIcon.contrast-white.png
2025-07-16 10:39:33,974 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:33,988 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GameSystemToastIcon.png
2025-07-16 10:39:34,318 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:34,387 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gameux.dll
2025-07-16 10:39:34,674 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:35,095 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gamingtcui.dll
2025-07-16 10:39:35,392 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:35,435 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gb2312.uce
2025-07-16 10:39:35,717 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:35,856 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gcdef.dll
2025-07-16 10:39:36,186 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:36,567 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gdi32.dll
2025-07-16 10:39:36,946 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:38,810 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gdi32full.dll
2025-07-16 10:39:39,111 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:42,188 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GdiPlus.dll
2025-07-16 10:39:42,476 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:43,320 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\generaltel.dll
2025-07-16 10:39:43,572 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:43,806 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\genpix.dll
2025-07-16 10:39:44,259 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:44,835 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GenValObj.exe
2025-07-16 10:39:45,153 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:45,402 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Geocommon.dll
2025-07-16 10:39:45,667 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:46,648 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Geolocation.dll
2025-07-16 10:39:46,907 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:47,280 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\getmac.exe
2025-07-16 10:39:47,551 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:47,601 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\getuname.dll
2025-07-16 10:39:47,899 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:47,901 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GfxValDisplayLog.bin
2025-07-16 10:39:48,176 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:48,925 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\glmf32.dll
2025-07-16 10:39:49,201 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:49,679 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\globinputhost.dll
2025-07-16 10:39:49,951 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:50,212 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\glu32.dll
2025-07-16 10:39:50,505 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:50,708 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gmsaclient.dll
2025-07-16 10:39:50,979 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:51,284 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gpapi.dll
2025-07-16 10:39:51,653 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:52,008 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GPCSEWrapperCsp.dll
2025-07-16 10:39:52,323 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:53,193 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gpedit.dll
2025-07-16 10:39:53,491 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:53,582 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gpprnext.dll
2025-07-16 10:39:53,860 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:54,251 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gpresult.exe
2025-07-16 10:39:54,534 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:56,278 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gpsvc.dll
2025-07-16 10:39:56,554 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:56,628 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gptext.dll
2025-07-16 10:39:56,905 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:57,038 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gpupdate.exe
2025-07-16 10:39:57,314 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:58,054 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\gpupvdev.dll
2025-07-16 10:39:58,379 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:58,848 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GraphicsCapture.dll
2025-07-16 10:39:59,146 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:39:59,813 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\GraphicsPerfSvc.dll
2025-07-16 10:40:00,121 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:00,157 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\grb.rs
2025-07-16 10:40:00,416 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:00,482 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\grpconv.exe
2025-07-16 10:40:00,779 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:00,786 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\h265e_64.vp
2025-07-16 10:40:01,110 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:01,404 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hadrres.dll
2025-07-16 10:40:01,689 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:01,726 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hal.dll
2025-07-16 10:40:01,996 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:02,082 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HalExtIntcLpioDMA.dll
2025-07-16 10:40:02,372 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:02,464 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HalExtIntcPseDMA.dll
2025-07-16 10:40:02,738 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:02,829 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HalExtPL080.dll
2025-07-16 10:40:03,127 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:03,140 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HandwritingSystemToastIcon.contrast-white.png
2025-07-16 10:40:03,437 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:03,451 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HandwritingSystemToastIcon.png
2025-07-16 10:40:03,715 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:03,879 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HanjaDS.dll
2025-07-16 10:40:04,173 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:04,486 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hascsp.dll
2025-07-16 10:40:04,761 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:05,253 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HashtagDS.dll
2025-07-16 10:40:05,603 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:05,797 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hbaapi.dll
2025-07-16 10:40:06,103 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:06,240 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hcproviders.dll
2025-07-16 10:40:06,514 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:07,370 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hcsdiag.exe
2025-07-16 10:40:07,661 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:07,800 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HdcpHandler.dll
2025-07-16 10:40:08,090 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:08,571 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hdwwiz.cpl
2025-07-16 10:40:08,890 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:08,979 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hdwwiz.exe
2025-07-16 10:40:09,243 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:09,256 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HeadphoneSystemToastIcon.contrast-white.png
2025-07-16 10:40:09,657 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:09,699 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HeadphoneSystemToastIcon.png
2025-07-16 10:40:10,182 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:10,207 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HeadsetSystemToastIcon.contrast-white.png
2025-07-16 10:40:10,551 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:10,573 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HeadsetSystemToastIcon.png
2025-07-16 10:40:10,902 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:10,919 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HealthSystemToastIcon.contrast-white.png
2025-07-16 10:40:11,316 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:11,331 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HealthSystemToastIcon.png
2025-07-16 10:40:11,631 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:11,645 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HearingAidSystemToastIcon.contrast-white.png
2025-07-16 10:40:11,958 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:11,972 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HearingAidSystemToastIcon.png
2025-07-16 10:40:12,266 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:12,899 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HeatCore.dll
2025-07-16 10:40:13,162 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:13,233 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\help.exe
2025-07-16 10:40:13,531 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:13,724 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HelpPaneProxy.dll
2025-07-16 10:40:13,981 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:13,989 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\he_64.vp
2025-07-16 10:40:14,264 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:14,691 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hgcpl.dll
2025-07-16 10:40:15,039 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:16,196 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hhctrl.ocx
2025-07-16 10:40:16,647 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:16,783 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hhsetup.dll
2025-07-16 10:40:17,063 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:17,283 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hid.dll
2025-07-16 10:40:17,721 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:18,103 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HidCfu.dll
2025-07-16 10:40:18,428 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:18,537 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hidphone.tsp
2025-07-16 10:40:18,833 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:18,944 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hidserv.dll
2025-07-16 10:40:19,210 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:19,426 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hlink.dll
2025-07-16 10:40:19,721 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:19,901 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hmkd.dll
2025-07-16 10:40:20,170 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:20,972 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hnetcfg.dll
2025-07-16 10:40:21,275 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:21,636 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HNetCfgClient.dll
2025-07-16 10:40:21,910 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:22,207 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hnetmon.dll
2025-07-16 10:40:22,491 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:22,848 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hnsdiag.exe
2025-07-16 10:40:23,151 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:23,212 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hnsproxy.dll
2025-07-16 10:40:23,504 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:23,560 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HOSTNAME.EXE
2025-07-16 10:40:23,877 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:30,996 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HostNetSvc.dll
2025-07-16 10:40:31,261 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:31,429 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hotpatchutil.dll
2025-07-16 10:40:31,710 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:31,870 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hotplug.dll
2025-07-16 10:40:32,158 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:32,814 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hpatchmon.dll
2025-07-16 10:40:33,118 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:33,157 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hpatchmonTask.cmd
2025-07-16 10:40:33,499 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:33,766 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HrtfApo.dll
2025-07-16 10:40:34,088 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:37,343 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HrtfDspCpu.dll
2025-07-16 10:40:37,627 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:37,761 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hspapi.dll
2025-07-16 10:40:38,059 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:38,689 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\html.iec
2025-07-16 10:40:38,977 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:39,188 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\httpapi.dll
2025-07-16 10:40:39,468 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:39,575 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\httpprxc.dll
2025-07-16 10:40:39,844 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:40,082 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\httpprxm.dll
2025-07-16 10:40:40,406 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:40,470 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\httpprxp.dll
2025-07-16 10:40:40,776 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:41,517 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HttpsDataSource.dll
2025-07-16 10:40:41,975 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:42,255 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\htui.dll
2025-07-16 10:40:42,630 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:45,593 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hvax64.exe
2025-07-16 10:40:45,858 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:40:46,066 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hvhostsvc.dll
2025-07-16 10:40:46,358 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:02,171 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hvix64.exe
2025-07-16 10:41:03,019 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:04,971 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hvloader.dll
2025-07-16 10:41:05,563 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:06,092 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\HvSocket.dll
2025-07-16 10:41:06,751 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:09,103 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\hwreqchk.dll
2025-07-16 10:41:09,606 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:09,948 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IA2ComProxy.dll
2025-07-16 10:41:10,486 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:10,970 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ias.dll
2025-07-16 10:41:11,588 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:12,039 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iasacct.dll
2025-07-16 10:41:12,556 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:13,273 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iasads.dll
2025-07-16 10:41:13,902 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:14,377 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iasdatastore.dll
2025-07-16 10:41:14,826 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:15,654 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iashlpr.dll
2025-07-16 10:41:16,087 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:17,683 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IasMigPlugin.dll
2025-07-16 10:41:18,053 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:18,666 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iasnap.dll
2025-07-16 10:41:19,035 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:19,293 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iaspolcy.dll
2025-07-16 10:41:19,769 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:20,612 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iasrad.dll
2025-07-16 10:41:21,024 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:21,406 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iasrecst.dll
2025-07-16 10:41:21,710 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:22,469 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iassam.dll
2025-07-16 10:41:22,862 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:23,654 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iassdo.dll
2025-07-16 10:41:24,008 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:24,533 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iassvcs.dll
2025-07-16 10:41:24,899 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:25,029 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icacls.exe
2025-07-16 10:41:25,328 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:25,678 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icfupgd.dll
2025-07-16 10:41:25,993 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:26,299 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icm32.dll
2025-07-16 10:41:26,666 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:26,693 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icmp.dll
2025-07-16 10:41:27,028 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:27,146 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icmui.dll
2025-07-16 10:41:27,431 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:27,477 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IconCodecService.dll
2025-07-16 10:41:27,793 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:27,949 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icsigd.dll
2025-07-16 10:41:28,269 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:28,358 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icsunattend.exe
2025-07-16 10:41:28,651 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:29,830 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icsvc.dll
2025-07-16 10:41:30,211 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:30,719 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icsvcext.dll
2025-07-16 10:41:31,148 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:32,192 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icsvcvss.dll
2025-07-16 10:41:32,833 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:38,933 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icu.dll
2025-07-16 10:41:39,322 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:39,414 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icuin.dll
2025-07-16 10:41:39,765 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:39,850 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\icuuc.dll
2025-07-16 10:41:40,216 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:40,898 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IdCtrls.dll
2025-07-16 10:41:41,260 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:41,326 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ideograf.uce
2025-07-16 10:41:41,710 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:42,096 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IDStore.dll
2025-07-16 10:41:42,507 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:43,560 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ie4uinit.exe
2025-07-16 10:41:43,968 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:44,853 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ie4ushowIE.exe
2025-07-16 10:41:45,318 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:45,789 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IEAdvpack.dll
2025-07-16 10:41:46,180 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:48,419 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ieapfltr.dll
2025-07-16 10:41:48,712 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:41:49,634 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iedkcs32.dll
2025-07-16 10:41:49,984 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:03,568 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ieframe.dll
2025-07-16 10:42:03,878 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:04,010 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iemigplugin.dll
2025-07-16 10:42:04,356 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:04,919 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iepeers.dll
2025-07-16 10:42:05,230 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:07,078 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ieproxy.dll
2025-07-16 10:42:07,421 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:07,526 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IEProxyDesktop.dll
2025-07-16 10:42:07,812 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:08,113 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iernonce.dll
2025-07-16 10:42:08,433 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:14,070 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iertutil.dll
2025-07-16 10:42:14,370 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:15,765 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IESettingSync.exe
2025-07-16 10:42:16,134 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:16,417 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iesetup.dll
2025-07-16 10:42:16,846 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:16,997 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iesysprep.dll
2025-07-16 10:42:17,378 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:18,787 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ieui.dll
2025-07-16 10:42:19,087 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:19,110 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ieuinit.inf
2025-07-16 10:42:19,417 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:19,757 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ieUnatt.exe
2025-07-16 10:42:20,101 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:20,351 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iexpress.exe
2025-07-16 10:42:20,652 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:20,740 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ifmon.dll
2025-07-16 10:42:21,027 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:21,352 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ifsutil.dll
2025-07-16 10:42:21,674 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:21,788 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ifsutilx.dll
2025-07-16 10:42:22,189 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:23,223 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IHDS.dll
2025-07-16 10:42:24,050 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:29,459 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IKEEXT.DLL
2025-07-16 10:42:30,005 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:30,251 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\imaadp32.acm
2025-07-16 10:42:30,676 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:31,093 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\imagehlp.dll
2025-07-16 10:42:31,450 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:31,483 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\imageres.dll
2025-07-16 10:42:31,886 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:31,928 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\imagesp1.dll
2025-07-16 10:42:32,612 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:33,230 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\imapi.dll
2025-07-16 10:42:33,596 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:35,874 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\imapi2.dll
2025-07-16 10:42:36,350 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:38,861 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\imapi2fs.dll
2025-07-16 10:42:39,197 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:39,407 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ime_textinputhelpers.dll
2025-07-16 10:42:39,771 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:39,955 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\imgutil.dll
2025-07-16 10:42:40,272 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:40,850 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\imm32.dll
2025-07-16 10:42:41,180 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:41,469 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\immersivetpmvscmgrsvr.exe
2025-07-16 10:42:41,815 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:42,287 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ImplatSetup.dll
2025-07-16 10:42:42,742 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:44,005 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IndexedDbLegacy.dll
2025-07-16 10:42:44,563 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:48,289 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\inetcomm.dll
2025-07-16 10:42:48,587 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:49,947 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\inetcpl.cpl
2025-07-16 10:42:50,305 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:50,475 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\inetmib1.dll
2025-07-16 10:42:50,950 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:51,467 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\inetpp.dll
2025-07-16 10:42:51,918 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:52,261 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\inetppui.dll
2025-07-16 10:42:52,783 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:53,202 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\INETRES.dll
2025-07-16 10:42:53,780 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:53,911 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InfDefaultInstall.exe
2025-07-16 10:42:54,287 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:42:54,950 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InkEd.dll
2025-07-16 10:42:55,291 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:00,295 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InkObjCore.dll
2025-07-16 10:43:01,042 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:01,938 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InprocLogger.dll
2025-07-16 10:43:02,667 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:04,796 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\input.dll
2025-07-16 10:43:05,369 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:07,393 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputCloudStore.dll
2025-07-16 10:43:07,946 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:08,445 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputController.dll
2025-07-16 10:43:08,851 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:16,524 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputHost.dll
2025-07-16 10:43:17,022 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:17,816 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputInjectionBroker.dll
2025-07-16 10:43:18,248 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:18,639 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputLocaleManager.dll
2025-07-16 10:43:19,044 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:28,013 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputService.dll
2025-07-16 10:43:28,329 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:31,010 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputSwitch.dll
2025-07-16 10:43:31,363 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:31,577 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputSwitchToastHandler.exe
2025-07-16 10:43:31,902 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:31,931 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputSystemToastIcon.contrast-white.png
2025-07-16 10:43:32,470 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:32,543 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputSystemToastIcon.png
2025-07-16 10:43:33,267 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:33,687 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InputViewExperience.dll
2025-07-16 10:43:34,079 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:34,416 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\inseng.dll
2025-07-16 10:43:34,830 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:37,291 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\installmon.dll
2025-07-16 10:43:37,646 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:42,775 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InstallService.dll
2025-07-16 10:43:43,106 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:44,057 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InstallServiceTasks.dll
2025-07-16 10:43:44,347 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:44,363 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IntegratedServicesRegionPolicySet.json
2025-07-16 10:43:44,674 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:48,938 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IntelIHVRouter08.dll
2025-07-16 10:43:49,389 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:55,049 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IntelWifiIhv08.dll
2025-07-16 10:43:55,356 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:56,039 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\intel_gfx_api-x64.dll
2025-07-16 10:43:56,476 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:56,708 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\Intel_OpenCL_ICD64.dll
2025-07-16 10:43:57,041 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:59,017 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\internetmail.dll
2025-07-16 10:43:59,389 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:43:59,609 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InternetMailCsp.dll
2025-07-16 10:44:00,132 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:00,638 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\intl.cpl
2025-07-16 10:44:01,047 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:03,363 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\invagent.dll
2025-07-16 10:44:03,937 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:06,288 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\InventorySvc.dll
2025-07-16 10:44:06,702 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:06,738 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iologmsg.dll
2025-07-16 10:44:07,119 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:07,299 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ipconfig.exe
2025-07-16 10:44:07,600 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:07,987 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IPHLPAPI.DLL
2025-07-16 10:44:08,307 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:09,790 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iphlpsvc.dll
2025-07-16 10:44:10,217 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:11,827 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ipnathlp.dll
2025-07-16 10:44:12,248 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:12,424 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IpNatHlpClient.dll
2025-07-16 10:44:12,802 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:13,652 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IppCommon.dll
2025-07-16 10:44:13,993 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:14,067 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IppCommonProxy.dll
2025-07-16 10:44:14,369 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:14,439 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iprtprio.dll
2025-07-16 10:44:14,812 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:15,977 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iprtrmgr.dll
2025-07-16 10:44:16,333 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:17,841 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ipsecsnp.dll
2025-07-16 10:44:18,376 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:20,061 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IPSECSVC.DLL
2025-07-16 10:44:20,433 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:22,394 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ipsmsnap.dll
2025-07-16 10:44:22,786 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:23,022 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ipxlatcfg.dll
2025-07-16 10:44:23,405 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:23,605 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iri.dll
2025-07-16 10:44:23,990 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:24,144 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\irprops.cpl
2025-07-16 10:44:24,521 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:24,722 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iscsicli.exe
2025-07-16 10:44:25,077 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:25,487 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iscsicpl.dll
2025-07-16 10:44:25,869 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:26,005 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iscsicpl.exe
2025-07-16 10:44:26,709 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:27,056 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iscsidsc.dll
2025-07-16 10:44:27,467 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:27,576 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iscsied.dll
2025-07-16 10:44:27,977 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:28,554 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iscsiexe.dll
2025-07-16 10:44:28,969 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:29,043 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iscsilog.dll
2025-07-16 10:44:29,532 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:29,744 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iscsium.dll
2025-07-16 10:44:30,060 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:30,220 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iscsiwmi.dll
2025-07-16 10:44:30,532 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:31,008 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iscsiwmiv2.dll
2025-07-16 10:44:31,350 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:37,376 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ISM.dll
2025-07-16 10:44:37,709 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:37,847 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\ISM.exe
2025-07-16 10:44:38,128 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:38,306 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\isoburn.exe
2025-07-16 10:44:38,828 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:39,195 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\itircl.dll
2025-07-16 10:44:39,525 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:39,939 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\itss.dll
2025-07-16 10:44:40,216 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:40,364 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iuilp.dll
2025-07-16 10:44:40,711 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:40,780 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iumbase.dll
2025-07-16 10:44:41,098 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:41,241 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iumcrypt.dll
2025-07-16 10:44:41,515 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:41,574 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iumdll.dll
2025-07-16 10:44:41,876 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:41,927 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\IumSdk.dll
2025-07-16 10:44:42,236 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:42,324 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\iyuv_32.dll
2025-07-16 10:44:42,618 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:42,842 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\JavaScriptCollectionAgent.dll
2025-07-16 10:44:43,135 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:43,263 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\jdns_sd.dll
2025-07-16 10:44:43,567 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:43,713 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\joinproviderol.dll
2025-07-16 10:44:44,008 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:44,166 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\joinutil.dll
2025-07-16 10:44:44,453 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:44,584 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\joy.cpl
2025-07-16 10:44:44,903 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:46,304 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\JpMapControl.dll
2025-07-16 10:44:46,603 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:47,090 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\jpndecoder.dll
2025-07-16 10:44:47,395 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:47,750 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\jpninputrouter.dll
2025-07-16 10:44:48,041 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:48,383 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\jpnranker.dll
2025-07-16 10:44:48,684 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:49,181 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\JpnServiceDS.dll
2025-07-16 10:44:49,501 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:51,114 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\jscript.dll
2025-07-16 10:44:51,406 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:44:59,994 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\jscript9.dll
2025-07-16 10:45:00,288 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:01,969 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\jscript9diag.dll
2025-07-16 10:45:02,272 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:10,278 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\jscript9Legacy.dll
2025-07-16 10:45:10,578 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:10,623 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\jsproxy.dll
2025-07-16 10:45:10,918 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:10,943 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kanji_1.uce
2025-07-16 10:45:11,265 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:11,287 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kanji_2.uce
2025-07-16 10:45:11,609 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:11,675 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbd101.dll
2025-07-16 10:45:11,956 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:12,021 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbd101a.dll
2025-07-16 10:45:12,312 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:12,365 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbd101b.dll
2025-07-16 10:45:12,646 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:12,699 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbd101c.dll
2025-07-16 10:45:13,010 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:13,069 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbd103.dll
2025-07-16 10:45:13,372 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:13,427 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbd106.dll
2025-07-16 10:45:13,788 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:13,937 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbd106n.dll
2025-07-16 10:45:14,721 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:14,948 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDA1.DLL
2025-07-16 10:45:15,397 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:15,469 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDA2.DLL
2025-07-16 10:45:15,824 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:15,893 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDA3.DLL
2025-07-16 10:45:16,189 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:16,253 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDADLM.DLL
2025-07-16 10:45:16,668 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:16,733 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDAL.DLL
2025-07-16 10:45:17,102 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:17,149 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDARME.DLL
2025-07-16 10:45:17,483 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:17,534 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdarmph.dll
2025-07-16 10:45:17,802 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:17,850 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdarmty.dll
2025-07-16 10:45:18,161 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:18,206 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDARMW.DLL
2025-07-16 10:45:18,479 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:18,535 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdax2.dll
2025-07-16 10:45:18,840 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:18,891 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDAZE.DLL
2025-07-16 10:45:19,226 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:19,295 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDAZEL.DLL
2025-07-16 10:45:19,563 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:19,619 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDAZST.DLL
2025-07-16 10:45:19,942 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:19,994 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBASH.DLL
2025-07-16 10:45:20,297 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:20,356 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBE.DLL
2025-07-16 10:45:20,621 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:20,685 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBENE.DLL
2025-07-16 10:45:20,983 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:21,040 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBGPH.DLL
2025-07-16 10:45:21,346 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:21,399 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBGPH1.DLL
2025-07-16 10:45:21,693 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:21,753 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBHC.DLL
2025-07-16 10:45:22,057 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:22,104 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBLR.DLL
2025-07-16 10:45:22,423 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:22,480 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBR.DLL
2025-07-16 10:45:22,762 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:22,819 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBU.DLL
2025-07-16 10:45:23,115 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:23,181 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBUG.DLL
2025-07-16 10:45:23,481 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:23,538 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDBULG.DLL
2025-07-16 10:45:23,810 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:23,862 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDCA.DLL
2025-07-16 10:45:24,134 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:24,186 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDCAN.DLL
2025-07-16 10:45:24,504 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:24,576 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDCHER.DLL
2025-07-16 10:45:24,843 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:24,911 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDCHERP.DLL
2025-07-16 10:45:25,210 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:25,262 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDCMK.DLL
2025-07-16 10:45:25,597 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:25,658 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDCR.DLL
2025-07-16 10:45:25,924 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:25,985 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDCZ.DLL
2025-07-16 10:45:26,299 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:26,358 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDCZ1.DLL
2025-07-16 10:45:26,669 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:26,731 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDCZ2.DLL
2025-07-16 10:45:27,044 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:27,099 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDDA.DLL
2025-07-16 10:45:27,396 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:27,453 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDDIV1.DLL
2025-07-16 10:45:27,711 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:27,762 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDDIV2.DLL
2025-07-16 10:45:28,040 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:28,092 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDDV.DLL
2025-07-16 10:45:28,387 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:28,439 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDDZO.DLL
2025-07-16 10:45:28,760 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:28,830 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDES.DLL
2025-07-16 10:45:29,131 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:29,187 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDEST.DLL
2025-07-16 10:45:29,460 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:29,515 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDFA.DLL
2025-07-16 10:45:29,804 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:29,861 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdfar.dll
2025-07-16 10:45:30,142 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:30,198 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDFC.DLL
2025-07-16 10:45:30,478 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:30,535 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDFI.DLL
2025-07-16 10:45:30,827 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:30,883 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDFI1.DLL
2025-07-16 10:45:31,183 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:31,242 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDFO.DLL
2025-07-16 10:45:31,537 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:31,598 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDFR.DLL
2025-07-16 10:45:31,878 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:31,930 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDFRNA.DLL
2025-07-16 10:45:32,222 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:32,274 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDFRNB.DLL
2025-07-16 10:45:32,569 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:32,621 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDFTHRK.DLL
2025-07-16 10:45:32,933 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:33,157 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDGAE.DLL
2025-07-16 10:45:33,554 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:33,610 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDGEO.DLL
2025-07-16 10:45:33,898 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:33,955 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdgeoer.dll
2025-07-16 10:45:34,275 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:34,331 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdgeome.dll
2025-07-16 10:45:34,625 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:34,682 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdgeooa.dll
2025-07-16 10:45:34,965 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:35,019 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdgeoqw.dll
2025-07-16 10:45:35,332 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:35,403 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDGKL.DLL
2025-07-16 10:45:35,692 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:35,740 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDGN.DLL
2025-07-16 10:45:36,012 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:36,066 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDGR.DLL
2025-07-16 10:45:36,374 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:36,430 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDGR1.DLL
2025-07-16 10:45:36,731 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:36,786 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDGRE1.DLL
2025-07-16 10:45:37,142 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:37,221 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDGRE2.DLL
2025-07-16 10:45:37,515 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:37,572 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDGRLND.DLL
2025-07-16 10:45:37,874 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:37,928 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDGTHC.DLL
2025-07-16 10:45:38,240 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:38,294 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHAU.DLL
2025-07-16 10:45:38,605 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:38,661 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHAW.DLL
2025-07-16 10:45:38,958 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:39,018 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHE.DLL
2025-07-16 10:45:39,329 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:39,386 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHE220.DLL
2025-07-16 10:45:39,662 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:39,708 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHE319.DLL
2025-07-16 10:45:39,971 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:40,033 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHEB.DLL
2025-07-16 10:45:40,374 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:40,454 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdhebl3.dll
2025-07-16 10:45:40,769 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:40,833 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdhebsi.dll
2025-07-16 10:45:41,255 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:41,369 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHELA2.DLL
2025-07-16 10:45:41,976 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:42,117 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHELA3.DLL
2025-07-16 10:45:42,584 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:42,773 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHEPT.DLL
2025-07-16 10:45:43,318 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:43,426 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHU.DLL
2025-07-16 10:45:43,842 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:43,956 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDHU1.DLL
2025-07-16 10:45:44,368 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:44,484 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\kbdibm02.dll
2025-07-16 10:45:44,972 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:45,124 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDIBO.DLL
2025-07-16 10:45:45,698 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:45,918 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDIC.DLL
2025-07-16 10:45:46,554 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:46,677 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINASA.DLL
2025-07-16 10:45:47,194 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:47,324 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINBE1.DLL
2025-07-16 10:45:48,031 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:48,203 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINBE2.DLL
2025-07-16 10:45:48,677 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:48,772 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINBEN.DLL
2025-07-16 10:45:49,204 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:49,316 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINDEV.DLL
2025-07-16 10:45:49,786 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:49,934 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINEN.DLL
2025-07-16 10:45:50,360 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:50,465 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINGUJ.DLL
2025-07-16 10:45:50,990 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:51,115 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINHIN.DLL
2025-07-16 10:45:51,657 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:51,896 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINKAN.DLL
2025-07-16 10:45:52,537 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:52,703 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINMAL.DLL
2025-07-16 10:45:53,495 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:53,712 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINMAR.DLL
2025-07-16 10:45:54,166 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:54,293 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINORI.DLL
2025-07-16 10:45:54,787 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:54,898 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINPUN.DLL
2025-07-16 10:45:55,363 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:55,520 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINTAM.DLL
2025-07-16 10:45:56,053 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:56,279 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINTEL.DLL
2025-07-16 10:45:57,134 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:57,384 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINUK2.DLL
2025-07-16 10:45:57,919 - src.analysis.static_analyzer - [32mINFO[0m - Loaded 1 YARA rules
2025-07-16 10:45:57,994 - src.analysis.static_analyzer - [32mINFO[0m - Static analysis completed for: C:\Windows\System32\KBDINUK3.DLL
