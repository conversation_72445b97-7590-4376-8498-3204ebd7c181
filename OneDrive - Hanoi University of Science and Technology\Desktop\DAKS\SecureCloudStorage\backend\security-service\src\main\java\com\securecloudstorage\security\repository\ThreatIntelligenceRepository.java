package com.securecloudstorage.security.repository;

import com.securecloudstorage.security.entity.ThreatIntelligence;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Threat Intelligence Repository
 * Repository cho ThreatIntelligence entity
 */
@Repository
public interface ThreatIntelligenceRepository extends JpaRepository<ThreatIntelligence, Long> {

    // Find methods
    ThreatIntelligence findByIpAddress(String ipAddress);
    
    ThreatIntelligence findByDomain(String domain);
    
    ThreatIntelligence findByHashValue(String hashValue);
    
    List<ThreatIntelligence> findByThreatType(String threatType);
    
    List<ThreatIntelligence> findBySeverity(String severity);
    
    List<ThreatIntelligence> findBySource(String source);
    
    List<ThreatIntelligence> findByIsActiveTrue();
    
    List<ThreatIntelligence> findByIsActiveFalse();
    
    List<ThreatIntelligence> findByCountry(String country);
    
    List<ThreatIntelligence> findByOrganization(String organization);
    
    List<ThreatIntelligence> findByAttackVector(String attackVector);
    
    List<ThreatIntelligence> findByThreatTypeAndIsActiveTrue(String threatType);
    
    List<ThreatIntelligence> findBySeverityAndIsActiveTrue(String severity);
    
    List<ThreatIntelligence> findByLastSeenAfter(LocalDateTime timestamp);

    // Exists methods
    boolean existsByIpAddress(String ipAddress);
    
    boolean existsByIpAddressAndIsActiveTrue(String ipAddress);
    
    boolean existsByDomain(String domain);
    
    boolean existsByHashValue(String hashValue);

    // Count methods
    long countByThreatType(String threatType);
    
    long countBySeverity(String severity);
    
    long countBySource(String source);
    
    long countByIsActiveTrue();
    
    long countByIsActiveFalse();
    
    long countByCountry(String country);
    
    long countByLastSeenAfter(LocalDateTime timestamp);

    // Custom queries
    @Query("SELECT t FROM ThreatIntelligence t WHERE t.isActive = true AND (t.ttl IS NULL OR t.updatedAt > :ttlCheck)")
    List<ThreatIntelligence> findActiveNonExpiredThreats(@Param("ttlCheck") LocalDateTime ttlCheck);
    
    @Query("SELECT t FROM ThreatIntelligence t WHERE t.isActive = true AND t.ttl IS NOT NULL AND t.updatedAt <= :ttlCheck")
    List<ThreatIntelligence> findExpiredThreats(@Param("ttlCheck") LocalDateTime ttlCheck);
    
    @Query("SELECT t.threatType, COUNT(t) FROM ThreatIntelligence t WHERE t.isActive = true GROUP BY t.threatType")
    List<Object[]> getActiveThreatsByType();
    
    @Query("SELECT t.severity, COUNT(t) FROM ThreatIntelligence t WHERE t.isActive = true GROUP BY t.severity")
    List<Object[]> getActiveThreatsBySeverity();
    
    @Query("SELECT t.source, COUNT(t) FROM ThreatIntelligence t WHERE t.isActive = true GROUP BY t.source")
    List<Object[]> getActiveThreatsBySource();
    
    @Query("SELECT t.country, COUNT(t) FROM ThreatIntelligence t WHERE t.isActive = true AND t.country IS NOT NULL GROUP BY t.country ORDER BY COUNT(t) DESC")
    List<Object[]> getActiveThreatsByCountry();
    
    @Query("SELECT DATE(t.lastSeen) as date, COUNT(t) as count FROM ThreatIntelligence t WHERE t.lastSeen > :timestamp GROUP BY DATE(t.lastSeen) ORDER BY date DESC")
    List<Object[]> getDailyThreatCounts(@Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT t FROM ThreatIntelligence t WHERE t.isActive = true AND t.severity = 'CRITICAL' ORDER BY t.lastSeen DESC")
    List<ThreatIntelligence> findCriticalActiveThreats();
    
    @Query("SELECT t FROM ThreatIntelligence t WHERE t.isActive = true AND t.confidence >= :minConfidence ORDER BY t.confidence DESC")
    List<ThreatIntelligence> findHighConfidenceThreats(@Param("minConfidence") Integer minConfidence);
    
    @Query("SELECT t FROM ThreatIntelligence t WHERE t.isActive = true AND t.reputationScore <= :maxScore ORDER BY t.reputationScore ASC")
    List<ThreatIntelligence> findLowReputationThreats(@Param("maxScore") Integer maxScore);
    
    @Query("SELECT t FROM ThreatIntelligence t WHERE t.isActive = true AND t.firstSeen > :timestamp ORDER BY t.firstSeen DESC")
    List<ThreatIntelligence> findRecentThreats(@Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT t FROM ThreatIntelligence t WHERE t.isActive = true AND t.lastSeen < :timestamp ORDER BY t.lastSeen ASC")
    List<ThreatIntelligence> findStaleThreats(@Param("timestamp") LocalDateTime timestamp);
    
    @Query("SELECT t FROM ThreatIntelligence t WHERE t.ipAddress LIKE :ipPattern")
    List<ThreatIntelligence> findByIpPattern(@Param("ipPattern") String ipPattern);
    
    @Query("SELECT t FROM ThreatIntelligence t WHERE t.domain LIKE :domainPattern")
    List<ThreatIntelligence> findByDomainPattern(@Param("domainPattern") String domainPattern);
    
    @Query("SELECT COUNT(t) FROM ThreatIntelligence t WHERE t.isActive = true AND t.threatType = :threatType AND t.lastSeen > :timestamp")
    long countRecentThreatsByType(@Param("threatType") String threatType, @Param("timestamp") LocalDateTime timestamp);
}
