"""
Packet Capture Module for Network IDS
====================================

Module capture gói tin mạng với nhiều tính năng nâng cao
"""

import os
import sys
import time
import threading
import queue
from typing import Dict, Any, Optional, Callable, List
import logging
from pathlib import Path

try:
    from scapy.all import sniff, get_if_list, conf, get_if_hwaddr
    from scapy.layers.inet import IP, TCP, UDP, ICMP
    from scapy.layers.inet6 import IPv6
    from scapy.layers.l2 import Ether, ARP
    from scapy.layers.dns import DNS
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False
    print("Warning: Scapy not available. Some features may be limited.")

class PacketCapture:
    """Lớp capture gói tin mạng"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Khởi tạo packet capture
        
        Args:
            config (Dict): Cấu hình capture
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.PacketCapture")
        
        # Capture settings
        self.interface = self._get_interface()
        self.filter_string = config.get('filter', '')
        self.promiscuous = config.get('promiscuous', True)
        self.timeout = config.get('timeout', 1000)
        self.buffer_size = config.get('buffer_size', 65536)
        self.max_packets = config.get('max_packets', 0)
        
        # Runtime state
        self.is_capturing = False
        self.packet_count = 0
        self.capture_thread = None
        self.packet_queue = queue.Queue(maxsize=10000)
        self.callback_function = None
        
        # Statistics
        self.stats = {
            'total_packets': 0,
            'tcp_packets': 0,
            'udp_packets': 0,
            'icmp_packets': 0,
            'arp_packets': 0,
            'dns_packets': 0,
            'malformed_packets': 0,
            'dropped_packets': 0,
            'bytes_captured': 0,
            'capture_duration': 0,
            'start_time': None
        }
        
        # Validate setup
        if not SCAPY_AVAILABLE:
            self.logger.error("Scapy not available. Packet capture disabled.")
            return
        
        self.logger.info(f"PacketCapture initialized on interface: {self.interface}")
    
    def _get_interface(self) -> str:
        """
        Lấy network interface để capture
        
        Returns:
            str: Interface name
        """
        interface = self.config.get('interface', 'auto')
        
        if interface == 'auto':
            # Tự động chọn interface
            if SCAPY_AVAILABLE:
                try:
                    interfaces = get_if_list()
                    # Loại bỏ loopback
                    interfaces = [iface for iface in interfaces if not iface.startswith('lo')]
                    if interfaces:
                        interface = interfaces[0]
                        self.logger.info(f"Auto-selected interface: {interface}")
                    else:
                        interface = 'any'
                except Exception as e:
                    self.logger.error(f"Error auto-selecting interface: {str(e)}")
                    interface = 'any'
            else:
                interface = 'any'
        
        return interface
    
    def get_interface_info(self) -> Dict[str, Any]:
        """
        Lấy thông tin về interface
        
        Returns:
            Dict: Thông tin interface
        """
        info = {
            'name': self.interface,
            'available': False,
            'mac_address': None,
            'ip_addresses': []
        }
        
        if not SCAPY_AVAILABLE:
            return info
        
        try:
            # Kiểm tra interface có khả dụng không
            available_interfaces = get_if_list()
            info['available'] = self.interface in available_interfaces or self.interface == 'any'
            
            if info['available'] and self.interface != 'any':
                # Lấy MAC address
                try:
                    info['mac_address'] = get_if_hwaddr(self.interface)
                except:
                    pass
                
                # Lấy IP addresses (cần implement thêm)
                # info['ip_addresses'] = get_if_addr(self.interface)
        
        except Exception as e:
            self.logger.error(f"Error getting interface info: {str(e)}")
        
        return info
    
    def start_capture(self, callback: Callable = None) -> bool:
        """
        Bắt đầu capture gói tin
        
        Args:
            callback (Callable): Hàm callback xử lý gói tin
            
        Returns:
            bool: True nếu thành công
        """
        if not SCAPY_AVAILABLE:
            self.logger.error("Cannot start capture: Scapy not available")
            return False
        
        if self.is_capturing:
            self.logger.warning("Capture already running")
            return False
        
        self.callback_function = callback
        self.is_capturing = True
        self.packet_count = 0
        self.stats['start_time'] = time.time()
        
        # Khởi tạo capture thread
        self.capture_thread = threading.Thread(
            target=self._capture_worker,
            name="PacketCaptureWorker"
        )
        self.capture_thread.daemon = True
        self.capture_thread.start()
        
        self.logger.info(f"Packet capture started on {self.interface}")
        return True
    
    def _capture_worker(self) -> None:
        """Worker thread cho packet capture"""
        try:
            # Scapy capture parameters
            capture_params = {
                'iface': self.interface if self.interface != 'any' else None,
                'prn': self._packet_handler,
                'filter': self.filter_string if self.filter_string else None,
                'store': False,  # Không lưu packets trong memory
                'timeout': self.timeout / 1000,  # Convert to seconds
                'stop_filter': lambda p: not self.is_capturing
            }
            
            # Thêm count nếu có giới hạn
            if self.max_packets > 0:
                capture_params['count'] = self.max_packets
            
            # Bắt đầu capture
            self.logger.info(f"Starting packet capture with params: {capture_params}")
            sniff(**capture_params)
            
        except Exception as e:
            self.logger.error(f"Capture worker error: {str(e)}")
        finally:
            self.is_capturing = False
            self.logger.info("Packet capture stopped")
    
    def _packet_handler(self, packet) -> None:
        """
        Xử lý gói tin được capture
        
        Args:
            packet: Gói tin từ Scapy
        """
        try:
            # Update statistics
            self.packet_count += 1
            self.stats['total_packets'] += 1
            
            # Classify packet
            self._classify_packet(packet)
            
            # Add to queue
            if not self.packet_queue.full():
                self.packet_queue.put(packet)
            else:
                self.stats['dropped_packets'] += 1
            
            # Call callback if provided
            if self.callback_function:
                self.callback_function(packet)
            
            # Check limits
            if self.max_packets > 0 and self.packet_count >= self.max_packets:
                self.stop_capture()
            
        except Exception as e:
            self.logger.error(f"Error handling packet: {str(e)}")
            self.stats['malformed_packets'] += 1
    
    def _classify_packet(self, packet) -> None:
        """
        Phân loại gói tin và cập nhật thống kê
        
        Args:
            packet: Gói tin
        """
        try:
            # Packet size
            packet_size = len(packet)
            self.stats['bytes_captured'] += packet_size
            
            # Protocol classification
            if packet.haslayer(TCP):
                self.stats['tcp_packets'] += 1
            elif packet.haslayer(UDP):
                self.stats['udp_packets'] += 1
            elif packet.haslayer(ICMP):
                self.stats['icmp_packets'] += 1
            elif packet.haslayer(ARP):
                self.stats['arp_packets'] += 1
            
            # DNS packets
            if packet.haslayer(DNS):
                self.stats['dns_packets'] += 1
            
        except Exception as e:
            self.logger.error(f"Error classifying packet: {str(e)}")
    
    def stop_capture(self) -> bool:
        """
        Dừng capture gói tin
        
        Returns:
            bool: True nếu thành công
        """
        if not self.is_capturing:
            self.logger.warning("Capture not running")
            return False
        
        self.is_capturing = False
        
        # Đợi capture thread kết thúc
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=5)
        
        # Update statistics
        if self.stats['start_time']:
            self.stats['capture_duration'] = time.time() - self.stats['start_time']
        
        self.logger.info(f"Packet capture stopped. Captured {self.packet_count} packets")
        return True
    
    def get_packet_from_queue(self, timeout: float = 1.0):
        """
        Lấy gói tin từ queue
        
        Args:
            timeout (float): Timeout in seconds
            
        Returns:
            Packet hoặc None nếu timeout
        """
        try:
            return self.packet_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Lấy thống kê capture
        
        Returns:
            Dict: Thống kê chi tiết
        """
        stats = self.stats.copy()
        
        # Tính toán thêm
        if stats['capture_duration'] > 0:
            stats['packets_per_second'] = stats['total_packets'] / stats['capture_duration']
            stats['bytes_per_second'] = stats['bytes_captured'] / stats['capture_duration']
        else:
            stats['packets_per_second'] = 0
            stats['bytes_per_second'] = 0
        
        # Tỷ lệ packet types
        total = stats['total_packets']
        if total > 0:
            stats['tcp_percentage'] = (stats['tcp_packets'] / total) * 100
            stats['udp_percentage'] = (stats['udp_packets'] / total) * 100
            stats['icmp_percentage'] = (stats['icmp_packets'] / total) * 100
            stats['arp_percentage'] = (stats['arp_packets'] / total) * 100
            stats['dns_percentage'] = (stats['dns_packets'] / total) * 100
        
        return stats
    
    def save_capture_to_file(self, filename: str, packet_list: List = None) -> bool:
        """
        Lưu capture vào file PCAP
        
        Args:
            filename (str): Tên file
            packet_list (List): Danh sách packets (nếu None thì lấy từ queue)
            
        Returns:
            bool: True nếu thành công
        """
        if not SCAPY_AVAILABLE:
            self.logger.error("Cannot save capture: Scapy not available")
            return False
        
        try:
            from scapy.utils import wrpcap
            
            if packet_list is None:
                # Lấy packets từ queue
                packet_list = []
                while not self.packet_queue.empty():
                    try:
                        packet = self.packet_queue.get_nowait()
                        packet_list.append(packet)
                    except queue.Empty:
                        break
            
            if not packet_list:
                self.logger.warning("No packets to save")
                return False
            
            # Tạo directory nếu cần
            Path(filename).parent.mkdir(parents=True, exist_ok=True)
            
            # Lưu file
            wrpcap(filename, packet_list)
            self.logger.info(f"Saved {len(packet_list)} packets to {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving capture: {str(e)}")
            return False
    
    def load_pcap_file(self, filename: str) -> List:
        """
        Đọc file PCAP
        
        Args:
            filename (str): Tên file PCAP
            
        Returns:
            List: Danh sách packets
        """
        if not SCAPY_AVAILABLE:
            self.logger.error("Cannot load PCAP: Scapy not available")
            return []
        
        try:
            from scapy.utils import rdpcap
            
            packets = rdpcap(filename)
            self.logger.info(f"Loaded {len(packets)} packets from {filename}")
            return packets
            
        except Exception as e:
            self.logger.error(f"Error loading PCAP: {str(e)}")
            return []
    
    def apply_filter(self, filter_string: str) -> bool:
        """
        Áp dụng BPF filter
        
        Args:
            filter_string (str): BPF filter string
            
        Returns:
            bool: True nếu thành công
        """
        self.filter_string = filter_string
        self.logger.info(f"Applied filter: {filter_string}")
        return True
    
    def get_supported_protocols(self) -> List[str]:
        """
        Lấy danh sách protocols được hỗ trợ
        
        Returns:
            List: Danh sách protocols
        """
        return ['TCP', 'UDP', 'ICMP', 'ARP', 'DNS', 'HTTP', 'HTTPS', 'FTP', 'SSH']
    
    def is_admin(self) -> bool:
        """
        Kiểm tra quyền admin (cần thiết cho packet capture)
        
        Returns:
            bool: True nếu có quyền admin
        """
        if sys.platform == 'win32':
            import ctypes
            try:
                return ctypes.windll.shell32.IsUserAnAdmin()
            except:
                return False
        else:
            return os.geteuid() == 0
    
    def __del__(self):
        """Destructor"""
        if self.is_capturing:
            self.stop_capture()
