package com.securecloudstorage.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;

/**
 * Secure Cloud Storage API Gateway
 * Tích hợp Network IDS và AI-Malware Detection
 */
@SpringBootApplication
public class ApiGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(ApiGatewayApplication.class, args);
    }

    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            // Storage Service Routes
            .route("storage-service", r -> r.path("/api/storage/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .circuitBreaker(config -> config.setName("storage-service-cb"))
                    .requestRateLimiter(config -> config
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(userKeyResolver())
                    )
                )
                .uri("http://localhost:8082"))
            
            // User Service Routes
            .route("user-service", r -> r.path("/api/users/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .circuitBreaker(config -> config.setName("user-service-cb"))
                )
                .uri("http://localhost:8083"))
            
            // Security Service Routes
            .route("security-service", r -> r.path("/api/security/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .circuitBreaker(config -> config.setName("security-service-cb"))
                )
                .uri("http://localhost:8084"))
            
            // Monitoring Service Routes
            .route("monitoring-service", r -> r.path("/api/monitoring/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .circuitBreaker(config -> config.setName("monitoring-service-cb"))
                )
                .uri("http://localhost:8085"))
            
            // Network IDS Integration
            .route("network-ids", r -> r.path("/api/ids/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .circuitBreaker(config -> config.setName("network-ids-cb"))
                )
                .uri("http://localhost:5000"))
            
            // AI Malware Detection Integration
            .route("ai-malware", r -> r.path("/api/malware/**")
                .filters(f -> f
                    .stripPrefix(2)
                    .circuitBreaker(config -> config.setName("ai-malware-cb"))
                )
                .uri("http://localhost:8086"))
            
            .build();
    }

    @Bean
    public org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter redisRateLimiter() {
        return new org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter(10, 20);
    }

    @Bean
    public org.springframework.cloud.gateway.filter.ratelimit.KeyResolver userKeyResolver() {
        return exchange -> exchange.getRequest().getHeaders().getFirst("X-User-ID") != null
            ? reactor.core.publisher.Mono.just(exchange.getRequest().getHeaders().getFirst("X-User-ID"))
            : reactor.core.publisher.Mono.just("anonymous");
    }
}
