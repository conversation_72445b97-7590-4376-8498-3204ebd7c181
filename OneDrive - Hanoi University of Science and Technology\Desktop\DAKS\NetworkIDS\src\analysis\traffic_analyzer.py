"""
Traffic Analysis Module for Network IDS
======================================

Module phân tích traffic mạng với feature extraction và pattern recognition
"""

import time
import threading
import queue
import hashlib
import json
import statistics
from collections import defaultdict, deque
from typing import Dict, Any, List, Optional, Tuple
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

try:
    from scapy.all import *
    from scapy.layers.inet import IP, TCP, UDP, ICMP
    from scapy.layers.inet6 import IPv6
    from scapy.layers.l2 import Ether, ARP
    from scapy.layers.dns import DNS
    from scapy.layers.http import HTTP
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False

class FlowManager:
    """Quản lý network flows"""
    
    def __init__(self, timeout: int = 300):
        """
        Khởi tạo flow manager
        
        Args:
            timeout (int): Timeout cho flows (seconds)
        """
        self.flows = {}
        self.timeout = timeout
        self.lock = threading.Lock()
    
    def get_flow_id(self, packet) -> str:
        """
        Tạo flow ID từ packet
        
        Args:
            packet: Gói tin
            
        Returns:
            str: Flow ID
        """
        try:
            if packet.haslayer(IP):
                src_ip = packet[IP].src
                dst_ip = packet[IP].dst
                
                if packet.haslayer(TCP):
                    src_port = packet[TCP].sport
                    dst_port = packet[TCP].dport
                    protocol = 'TCP'
                elif packet.haslayer(UDP):
                    src_port = packet[UDP].sport
                    dst_port = packet[UDP].dport
                    protocol = 'UDP'
                else:
                    src_port = dst_port = 0
                    protocol = 'OTHER'
                
                # Tạo flow ID chuẩn hóa (src < dst)
                if (src_ip, src_port) < (dst_ip, dst_port):
                    flow_id = f"{src_ip}:{src_port}-{dst_ip}:{dst_port}-{protocol}"
                else:
                    flow_id = f"{dst_ip}:{dst_port}-{src_ip}:{src_port}-{protocol}"
                
                return flow_id
        except:
            pass
        
        return "unknown"
    
    def update_flow(self, packet) -> Dict[str, Any]:
        """
        Cập nhật flow với packet mới
        
        Args:
            packet: Gói tin
            
        Returns:
            Dict: Thông tin flow
        """
        flow_id = self.get_flow_id(packet)
        current_time = time.time()
        
        with self.lock:
            if flow_id not in self.flows:
                self.flows[flow_id] = {
                    'id': flow_id,
                    'start_time': current_time,
                    'last_seen': current_time,
                    'packet_count': 0,
                    'byte_count': 0,
                    'src_packets': 0,
                    'dst_packets': 0,
                    'src_bytes': 0,
                    'dst_bytes': 0,
                    'protocols': set(),
                    'ports': set(),
                    'flags': set(),
                    'packet_sizes': [],
                    'inter_arrival_times': [],
                    'is_active': True
                }
            
            flow = self.flows[flow_id]
            
            # Cập nhật flow
            flow['last_seen'] = current_time
            flow['packet_count'] += 1
            flow['byte_count'] += len(packet)
            
            # Thêm packet size
            flow['packet_sizes'].append(len(packet))
            
            # Tính inter-arrival time
            if len(flow['packet_sizes']) > 1:
                flow['inter_arrival_times'].append(current_time - flow['start_time'])
            
            # Phân tích direction
            if packet.haslayer(IP):
                # Determine direction based on source
                # This is simplified - in reality you'd need more sophisticated logic
                flow['src_packets'] += 1
                flow['src_bytes'] += len(packet)
                
                # Collect protocol info
                if packet.haslayer(TCP):
                    flow['protocols'].add('TCP')
                    flow['ports'].add(packet[TCP].sport)
                    flow['ports'].add(packet[TCP].dport)
                    if packet[TCP].flags:
                        flow['flags'].add(packet[TCP].flags)
                elif packet.haslayer(UDP):
                    flow['protocols'].add('UDP')
                    flow['ports'].add(packet[UDP].sport)
                    flow['ports'].add(packet[UDP].dport)
            
            return flow
    
    def get_active_flows(self) -> List[Dict[str, Any]]:
        """
        Lấy danh sách active flows
        
        Returns:
            List: Danh sách flows
        """
        current_time = time.time()
        active_flows = []
        
        with self.lock:
            for flow_id, flow in self.flows.items():
                if current_time - flow['last_seen'] < self.timeout:
                    active_flows.append(flow.copy())
                else:
                    flow['is_active'] = False
        
        return active_flows
    
    def cleanup_old_flows(self) -> int:
        """
        Xóa flows cũ
        
        Returns:
            int: Số lượng flows đã xóa
        """
        current_time = time.time()
        removed_count = 0
        
        with self.lock:
            flow_ids_to_remove = []
            
            for flow_id, flow in self.flows.items():
                if current_time - flow['last_seen'] > self.timeout:
                    flow_ids_to_remove.append(flow_id)
            
            for flow_id in flow_ids_to_remove:
                del self.flows[flow_id]
                removed_count += 1
        
        return removed_count

class TrafficAnalyzer:
    """Lớp phân tích traffic mạng"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Khởi tạo traffic analyzer
        
        Args:
            config (Dict): Cấu hình analyzer
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.TrafficAnalyzer")
        
        # Configuration
        self.window_size = config.get('window_size', 60)
        self.features = config.get('features', [])
        self.aggregation_interval = config.get('aggregation_interval', 10)
        self.baseline_period = config.get('baseline_period', 300)
        
        # Flow manager
        self.flow_manager = FlowManager(timeout=self.window_size * 2)
        
        # Data structures
        self.packet_queue = queue.Queue(maxsize=10000)
        self.analysis_results = queue.Queue(maxsize=1000)
        self.time_series_data = defaultdict(deque)
        self.baseline_stats = {}
        
        # Runtime state
        self.is_running = False
        self.worker_thread = None
        self.baseline_established = False
        
        # Statistics
        self.stats = {
            'packets_analyzed': 0,
            'flows_created': 0,
            'features_extracted': 0,
            'anomalies_detected': 0,
            'analysis_errors': 0,
            'processing_time': 0
        }
        
        self.logger.info("TrafficAnalyzer initialized")
    
    def start(self):
        """Bắt đầu analysis"""
        if self.is_running:
            return
        
        self.is_running = True
        self.worker_thread = threading.Thread(target=self._analysis_worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
        
        self.logger.info("Traffic analysis started")
    
    def stop(self):
        """Dừng analysis"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        
        self.logger.info("Traffic analysis stopped")
    
    def _analysis_worker(self):
        """Worker thread cho analysis"""
        while self.is_running:
            try:
                # Process packets from queue
                self.process_queue()
                
                # Cleanup old flows
                if self.stats['packets_analyzed'] % 1000 == 0:
                    removed = self.flow_manager.cleanup_old_flows()
                    if removed > 0:
                        self.logger.debug(f"Cleaned up {removed} old flows")
                
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Analysis worker error: {str(e)}")
                self.stats['analysis_errors'] += 1
    
    def analyze_packet(self, packet) -> Dict[str, Any]:
        """
        Phân tích gói tin
        
        Args:
            packet: Gói tin
            
        Returns:
            Dict: Kết quả phân tích
        """
        start_time = time.time()
        
        try:
            # Basic packet info
            packet_info = self.extract_packet_info(packet)
            
            # Update flow
            flow_info = self.flow_manager.update_flow(packet)
            
            # Extract features
            features = self.extract_features(packet, flow_info)
            
            # Combine results
            result = {
                'timestamp': time.time(),
                'packet_info': packet_info,
                'flow_info': flow_info,
                'features': features,
                'anomaly_score': 0,
                'is_anomaly': False
            }
            
            # Update statistics
            self.stats['packets_analyzed'] += 1
            self.stats['features_extracted'] += len(features)
            self.stats['processing_time'] += time.time() - start_time
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing packet: {str(e)}")
            self.stats['analysis_errors'] += 1
            return {}
    
    def extract_packet_info(self, packet) -> Dict[str, Any]:
        """
        Trích xuất thông tin cơ bản từ packet
        
        Args:
            packet: Gói tin
            
        Returns:
            Dict: Thông tin packet
        """
        info = {
            'timestamp': time.time(),
            'size': len(packet),
            'protocol': 'unknown',
            'src_ip': None,
            'dst_ip': None,
            'src_port': None,
            'dst_port': None,
            'flags': None,
            'payload_size': 0
        }
        
        try:
            # Layer 2 (Ethernet)
            if packet.haslayer(Ether):
                info['src_mac'] = packet[Ether].src
                info['dst_mac'] = packet[Ether].dst
            
            # Layer 3 (IP)
            if packet.haslayer(IP):
                info['src_ip'] = packet[IP].src
                info['dst_ip'] = packet[IP].dst
                info['ip_version'] = packet[IP].version
                info['ttl'] = packet[IP].ttl
                info['protocol'] = packet[IP].proto
                
                # Layer 4 (TCP/UDP)
                if packet.haslayer(TCP):
                    info['protocol'] = 'TCP'
                    info['src_port'] = packet[TCP].sport
                    info['dst_port'] = packet[TCP].dport
                    info['flags'] = packet[TCP].flags
                    info['seq'] = packet[TCP].seq
                    info['ack'] = packet[TCP].ack
                    info['window'] = packet[TCP].window
                    info['payload_size'] = len(packet[TCP].payload)
                
                elif packet.haslayer(UDP):
                    info['protocol'] = 'UDP'
                    info['src_port'] = packet[UDP].sport
                    info['dst_port'] = packet[UDP].dport
                    info['payload_size'] = len(packet[UDP].payload)
                
                elif packet.haslayer(ICMP):
                    info['protocol'] = 'ICMP'
                    info['icmp_type'] = packet[ICMP].type
                    info['icmp_code'] = packet[ICMP].code
            
            # IPv6
            elif packet.haslayer(IPv6):
                info['src_ip'] = packet[IPv6].src
                info['dst_ip'] = packet[IPv6].dst
                info['ip_version'] = 6
                info['protocol'] = packet[IPv6].nh
            
            # ARP
            elif packet.haslayer(ARP):
                info['protocol'] = 'ARP'
                info['src_ip'] = packet[ARP].psrc
                info['dst_ip'] = packet[ARP].pdst
                info['arp_op'] = packet[ARP].op
            
            # DNS
            if packet.haslayer(DNS):
                info['dns_query'] = packet[DNS].qd.qname.decode() if packet[DNS].qd else None
                info['dns_response'] = packet[DNS].an.rdata if packet[DNS].an else None
            
            # HTTP
            if packet.haslayer(HTTP):
                info['http_method'] = packet[HTTP].Method
                info['http_host'] = packet[HTTP].Host
                info['http_uri'] = packet[HTTP].Path
        
        except Exception as e:
            self.logger.error(f"Error extracting packet info: {str(e)}")
        
        return info
    
    def extract_features(self, packet, flow_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Trích xuất features cho ML
        
        Args:
            packet: Gói tin
            flow_info: Thông tin flow
            
        Returns:
            Dict: Features
        """
        features = {}
        
        try:
            # Packet-level features
            features['packet_size'] = len(packet)
            features['protocol_type'] = self._get_protocol_type(packet)
            features['is_tcp'] = 1 if packet.haslayer(TCP) else 0
            features['is_udp'] = 1 if packet.haslayer(UDP) else 0
            features['is_icmp'] = 1 if packet.haslayer(ICMP) else 0
            
            # Flow-level features
            if flow_info:
                features['flow_duration'] = flow_info.get('last_seen', 0) - flow_info.get('start_time', 0)
                features['flow_packet_count'] = flow_info.get('packet_count', 0)
                features['flow_byte_count'] = flow_info.get('byte_count', 0)
                features['flow_packets_per_second'] = (
                    features['flow_packet_count'] / max(features['flow_duration'], 1)
                )
                features['flow_bytes_per_second'] = (
                    features['flow_byte_count'] / max(features['flow_duration'], 1)
                )
                
                # Packet size statistics
                packet_sizes = flow_info.get('packet_sizes', [])
                if packet_sizes:
                    features['avg_packet_size'] = statistics.mean(packet_sizes)
                    features['max_packet_size'] = max(packet_sizes)
                    features['min_packet_size'] = min(packet_sizes)
                    if len(packet_sizes) > 1:
                        features['packet_size_std'] = statistics.stdev(packet_sizes)
                    else:
                        features['packet_size_std'] = 0
                
                # Inter-arrival time statistics
                inter_times = flow_info.get('inter_arrival_times', [])
                if inter_times:
                    features['avg_inter_arrival_time'] = statistics.mean(inter_times)
                    features['max_inter_arrival_time'] = max(inter_times)
                    features['min_inter_arrival_time'] = min(inter_times)
                    if len(inter_times) > 1:
                        features['inter_arrival_std'] = statistics.stdev(inter_times)
                    else:
                        features['inter_arrival_std'] = 0
            
            # Port-based features
            if packet.haslayer(TCP) or packet.haslayer(UDP):
                src_port = packet[TCP].sport if packet.haslayer(TCP) else packet[UDP].sport
                dst_port = packet[TCP].dport if packet.haslayer(TCP) else packet[UDP].dport
                
                features['src_port'] = src_port
                features['dst_port'] = dst_port
                features['is_well_known_port'] = 1 if (src_port < 1024 or dst_port < 1024) else 0
                features['is_ephemeral_port'] = 1 if (src_port > 32768 or dst_port > 32768) else 0
            
            # TCP-specific features
            if packet.haslayer(TCP):
                tcp_flags = packet[TCP].flags
                features['tcp_syn'] = 1 if tcp_flags & 0x02 else 0
                features['tcp_ack'] = 1 if tcp_flags & 0x10 else 0
                features['tcp_fin'] = 1 if tcp_flags & 0x01 else 0
                features['tcp_rst'] = 1 if tcp_flags & 0x04 else 0
                features['tcp_psh'] = 1 if tcp_flags & 0x08 else 0
                features['tcp_urg'] = 1 if tcp_flags & 0x20 else 0
                features['tcp_window'] = packet[TCP].window
            
            # Payload features
            if packet.haslayer(Raw):
                payload = packet[Raw].load
                features['payload_length'] = len(payload)
                features['payload_entropy'] = self._calculate_entropy(payload)
                features['payload_printable_ratio'] = self._calculate_printable_ratio(payload)
            else:
                features['payload_length'] = 0
                features['payload_entropy'] = 0
                features['payload_printable_ratio'] = 0
            
            # Time-based features
            current_time = time.time()
            features['hour_of_day'] = datetime.fromtimestamp(current_time).hour
            features['day_of_week'] = datetime.fromtimestamp(current_time).weekday()
            
        except Exception as e:
            self.logger.error(f"Error extracting features: {str(e)}")
        
        return features
    
    def _get_protocol_type(self, packet) -> int:
        """
        Lấy protocol type dưới dạng số
        
        Args:
            packet: Gói tin
            
        Returns:
            int: Protocol type
        """
        if packet.haslayer(TCP):
            return 6  # TCP
        elif packet.haslayer(UDP):
            return 17  # UDP
        elif packet.haslayer(ICMP):
            return 1  # ICMP
        else:
            return 0  # Other
    
    def _calculate_entropy(self, data: bytes) -> float:
        """
        Tính entropy của data
        
        Args:
            data: Dữ liệu
            
        Returns:
            float: Entropy value
        """
        if not data:
            return 0
        
        # Count byte frequencies
        frequencies = defaultdict(int)
        for byte in data:
            frequencies[byte] += 1
        
        # Calculate entropy
        entropy = 0
        data_len = len(data)
        for count in frequencies.values():
            if count > 0:
                probability = count / data_len
                entropy -= probability * np.log2(probability)
        
        return entropy
    
    def _calculate_printable_ratio(self, data: bytes) -> float:
        """
        Tính tỷ lệ ký tự printable trong data
        
        Args:
            data: Dữ liệu
            
        Returns:
            float: Tỷ lệ printable (0-1)
        """
        if not data:
            return 0
        
        printable_count = 0
        for byte in data:
            if 32 <= byte <= 126:  # ASCII printable range
                printable_count += 1
        
        return printable_count / len(data)
    
    def process_queue(self):
        """Xử lý packet queue"""
        processed = 0
        
        while not self.packet_queue.empty() and processed < 100:
            try:
                packet = self.packet_queue.get_nowait()
                result = self.analyze_packet(packet)
                
                if result:
                    self.analysis_results.put(result)
                
                processed += 1
                
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"Error processing packet from queue: {str(e)}")
    
    def get_analysis_results(self) -> List[Dict[str, Any]]:
        """
        Lấy kết quả phân tích
        
        Returns:
            List: Danh sách kết quả
        """
        results = []
        
        while not self.analysis_results.empty():
            try:
                result = self.analysis_results.get_nowait()
                results.append(result)
            except queue.Empty:
                break
        
        return results
    
    def analyze_pcap_file(self, pcap_file: str) -> Dict[str, Any]:
        """
        Phân tích file PCAP
        
        Args:
            pcap_file (str): Đường dẫn file PCAP
            
        Returns:
            Dict: Kết quả phân tích
        """
        if not SCAPY_AVAILABLE:
            return {'error': 'Scapy not available'}
        
        try:
            from scapy.utils import rdpcap
            
            # Đọc packets
            packets = rdpcap(pcap_file)
            
            # Phân tích từng packet
            results = []
            for packet in packets:
                result = self.analyze_packet(packet)
                if result:
                    results.append(result)
            
            # Tạo báo cáo tổng hợp
            report = {
                'file': pcap_file,
                'total_packets': len(packets),
                'analyzed_packets': len(results),
                'analysis_timestamp': time.time(),
                'summary': self._generate_summary(results),
                'flows': self.flow_manager.get_active_flows(),
                'statistics': self.get_statistics()
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"Error analyzing PCAP file: {str(e)}")
            return {'error': str(e)}
    
    def _generate_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Tạo summary từ kết quả phân tích
        
        Args:
            results: Danh sách kết quả
            
        Returns:
            Dict: Summary
        """
        summary = {
            'protocol_distribution': defaultdict(int),
            'port_distribution': defaultdict(int),
            'packet_size_stats': {},
            'flow_stats': {},
            'anomaly_count': 0
        }
        
        packet_sizes = []
        
        for result in results:
            packet_info = result.get('packet_info', {})
            features = result.get('features', {})
            
            # Protocol distribution
            protocol = packet_info.get('protocol', 'unknown')
            summary['protocol_distribution'][protocol] += 1
            
            # Port distribution
            src_port = packet_info.get('src_port')
            dst_port = packet_info.get('dst_port')
            if src_port:
                summary['port_distribution'][src_port] += 1
            if dst_port:
                summary['port_distribution'][dst_port] += 1
            
            # Packet sizes
            size = packet_info.get('size', 0)
            if size > 0:
                packet_sizes.append(size)
            
            # Anomalies
            if result.get('is_anomaly', False):
                summary['anomaly_count'] += 1
        
        # Packet size statistics
        if packet_sizes:
            summary['packet_size_stats'] = {
                'mean': statistics.mean(packet_sizes),
                'median': statistics.median(packet_sizes),
                'min': min(packet_sizes),
                'max': max(packet_sizes),
                'std': statistics.stdev(packet_sizes) if len(packet_sizes) > 1 else 0
            }
        
        # Flow statistics
        flows = self.flow_manager.get_active_flows()
        if flows:
            flow_durations = [f.get('last_seen', 0) - f.get('start_time', 0) for f in flows]
            flow_packet_counts = [f.get('packet_count', 0) for f in flows]
            
            summary['flow_stats'] = {
                'total_flows': len(flows),
                'avg_flow_duration': statistics.mean(flow_durations) if flow_durations else 0,
                'avg_packets_per_flow': statistics.mean(flow_packet_counts) if flow_packet_counts else 0
            }
        
        return dict(summary)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Lấy thống kê analyzer
        
        Returns:
            Dict: Thống kê
        """
        stats = self.stats.copy()
        
        # Tính toán thêm
        if stats['packets_analyzed'] > 0:
            stats['avg_processing_time'] = stats['processing_time'] / stats['packets_analyzed']
            stats['features_per_packet'] = stats['features_extracted'] / stats['packets_analyzed']
        
        return stats
